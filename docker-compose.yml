version: '3.8'
services:
  db:
    image: postgres:13.5
    container_name: postgres_container
    environment:
      POSTGRES_USER: app_user
      POSTGRES_PASSWORD: contraseña
      POSTGRES_DB: agentor-db
    volumes:
      - ./db_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: always

  pgadmin:
    image: dpage/pgadmin4
    container_name: pgadmin_container
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - "5050:80"
    depends_on:
      - db
    restart: always
    extra_hosts:
      - "host.docker.internal:host-gateway"