/**
 * Runs all test in file all_tests_imports.ts
 * 
 * To run the tests, place first on project root folder and then run
 * 
 * $ npm run tests
 * 
 * This command will regenerate the all_tests_imports.ts file and then run the tests.
 * 
 * It is equivalent to run two command lines:
 * 
 * $ ts-node tests/generate_all_tests_imports.ts
 * $ ts-node -r tsconfig-paths/register tests/runner.ts
 * 
 * TODO:
 * Conseguir que los ficheros de tests residan al lado del código real (Exluir los tests al ejecutar/compilar la aplicación )
 * 
 */
import { AppContext, setAppContext } from "../src/lib/AppContext";
import testsmodules from "./all_tests_imports";

const ctx = setAppContext(loadAppContext());

(async () => {
  let totals = { ok: 0, err: 0 };
  let start = new Date();
  try {
    ctx.logger.info("Running tests");
    for (const testsmodule of testsmodules) {
      let testresult = { ok: 0, err: 0 };
      ctx.logger.info(` ${testsmodule.path}`);
      for (const test of testsmodule.tests) {

        try {
          ctx.logger.info(`  ${test.name}`);
          await test.fn(ctx);
          testresult.ok++;
        } catch (e) {
          ctx.logger.error(`   ${test.name}`);
          ctx.logger.error(`   ${(e as Error).stack}`);
          testresult.err++;
        }
      }
      ctx.logger.info(`  ok: ${testresult.ok}  error: ${testresult.err}`);

      totals.ok += testresult.ok;
      totals.err += testresult.err;
    }
    ctx.logger.info("");
    ctx.logger.info(`All tests run.  ok: ${totals.ok}  error: ${totals.err}`);
    ctx.logger.info(`Time: ${(new Date().getTime() - start.getTime())} ms`)
    return totals.err === 0 ? 0 : 1;
  } catch (e) {
    ctx.logger.error("process ended with error", e);
    return 1;
  } finally {
    // Permitir que se liberen posibles promesas "en background" (Ej: envío de emails y demás)
    await sleep(1500);
  }
})().
  then((code) => {
    process.exit(code);
  });


function loadAppContext() {
  const config = require("../config/app.js");
  const argv = require('minimist')(process.argv.slice(2));

  // USADO en la generación de tokens (JWT).
  // Debe provenir de la configuración
  if (!process.env.ACCESS_TOKEN_SECRET) {
    process.env.ACCESS_TOKEN_SECRET = config.jwt.ACCESS_TOKEN_SECRET;
  }

  return new AppContext({
    db: config.db,
    storages: config.storages,
    geoloc: config.geoloc,
    email: config.email,
    stripe: config.stripe,
    tusolucionhipotecaria: config.tusolucionhipotecaria,
    crwindividuals_api: config.crwindividuals_api,
    crwidealista_api: config.crwidealista_api,
    crwhaya_api: config.crwhaya_api,
    crwservihabitat_api: config.crwservihabitat_api,
    crwsolvia_api: config.crwsolvia_api,
    crwunicaja_api: config.crwunicaja_api,
    crwportalnow_api: config.crwportalnow_api,
    crwaliseda_api: config.crwaliseda_api,
    crwuci_api: config.crwuci_api,
    messagesbroker: config.messagesbroker,
    logger: { level: argv.logger_level ?? 'info' }
  });
}

function sleep(ms: number) {
  return new Promise((resolve) => {
    setTimeout(resolve, ms);
  });
}