/**
 * Genera el fichero all_imports.ts en el que se importan todos los ficheros de test de esta carpeta y las carpetas hijas.
 * Los ficheros de tests son aquellos cuyo nombre de fichero acaba en "Test.ts" o "Tests.ts"
 *
 */

import { readdir, stat, writeFile, appendFile } from "fs/promises";
import path from "node:path";


const root = __dirname;
const outFile = `${root}/all_tests_imports.ts`;

console.log(`Generating ${outFile}`);

(
  async () => {
    const tests = await listAllTests(root);

    await writeFile(outFile, [
      `// `,
      `// This file has been generated by 'generate_all_imports.ts' script`,
      `// at ${new Date()}`,
      `// `,
      `// To regenerate it simply run`,
      `//   $ ts-node generate_all_tests_imports`,
      `\n`
    ].join("\n"));

    for (const [index, test] of tests.entries()) {
      await appendFile(outFile, `import t${index} from "${test.path.substring(0, test.path.length - 3)}";\n`);
    }
    await appendFile(outFile, "export default [\n");

    for (const [index, test] of tests.entries()) {
      await appendFile(outFile, `  {path:"${test.path}", ...t${index}},\n`);
    }

    await appendFile(outFile, "];\n");

  }
)();


type TestToImport = {
  name: string,
  path: string
}

async function listAllTests(dirPath: string): Promise<TestToImport[]> {
  let result: TestToImport[] = [];
  const dirEntries = await readdir(dirPath);
  for (const dirEntry of dirEntries) {
    const entryPath = path.join(dirPath, dirEntry);
    
    const entryState = await stat(entryPath);
    if (entryState.isDirectory()) {
      result = result.concat(await listAllTests(entryPath));
    } else if (entryState.isFile() && (dirEntry.endsWith("Test.ts") || dirEntry.endsWith("Tests.ts"))) {
      result.push({
        name: dirEntry,
        path: entryPath.replace(root,".")
      });
    }
  }
  return result;
}