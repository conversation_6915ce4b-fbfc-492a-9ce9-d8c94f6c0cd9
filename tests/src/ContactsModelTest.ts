import assert from 'assert';
import { assertEq } from './testcommon/asserts';
import { AppContext } from '../../src/lib/AppContext';
import { ContactsModel } from "../../src/model/contactsModel";

const agent_id = "1";
const
  c1Data = { email: "<EMAIL>", firstName: "Paco", lastName: "Pil", mobile: "*********", isBankServicer: true, },
  c2Data = { email: "<EMAIL>", firstName: "Paco2", lastName: "Pil2", mobile: "*********", isBankServicer: false, };

export default {
  name: "Contacts Model",
  tests: [
    {
      name: "Contact CRUD",
      fn: async ({ db }: AppContext) => {


        await db.withRdTransaction( async (dbTran) => {
          const pk = await ContactsModel.create({ agent: { id: agent_id }, ...c1Data })(dbTran);
          assert(pk);
          assert(pk.id);

          {
            const contact = await ContactsModel.read(agent_id, pk.id)(dbTran);
            assert(contact);
            assertEq(c1Data.email, contact.email);
            assertEq(c1Data.firstName, contact.firstName);
            assertEq(c1Data.lastName, contact.lastName);
            assertEq(c1Data.mobile, contact.mobile);
            assertEq(c1Data.isBankServicer, contact.isBankServicer);
          }
          
          {
            const nCount = await ContactsModel.remove(pk.id)(dbTran);
            assertEq(1, nCount, "Se han borrado 1 contacto");
          }
          
          {
            const contact = await ContactsModel.read(agent_id, pk.id)(dbTran);
            assertEq(undefined, contact, "Deleted contact shouldn't exist");
          }

        })();
      },
    },


  ],
};



