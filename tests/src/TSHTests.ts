import { AppContext } from '../../src/lib/AppContext';
import { PoolClient } from 'pg';
import { Db } from 'agentor-lib';
import { Tusolucionhipotecaria } from '../../src/lib/Tusolucionhipotecaria';
import assert from 'assert';


export default {
  name: "TSHTests",
  tests: [
    {
      name: "Decodifica un formulario Tally",
      fn: async (_: AppContext) => {
        const tally_survey_01_data = require('./assets/tally_survey_01_data.json');
        const survey = Tusolucionhipotecaria.obj2TallyFormResponse(tally_survey_01_data);

        assert(survey.eventId = "24ce5e9e-be41-475a-b955-c60d28eb9d49");
      },
    },

  ],
};


/**
 * Si todo va bien, genera un rollback al finalizar.
 * @param db 
 * @param f 
 */
async function readonlyTrans(db: Db, f: (dbTran: PoolClient) => Promise<void>) {
  const rfemtftr = "$Reserved$Fake$Error$$Message$To$Force$Transaction$Rollback$:-)$ASEREJÉ$."
  try {
    await db.withTransaction(async dbTran => {
      await f(dbTran);
      throw new Error(rfemtftr)
    })();
  } catch (e) {
    if (e instanceof Error && e.message !== rfemtftr) throw e;
  }

}
