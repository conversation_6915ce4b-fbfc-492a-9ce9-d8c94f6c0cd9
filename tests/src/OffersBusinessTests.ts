/**
 * Tests sobre OfferBusiness
 */
import { OfferversiontypeCode } from '../../src/model/common/dtos/offerversiontypesDTO';
import { OfferBusiness } from '../../src/business/offerBusiness';
import { OffersModel } from '../../src/model/offersModel';
import assert from 'assert';
import { AppContext } from '../../src/lib/AppContext';

export default {
  name: "Offers Business",
  tests: [
    {
      name: "Crear una oferta inmobiliaria en comercialización",
      fn: async ({ db }: AppContext) => {
        const agent_id = "1";

        const offerData = require("./assets/offer_01_no_customer_data.json");
        await db.withRdTransaction( async (dbTran) => {
          const offer = await OfferBusiness.create(agent_id, offerData)(dbTran);
          assert(offer);
          assert(typeof offer.id === "string", "Id is string")
          assert(typeof offer?.property?.id === "string", "Property should be created when Offer has been created")
          assert(typeof (offer.property?.location?.longitude) === 'number');
          assert(typeof (offer.property?.location?.longitude) === 'number');
          assert(offer.property?.attributes?.gasSupplyHas === true);
          assert(offer.property?.attributes?.totalSurfaceM2 === 1754);
        })();
      },
    },
    {
      name: "Cálculo de Habitaciones Totales",
      fn: async ({ db }: AppContext) => {
        const agent_id = "1";
        const offerData = require("./assets/offer_01_no_customer_data.json");
        const attrs = offerData.property.attributes;

        await db.withRdTransaction( async (dbTran) => {

          {
            offerData.property.attributes = {
              ...attrs, individualBedroomsCount: 3, doubleBedroomsCount: 2, suiteBedroomsCount: 1,
            };
            const offer = await OfferBusiness.create(agent_id, offerData)(dbTran);
            assert(offer.property?.attributes?.totalBedroomsCount === 6);
          }
          {
            offerData.property.attributes = {
              ...attrs, individualBedroomsCount: 3, doubleBedroomsCount: null, suiteBedroomsCount: 1,
            };
            const offer = await OfferBusiness.create(agent_id, offerData)(dbTran);
            assert(offer.property?.attributes?.totalBedroomsCount === 4);
          }
          {
            offerData.property.attributes = {
              ...attrs, individualBedroomsCount: null, doubleBedroomsCount: null, suiteBedroomsCount: null,
            };
            const offer = await OfferBusiness.create(agent_id, offerData)(dbTran);
            assert(offer.property?.attributes?.totalBedroomsCount === 0,
              "Si no se ha informado ningún tipo de dormitorio, la suma total será 0");

            offer.property!.attributes!.doubleBedroomsCount = 5;
            const updated = await OfferBusiness.update(agent_id, offer.id!, offer)(dbTran);
            assert(updated);
            assert(updated.property?.attributes?.totalBedroomsCount === 5);

          }
        })();
      },
    },
    {
      name: "Crear una versión para cuotas",
      fn: async ({ db }: AppContext) => {
        const agent_id = "1";
        const offerData = require("./assets/offer_01_no_customer_data.json");
        await db.withRdTransaction( async (dbTran) => {
          const offer = await OfferBusiness.create(agent_id, offerData)(dbTran);
          const versionOffer = await OfferBusiness.create(agent_id, {
            ...offer, id: undefined,
            version: {
              of: { id: offer.id },
              type: { code: OfferversiontypeCode.monthlypayment },
              disclaimer: "TEST DISCLAIMER"
            },
            sale: {
              ...offer.sale,
              monthlyPayment: 46210
            }
          })(dbTran);

          assert(versionOffer.id !== offer.id,
            "La oferta versionada y la oferta principal no pueden tener el mismo id");
          assert(typeof versionOffer.version?.of?.id === "string",
            "El identificador de la oferta versionada debe ser una string");
          assert(versionOffer.version?.of?.id === offer.id,
            "La oferta versionada debe ser la indicada al crear");
          assert(versionOffer.version?.type?.code === OfferversiontypeCode.monthlypayment,
            "El tipo de versión debe ser el indicado al crear.");
          assert(versionOffer.version?.disclaimer === "TEST DISCLAIMER",
            "El disclaimer de la versión debe ser el indicado al crear.");
          assert(versionOffer.sale?.monthlyPayment === "46210",
            "Montly payment es string (todas las cuantías se leen como string)")


          // Verificar filtro "isVersion" sobre ofertas que son version
          assert(0 === await OffersModel.count({ isVersion: false, agent_id, id: versionOffer.id })(dbTran),
            "Las versiones no deben contabilizarse con el filtro isVersion:false");
          assert(1 === await OffersModel.count({ isVersion: true, agent_id, id: versionOffer.id })(dbTran),
            "Las versiones no deben contabilizarse con el filtro isVersion:true");
          // Verificar filtro "isVersion" sobre ofertas principales
          assert(1 === await OffersModel.count({ isVersion: false, agent_id, id: offer.id })(dbTran),
            "Las ofertas principales deben contabilizarse con el filtro isVersion:false");
          assert(0 === await OffersModel.count({ isVersion: true, agent_id, id: offer.id })(dbTran),
            "Las ofertas principales no deben contabilizarse con el filtro isVersion:true");
        })();
      },
    },


  ],
};

