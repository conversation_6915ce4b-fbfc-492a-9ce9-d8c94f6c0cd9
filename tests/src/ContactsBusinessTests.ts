/**
 * Tests sobre ContactBusiness
 */

import assert from 'assert';
import { assertThrown, assertNotThrown, assertEq, assertTrue } from './testcommon/asserts';

import { AppContext } from '../../src/lib/AppContext';
import { ContactBusiness } from '../../src/business/contactBusiness';

const agent_id = "1";

export default {
  name: "Contacts Business",
  tests: [
    {
      name: "Crear una contacto",
      fn: async ({ db }: AppContext) => {

        await db.withRdTransaction(async (dbTran) => {
          const contact = await ContactBusiness.create(agent_id, {
            email: "<EMAIL>",
            firstName: "Paco",
            mobile: "*********",
            isBankServicer: true
          })(dbTran);

          assertEq(agent_id, contact.agent?.id,
            "El contacto debe pertenecer al agente indicado al crear");
          assertEq("<EMAIL>", contact.email,
            "email debe ser el indicado al crear");
          assertEq("Paco", contact.firstName,
            "firstName debe ser el indicado al crear");
          assertEq("*********", contact.mobile,
            "mobile debe ser el indicado al crear");
          assertTrue(contact.isBankServicer,
            "El contacto creado debe ser Bank Servicer");
        })();
      },
    },
    {
      name: "email is unique",
      fn: async ({ db }: AppContext) => {

        await db.withRdTransaction(async (dbTran) => {
          await ContactBusiness.create(agent_id, { email: "<EMAIL>", firstName: "Paco", mobile: "*********", isBankServicer: true })(dbTran);
          await assertThrown(Error,
            () => ContactBusiness.create(agent_id, { email: "<EMAIL>", firstName: "Juan", mobile: "*********", isBankServicer: true })(dbTran),
            "Contact email must be unique");

        })();
      },
    },
    {
      name: "Mobile is not unique",
      fn: async ({ db }: AppContext) => {
        await db.withRdTransaction(async (dbTran) => {

          await ContactBusiness.create(agent_id, { email: "<EMAIL>", firstName: "Paco", mobile: "*********", isBankServicer: true })(dbTran);
          await assertNotThrown(
            () => ContactBusiness.create(agent_id, { email: "<EMAIL>", firstName: "Juan", mobile: "*********", isBankServicer: false })(dbTran),
            "Contact mobile is not unique");

        })();
      },
    },


  ],
};

