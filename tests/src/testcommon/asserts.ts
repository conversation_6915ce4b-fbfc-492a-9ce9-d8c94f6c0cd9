import { AssertionError } from "assert";

/**
 * Code must raise an exception of type T
 * @remarks If assertThrown is called into a "read only transaction" you must ensure
 * it is the last call into a test, because transaction will not accept any other 
 * sql but rollback
 * @param e The expected exception (i.e.  Error)
 * @param f The function to check (that must raise the Error)
 * @param message The text of the assertion.
 * @returns A promise of nothing (Promise<void> )
 * @example
 * 
 * await assertThrown(BusinessError, ()=>{  throwBusinessError("This is an error")  }, "Code must raise a BusinessError");
 */
export async function assertThrown<T>(e: { new(): T }, f: () => Promise<any | void>, message?: string) {
  try { await f(); } catch (err) {
    if (err instanceof e) return;
    throw new AssertionError({ message: message ?? `expected ${e} but ${(err as Error).name} found` })
  }
  throw new AssertionError({ message: message ?? `expected ${e.name} but no error was raised` });
}
/**
 * Code doesn't raise any exception
 * Remarks:  If code raises an exception and you are into a transaction, be aware you finish the test 
 * because database connection will not accept any other command but rollback.
 * @param f 
 * @param message 
 */
export async function assertNotThrown(f: () => Promise<any | void>, message?: string) {
  try { await f(); } catch (err) {
    throw new AssertionError({ message: message ?? `Expected no error, but ${(err as Error).name} whas raised` })
  }
}

/**
 * Asserts 2 values are equals
 * @param expected The expected value
 * @param actual The "tested" value
 * @param message 
 */
export function assertEq(expected: any, actual: any, message?: string): void {
  if (expected !== actual) {
    throw new AssertionError({ actual, expected, message: `expected ${expected} actual ${actual}, ${message}` });
  }
}

export function assertTrue(actual: any, message?: string): void {
  if (true !== actual) {
    throw new AssertionError({ actual, expected: true, message: `expected ${true} actual ${actual}, ${message}` });
  }
}

export function assertFalse(actual: any, message?: string): void {
  if (false !== actual) {
    throw new AssertionError({ actual, expected: false, message: `expected ${false} actual ${actual}, ${message}` });
  }
}

export function assertNullish(actual: any, message?: string): void {
  if (actual !== null && actual !== void 0) {
    throw new AssertionError({ actual, message: `expected null or undefined, but ${actual} found, ${message}` });
  }
}
export function assertNotNullish(actual: any, message?: string): void {
  if (actual === null && actual === void 0) {
    throw new AssertionError({ actual, message: `expected not null nor undefined, but ${actual} found, ${message}` });
  }
}
export function assertIn(expected: any, actual: any[], message?: string): void {
  if (!actual.includes(expected)) {
    throw new AssertionError({ actual, expected, message: `expected ${expected} to be included in ${actual}, ${message}` });
  }
}
export function assertNotIn(expected: any, actual: any[], message?: string): void {
  if (actual.includes(expected)) {
    throw new AssertionError({ actual, expected, message: `expected ${expected} to be not included in ${actual}, ${message}` });
  }
}
