import assert from 'assert';

import { ActionBusiness } from '../../src/business/actionBusiness';
import { ActionsModel } from "../../src/model/actionsModel";
import { ContactBusiness } from '../../src/business/contactBusiness';
import { AppContext } from '../../src/lib/AppContext';
import { OfferBusiness } from '../../src/business/offerBusiness';
import { assertEq, assertFalse, assertNotNullish, assertTrue } from './testcommon/asserts';


export default {
  name: "Actions Business",
  tests: [
    {
      name: "Solicitud de más información de un inmueble bancario",
      fn: async ({ db }: AppContext) => {
        // TODO: MOCK del post para verificar los datos posteados a TSH
        // TODO: MOCK del smtp para verificar los contenidos enviados por email
        const agent_id = "1";
        const offerData = require("./assets/offer_01_no_customer_data.json");

        await db.withRdTransaction(async (dbTran) => {
          const bankservicer = await ContactBusiness.create(agent_id, { email: "<EMAIL>", firstName: "Paco", mobile: "*********", isBankServicer: true })(dbTran);
          const offer = await OfferBusiness.create(agent_id, { ...offerData, customer: bankservicer })(dbTran);

          const action = await ActionBusiness.createByOperator(
            agent_id,
            {
              // Solicitud de más información
              type: { id: "JQ1KiMy9hap8sTGhFBOJqA" },
              // Peeter norton
              contact: bankservicer,
              // Una oferta de un cliente bancario
              offer,
              // 
              description: "Una descripción $ax\n",
            }
          )(dbTran);
          assertEq("object", typeof action,
            "Result must be an object");
          assertEq("string", typeof action.id,
            "Id must be an string");
          assertEq(offer.id, action.offer?.id,
            "Offer must be the specified");
          assertEq("JQ1KiMy9hap8sTGhFBOJqA", action.type?.id,
            "Type must be the specified");
          assertNotNullish(action.when,
            "when must be generated if not specified");
          assertEq("Una descripción $ax\n", action.description,
            "Description must be the specified");
        })();
      },
    },
    {
      name: "Solicitud de más información de una version",
      fn: async ({ db }: AppContext) => {
        const agent_id = "1";
        const offerData = require("./assets/offer_01_no_customer_data.json");
        const offerVersionData = require("./assets/offer_02_verison_cuotas_data.json");
        const tally_survey_01_data = require('./assets/tally_survey_01_data.json');

        await db.withRdTransaction(async (dbTran) => {
          const
            offer =
              await OfferBusiness.create(agent_id, offerData)(dbTran),
            offerVersion =
              await OfferBusiness.create(agent_id, { ...offerVersionData, version: { ...offerVersionData.version, of: { id: offer.id } } })(dbTran),
            contact =
              await ContactBusiness.create(agent_id, { email: "<EMAIL>", firstName: "Paco", mobile: "*********" })(dbTran);

          //
          // La acción sobre una versión por cuotas no debe constar como "hecha" tras su creación
          //
          const action = await ActionBusiness.createByOperator(agent_id, { type: { id: "JQ1KiMy9hap8sTGhFBOJqA" }, when: new Date(), contact, offer: offerVersion, description: "solicito + información" })(dbTran);
          assertFalse(action.done,
            "Las solicitudes de + información sobre versiones de tipo cuota no se dan por hechas");

          //
          // Registramos una encuesta posterior a la solicitud de + info
          //
          const survey = await ActionBusiness.createByOperator(agent_id, { type: { id: "1qhC2ZU4wRzT7AO9OtPsjA" }, when: new Date(), contact, offer: offerVersion, description: "encuesta", extradata: tally_survey_01_data })(dbTran);
          assertNotNullish(survey,
            "La acción debe existir");
          assertTrue(survey.done,
            "La acción asociada al formulario se da por hecha");
          assertEq("24ce5e9e-be41-475a-b955-c60d28eb9d49", survey.extradata?.eventId,
            "La acción debe incluir, como extradata, el formulario de tally");

          //
          // Tras registrar la encuesta, la acción original (solicitud de + info) debe constar como hecha
          //
          const [actionRd] = await ActionsModel.list({ id: action.id })(dbTran);
          assertNotNullish(actionRd,
            "La acción debe existir");
          assertTrue(actionRd.done,
            "La acción de solicitud de + info debe darse por notoficada tras procesar el formulario de tally");
        })();
      },
    },


  ],
};



