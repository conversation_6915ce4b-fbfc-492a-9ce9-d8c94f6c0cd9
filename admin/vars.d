module admin.vars;

import admin.lib.tools: SshHost;

const c_installer_dir = "installer";
const c_public_dir = "public";
const c_publicwebapp_dir = c_public_dir ~ "/webapp";
const c_agentorapp_dir = "../agentorapp";
const c_agentorweb_dir = c_agentorapp_dir ~ "/build/web";

const c_ssh_pem_file = "~/.ssh/topbrokers20210922.pem";
const c_ssh_user = "ubuntu";


const SshHost[] c_ssh_hosts = [
  {"agentor01", c_ssh_pem_file, c_ssh_user, "localhost", 9005, "app"},
  {"cron02", c_ssh_pem_file, c_ssh_user, "localhost", 9006, "cron"}
];
