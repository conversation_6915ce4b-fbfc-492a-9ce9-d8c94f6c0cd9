#!/usr/bin/env rdmd
/++
 + <PERSON>ript implementado en lenguaje d
 * Debes tener instalado rdmd en tu sistema (acompaña a dmd)
 + $ sudo apt install dmd
 + o bien
 + $ sudo snap install dmd --classic
 +
 + This script has to be called from the root folder of the project:
 +     rdmd admin/pack.d
 +/
import std.stdio : writeln;
import std.format : format;
import std.file : exists, copy, mkdirRecurse, rmdirRecurse;
import std.algorithm : each, map;
import admin.lib.tools : cleanupDir, copyDir, exec, generateZip;

void main()
{
  import admin.vars : c_installer_dir, c_public_dir, c_publicwebapp_dir,
    c_agentorapp_dir, c_agentorweb_dir;

  try
  {
    auto releasePath = c_installer_dir ~ "/release";
    auto packadgeName = generatePackadgeName();
    auto zipFileName = packadgeName ~ ".7z";

    log("--------------------------------------");
    log("    Compile and generate installer    ");
    log("--------------------------------------");
    log("");

    log("Cleaning up installer folder");
    cleanupDir(c_installer_dir);

    log("agentorapp");
    log("----------");
    log("agentorapp: Building Flutter Application");
    buildFlutterApplication(c_agentorapp_dir);
    log("agentorapp: Importing Flutter Application");
    copyDir(c_agentorweb_dir, c_publicwebapp_dir, true);

    ["agentor-lib"].each!((localLibName) {
      string localLibPath = "../" ~ localLibName;
      log(localLibName ~ ": compiling");
      // Obtener referencias (modo devel) y compilar.  
      runNpmInstall(localLibPath, false);
      runNpmBuild(localLibPath);
      // Copiar a la carpeta installer/release
      log(localLibName ~ ": copying to installer folder");
      mkdirRecurse(
        releasePath ~ "/" ~ localLibName
      );
      copyDir(
        localLibPath ~ "/lib",
        releasePath ~ "/" ~ localLibName
      );
      copy(
        localLibPath ~ "/package.json",
        releasePath ~ "/" ~ localLibName ~ "/package.json"
      );
    });

    log("agentor: compiling");
    runNpmInstall("./", false);
    runNpmBuild();

    log("agentor: copying to installer folder");
    mkdirRecurse(releasePath ~ "/agentor");
    copyDir(
      "build",
      releasePath ~ "/agentor",
      true
    );
    copy(
      "package.json",
      releasePath ~ "/agentor/package.json"
    );
    copy(
      "tsconfig.json",
      releasePath ~ "/agentor/tsconfig.json"
    );

    log("agentor conf: copying to installer folder");
    mkdirRecurse(releasePath ~ "/config");
    copy(
      "config/app.js", 
      releasePath ~ "/config/app.js" );
    copyDir(
      "config/production",
      releasePath ~ "/config"
    );
    // Otros "assets"
    log("agentor assets: copying to installer folder");
    copyDir(
      "admin/assets",
      releasePath
    );
    // Generamos zip con los 4 elementos
    log("Generating zip file");
    generateZip(
      releasePath,
      c_installer_dir ~ "/" ~ zipFileName
    );
    // Removing temporal folder
    log("Removing temporal folder");
    rmdirRecurse(releasePath);

    log("Generating sh file");
    generateShFile(format!"%s/%s_noservice.sh"(c_installer_dir, packadgeName), zipFileName,  packadgeName, false);
    generateShFile(format!"%s/%s.sh"(c_installer_dir, packadgeName), zipFileName,  packadgeName, true);
    //generateShFile(format!"%s/%s.sh"(c_installer_dir, packadgeName), zipFileName, configZipFileName, packadgeName);

    writeln("Installer generated Ok");
  }
  catch (Exception e)
  {
    writeln("Installer has not been generated");
  }
  finally
  {
    writeln("");
  }
}

private:

string generatePackadgeName()
{
  import std.datetime.systime : Clock;

  const now = Clock.currTime();
  return format!"agentorwebapp%04d%02d%02d%02d%02d"(now.year, now.month,
    now.day, now.hour, now.minute);
}

void generateShFile(string shFilePath, string zipFileName, string packadgeName, bool installService = true)
{
  import std.array : join;
  import std.stdio : File;

  const c_srv_name = "agentorwebapp";

  scope (failure)
    writeln("Something was wrong generating sh file");
  File(shFilePath, "w").write([
    format!"if ! node --version 2> /dev/null | grep -icq \"v16.\"; then"(),
    format!"  echo \"Node v16 is not installed\""(),
    format!"  exit 1"(),
    format!"elif ! which 7z >/dev/null; then"(),
    format!"  echo \"7z is not installed\""(),
    format!"  exit 1"(),
    format!"elif [ -d %s ]; then"(packadgeName),
    format!"  echo \"Alredy installed\""(),
    format!"else"(),
    format!"  restoresrv=0"(),
    format!"  restorecron=0"(),
    format!"  7z x %s -o%s"(zipFileName, packadgeName),
    format!"  echo \"Installing opencv requeriments\""(),
    format!"  sudo apt install ffmpeg libsm6 libxext6  -y"(),
    ["agentor-lib", "agentor"].map!((project) =>
        [
          format!""(),
          format!"  # install %s dependencies"(project),
          format!"  cd %s/%s"(packadgeName, project),
          format!"  npm install --production"(),
          format!"  if [ -d py_src ]; then"(),
          format!"    cd py_src"(), 
          format!"    pip3 install -r requeriments.txt"(),
          format!"    cd .."(),
          format!"  fi"(),
          format!"  cd ../.."(),
        ].join("\n")
    ).join("\n"),
    format!""(),
    format!"  if sudo systemctl status %s.service 2> /dev/null | grep -q -wi running; then"(c_srv_name),
    format!"    echo \"stopping service %s\""(c_srv_name),
    format!"    sudo service %s stop"(c_srv_name),
    format!"    restoresrv=1"(),
    format!"  fi"(),
    format!"  if sudo systemctl status cron.service 2> /dev/null | grep -q -wi running; then"(),
    format!"    echo \"stopping service %s\""("cron"),
    format!"    sudo service cron stop"(),
    format!"    restorecron=1"(),
    format!"  fi"(),
    format!"  # Redirigimos el link simbólico a la nueva versión"(),
    format!"  ln -fsn %s %s"(packadgeName, c_srv_name),
    format!"  # Restauramos los servicios que se pararon"(),
    format!"  if [ $restorecron -eq 1 ]"(),
    format!"  then"(),
    format!"    echo \"restarting cron\""(),
    format!"    sudo service cron start"(),
    format!"  fi"(),
    installService ? [
      format!"  if ! sudo systemctl list-units 2> /dev/null | grep -q -wic \"%s.service\"; then"(c_srv_name),
      format!"    # Si el servicio no está instalado "(),
      format!"    echo \"Installing %s service\""(c_srv_name),
      format!"    sudo cp %s/assets/etc/systemd/system/%s.service /etc/systemd/system"(
        c_srv_name, c_srv_name),
      format!"    sudo systemctl enable %s.service"(c_srv_name),
      format!"    sudo service %s start"(c_srv_name),
      format!"  elif ! cmp -s \"%s/assets/etc/systemd/system/%s.service\" \"/etc/systemd/system/%s.service\"; then"(
        c_srv_name, c_srv_name, c_srv_name),
      format!"    # Si el servicio está instalado, pero ha cambiado "(),
      format!"    echo \"Re-installing %s service\""(c_srv_name),
      format!"    sudo cp %s/assets/etc/systemd/system/%s.service /etc/systemd/system"(c_srv_name, c_srv_name),
      format!"    sudo systemctl daemon-reload"(),
      format!"  fi"()
    ].join("\n"): "",
    format!"  if [ $restoresrv -eq 1 ]"(),
    format!"  then"(),
    format!"    # Si el servicio estaba iniciado anteriormente, lo iniciamos"(),
    format!"    echo \"restarting %s service\""(c_srv_name),
    format!"    sudo service %s start"(c_srv_name),
    format!"  fi"(),
    format!"  if [ ! -f /etc/logrotate.d/agentor ]; then "(),
    format!"    sudo cp %s/assets/etc/logrotate.d/agentor /etc/logrotate.d"(c_srv_name),
    format!"  fi"(),
    format!"fi"()
  ].join("\n"));
  format!"chmod +x \"%s\""(shFilePath).exec();

}


void buildFlutterApplication(string flutterAppPath)
in (flutterAppPath.exists, "flutter app folder doesn't exist")
{
  import std.file : chdir, getcwd;

  scope (failure)
    writeln("Problems building flutter application");

  auto actualDir = getcwd();
  scope (exit)
    chdir(actualDir);

  chdir(flutterAppPath);
  exec("flutter build web --release");
}

void runNpmBuild(string folderPath = "./")
in (folderPath.exists, format!"%s folder doesn't exist"(folderPath))
{
  import std.file : chdir, getcwd;

  scope (failure)
    format!"Problems building %s"(folderPath.exists);

  auto actualDir = getcwd();
  scope (exit)
    chdir(actualDir);

  chdir(folderPath);
  exec("npm run build");
}

void runNpmInstall(string folderPath = "./", bool production = true)
in (folderPath.exists, format!"%s folder doesn't exist"(folderPath))
{
  import std.file : chdir, getcwd;

  scope (failure)
    format!"Problems running npm install %s"(folderPath.exists);

  auto actualDir = getcwd();
  scope (exit)
    chdir(actualDir);

  chdir(folderPath);
  exec("npm install" ~ (production ? " --production" : ""));
}

void log(string txt)
{
  writeln("pack.d> " ~ txt);
}
