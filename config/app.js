const { env } = require("process");
const fs = require("fs");

const environment = (env.NODE_ENV === "production") ? "production" : "devel";

const config = {
  http: importJsonFile(`${__dirname}/${environment}/http.json`),
  db: importJsonFile(`${__dirname}/${environment}/db.json`),
  session: importJsonFile(`${__dirname}/${environment}/session.json`),
  jwt: importJsonFile(`${__dirname}/${environment}/jwt.json`),
  storages: importJsonFile(`${__dirname}/${environment}/storages.json`),
  geoloc: importJsonFile(`${__dirname}/${environment}/geoloc.json`),
  email: importJsonFile(`${__dirname}/${environment}/email.json`),
  stripe: importJsonFile(`${__dirname}/${environment}/stripe.json`),
  tusolucionhipotecaria: importJsonFile(`${__dirname}/${environment}/tusolucionhipotecaria.json`),
  crwidealista_api: importJsonFile(`${__dirname}/${environment}/crwidealista_api.json`),
  crwhaya_api:  importJsonFile(`${__dirname}/${environment}/crwhaya_api.json`),
  crwservihabitat_api:  importJsonFile(`${__dirname}/${environment}/crwservihabitat_api.json`),
  crwsolvia_api:  importJsonFile(`${__dirname}/${environment}/crwsolvia_api.json`),
  crwindividuals_api:  importJsonFile(`${__dirname}/${environment}/crwindividuals_api.json`),
  crwunicaja_api:  importJsonFile(`${__dirname}/${environment}/crwunicaja_api.json`),
  crwportalnow_api:  importJsonFile(`${__dirname}/${environment}/crwportalnow_api.json`),
  crwaliseda_api:  importJsonFile(`${__dirname}/${environment}/crwaliseda_api.json`),
  crwuci_api:  importJsonFile(`${__dirname}/${environment}/crwuci_api.json`),
  adevinta_api: importJsonFile(`${__dirname}/${environment}/adevinta_api.json`),
  pisoscom_api: importJsonFile(`${__dirname}/${environment}/pisoscom_api.json`),
  pisoscom_offer_mapper: importJsonFile(`${__dirname}/${environment}/pisoscom_offer_mapper.json`),
  messagesbroker: importJsonFile(`${__dirname}/${environment}/messagesbroker.json`),
  csite: importJsonFile(`${__dirname}/${environment}/csite.json`)
};

module.exports = config;

function importJsonFile(path) {
  return JSON.parse(fs.readFileSync(path));
}