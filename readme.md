# Installation

# Installation

## Postgres

Install extension **unnacent**:

```sql
create extension if not exists unaccent;
```

This extension is available in Amazon RDS and standard Postgress installation.

Remarks: It is automatically created by schema change scripts (```101.sql```)

# Ejecución código compilado

La aplicación no hace un uso standard de los pathsa para realizar "import" con el fin de evitar el infierno de pahts relativos ("../../../")

El compilador **tsc** funciona sin problemas (en **tsconfig.json** encontrarás la entrada **"baseUrl"** que indica el punto raíz del código fuente)

El problema está al ejecutar con **ts-node** o con **node**, ya que el compilador **no genera los pathds relativos en los ficheros js** (deja los originales).

#### Ejecución con ts-node:

```shell
ts-node -r tsconfig-paths/register src/tasks.ts --sync_crwidealista
```

```shell
ts-node -r tsconfig-paths/register src/index.ts
```

#### Ejecución con node:

Hemos creado un javascript que no depende de tsconfig.json y que encontraras en la carpeta ```basecontent/js-paths-bootstrap```. Este script se copia a la carpeta "build" en el proceso de compilación (npm run build)

Para usarlo

```shell
node -r ./js-paths-bootstrap.js index.js
node -r ./js-paths-bootstrap.js tasks.js --sync_crwidealista
```


## Postgres

Install extension **unnacent**:

```sql
create extension if not exists unaccent;
```

This extension is available in Amazon RDS and standard Postgress installation.

Remarks: It is automatically created by schema change scripts (```101.sql```)

# Ejecución código compilado

La aplicación no hace un uso standard de los pathsa para realizar "import" con el fin de evitar el infierno de pahts relativos ("../../../")

El compilador **tsc** funciona sin problemas (en **tsconfig.json** encontrarás la entrada **"baseUrl"** que indica el punto raíz del código fuente)

El problema está al ejecutar con **ts-node** o con **node**, ya que el compilador **no genera los pathds relativos en los ficheros js** (deja los originales).

#### Ejecución con ts-node:

```shell
ts-node -r tsconfig-paths/register src/tasks.ts --sync_crwidealista
```

```shell
ts-node -r tsconfig-paths/register src/index.ts
```

#### Ejecución con node:

Hemos creado un javascript equivalente a tsconfig-paths/register, pero que no depende  tsconfig.json:  ```js-paths-bootstrap.js``` y que encontraras en la carpeta ```basecontent```. Este script se copia a la carpeta "build" en el proceso de compilación (```npm run build```)

Para usarlo

```shell
node -r ./js-paths-bootstrap.js index.js
node -r ./js-paths-bootstrap.js tasks.js --sync_crwidealista
```


# Deploy

Ejecutar los scripts desde la carpeta del proyecto: (tienes que tener instalado el compilador de D)

admin/pack.d && admin/deploy.d