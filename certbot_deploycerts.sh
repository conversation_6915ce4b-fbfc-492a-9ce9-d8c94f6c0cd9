#!/bin/bash

#sudo certbot renew

haproxy_certs_folder=/etc/haproxy/certs

if [ -d $haproxy_certs_folder ]; then
: # Do nothing
else
  echo Creating $haproxy_certs_folder
  mkdir $haproxy_certs_folder
fi

echo Combining letscript files into one
# Combine two files into one in one line
DOMAIN='topbrokers.io' sudo -E bash -c 'cat /etc/letsencrypt/live/$DOMAIN/fullchain.pem /etc/letsencrypt/live/$DOMAIN/privkey.pem > /etc/haproxy/certs/$DOMAIN.pem'
DOMAIN='www.topbrokers.io' sudo -E bash -c 'cat /etc/letsencrypt/live/$DOMAIN/fullchain.pem /etc/letsencrypt/live/$DOMAIN/privkey.pem > /etc/haproxy/certs/$DOMAIN.pem'

