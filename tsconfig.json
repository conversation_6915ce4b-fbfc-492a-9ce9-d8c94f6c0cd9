{"compilerOptions": {"target": "es2019", "lib": ["es2019"], "module": "commonjs", "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "resolveJsonModule": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "typeRoots": ["./typings", "./node_modules/@types"], "outDir": "build", "baseUrl": "./src", "paths": {"src/*": ["src/*"]}, "strict": true, "sourceMap": true, "strictNullChecks": true}, "exclude": ["node_modules", "public", "src/tests/**"], "include": ["src/**/*", "src/**/*.json"]}