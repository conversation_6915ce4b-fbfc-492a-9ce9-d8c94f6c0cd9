const { env } = require('process');
const _config = {
	devel: {
		http: {
			port: 8000,
		},
		db: {
			host: 'localhost',
			//database: 'agentor-db',
			database: 'agentor-db',
			user: 'app_user',
			//password: "qzcooa4wj72w5b1qmttd4zoux4ro",
			password: 'ubuntu',
			//port: 6432,
			port: 5432,
			max: 10,
			min: 0,
		},
		session: {
			name: 'agentorses',
			secret:
				'3J54rZGE8FUOF9BaGnZQFAkuv1LHY2Xr3ynFwlH50HhAZXLYsrnRAupmLxwXhMea3Q',
			httpOnly: true,
			maxAge: 7 * 24 * 3600,
		},
		jwt: {
			ACCESS_TOKEN_SECRET:
				'AS7_+D801ss234ÑIZXJCVKQ4ÑRKJa9as$d809172-34iaksctjxxxxa',
		},
		storage: {
			bucket: 'ew1-agentor-dev-storage',
			credentials: {
				accessKeyId: '********************',
				secretAccessKey: 'd8PvKxOwWLvJkAokdSP/xl+Y4o4sjPMLZbZiv7Jd',
			},
		},
	},
	production: {
		http: {
			port: 8000,
		},
		db: {
			host: 'db01.cpf8u964hg6k.eu-west-1.rds.amazonaws.com',
			database: 'agentor-db',
			user: 'app_user',
			password: 'qzcooa4wj72w5b1qmttd4zoux4ro',
			port: 5432,
			max: 10,
			min: 0,
		},
		session: {
			name: 'agentorses',
			secret:
				'3J54rZGE8FUOF9BaGnZQFAkuv1LHY2Xr3ynFwlH50HhAZXLYsrnRAupmLxwXhMea3Q',
			httpOnly: true,
			maxAge: 7 * 24 * 3600,
		},
		jwt: {
			ACCESS_TOKEN_SECRET:
				'AS7_+D801ss234ÑIZXJCVKQ4ÑRKJa9as$d809172-34iaksctjxxxxa',
		},
		storage: {
			bucket: 'ew1-agentor-storage',
			credentials: {
				accessKeyId: '********************',
				secretAccessKey: 'd8PvKxOwWLvJkAokdSP/xl+Y4o4sjPMLZbZiv7Jd',
			},
		},
	},
};

const config =
	env.NODE_ENV === 'production' ? _config.production : _config.devel;

module.exports = config;
