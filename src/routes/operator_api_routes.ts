import { OperatorApi } from "controllers/operator-api/OperatorApi";
import express from "express";


export const operatorApiRoutes = express.
  Router({ mergeParams: true }).
  use(express.json({})).
  get(
    "/agents", OperatorApi.AgentsCtr.getAgentsAct).
  get(
    "/agents/:agent_id/contacts", OperatorApi.ContactsCtrl.getContactsAct).
  post(
    "/agents/:agent_id/contacts", OperatorApi.ContactsCtrl.postContactAct).
  get(
    "/agents/:agent_id/contacts/:contact_id", OperatorApi.ContactsCtrl.getContactAct).
  get(
    "/agents/:agent_id/actions", OperatorApi.ActionsCtrl.getActions).
  post(
    "/agents/:agent_id/actions", OperatorApi.ActionsCtrl.postAction).
  get(
    "/agents/:agent_id/offers", OperatorApi.OffersCtrl.getAgentOffersAct).
  get(
    "/agents/:agent_id/offers/:offer_id", OperatorApi.OffersCtrl.getAgentOfferAct).
  post(
    "/agents/:agent_id/offers", OperatorApi.OffersCtrl.postOfferAct).
  put(
    "/agents/:agent_id/offers/:offer_id", OperatorApi.OffersCtrl.putOfferAct).
  post(
    "/agents/:agent_id/offers/:offer_id/publications", OperatorApi.OffersCtrl.publishOfferAct).
  delete(
    "/agents/:agent_id/offers/:offer_id/publications/:workgroup_id", OperatorApi.OffersCtrl.unpublishOfferAct).
  get(
    "/agents/:agent_id/offers/:offer_id/property/medias", OperatorApi.OffersCtrl.listPropertyMediasAct)
  
  ;