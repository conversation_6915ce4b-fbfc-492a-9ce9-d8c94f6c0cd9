import { deleteActionAct, getActionAct, getActionsAct, postActionAct, putActionAct } from "controllers/api/ActionsCtrl";
import { getActiontypesAct } from "controllers/api/ActiontypesCtrl";
import { getAgentAct, getMyAgentAct, postAgentAct, postPasswordAct, postPasswordValidationcodeAct, postPreSignUp, putAgentAct, putMyAgentAct } from "controllers/api/AgentsCtrl";
import { CitiesCtrl } from "controllers/api/CitiesCtrl";
import { Offers_CloudVwCtrl } from "controllers/api/CloudOffersCtrl";
import { getContactAct, getContactsAct, postContactAct, putContactAct } from "controllers/api/ContactsCtrl";
import { DemandsCtrl } from "controllers/api/DemandsCtrl";
import { getMyEcomAccountAct } from "controllers/api/EcomAccountsCtrl";
import { EcomPurchasesCtrl } from "controllers/api/EcomPurchasesCtrl";
import { MatchingsCtrl } from "controllers/api/MatchingsCtrl";
import { OffersCtrl } from "controllers/api/OffersCtrl";
import { PropertytypesCtrl } from "controllers/api/PropertytypesCtrl";
import { PropertyzonesCtrl } from "controllers/api/PropertyzonesCtrl";
import { createTokenAct, renewTokenAct } from "controllers/api/SessionsCtrl";
import { getStreettypesAct } from "controllers/api/StreettypesCtrl";
import { getOfferversiontypesListAct } from "controllers/api/OfferversiontypesCtrl";
import { StripeCtrl } from "controllers/api/StripeCtrl";
import { SupportrequestsCtrl } from "controllers/api/SupportrequestsCtrl";
import { WorkgroupMembersCtrl } from "controllers/api/WorkgroupMemebersCtrl ";
import { WorkgroupsCtrl } from "controllers/api/WorkgroupsCtrl";
import { WaSrvCtrl } from "controllers/api/WASrvCtrl";
import express from "express";
import { ScrtyUsersCtrl } from "controllers/api/ScrtyUsersCtrl";

export const appApiRoutes = express.
  Router({ mergeParams: true }).
  use(express.json({})).
  use("/wasrv", express.Router({ mergeParams: true }).
    post(
      "/tokens", WaSrvCtrl.createToken)
  ).
  post(
    "/tokens", createTokenAct).
  use("/users", express.Router({ mergeParams: true }).
    get(
      "/me", ScrtyUsersCtrl.getMyUserAct)
  ).
  put(
    "/tokens/actual", renewTokenAct).
  get(
    "/cities", CitiesCtrl.listAct).
  get(
    "/offerversiontypes", getOfferversiontypesListAct).
  get(
    "/propertyzones", PropertyzonesCtrl.listAct).
  get(
    "/propertytypes", PropertytypesCtrl.getPropertytypesAct).
  get(
    "/propertyzones/:id/path", PropertyzonesCtrl.getPathAct).
  use("/agents",
    express.Router({ mergeParams: true }).

      get(
        "/me", getMyAgentAct).
      put(
        "/me", putMyAgentAct).
      get(
        "/me/contacts", getContactsAct).
      get(
        "/me/contacts/:contact_id", getContactAct).
      post(
        "/me/contacts", postContactAct).
      put(
        "/me/contacts/:contact_id", putContactAct).
      post(
        "/me/offers", OffersCtrl.createAct).
      get(
        "/me/offers", OffersCtrl.listAct).
      get(
        "/me/cloud_offers", Offers_CloudVwCtrl.listAct).
      post(
        "/me/favourite_offers", OffersCtrl.addOfferToFavourites).
      delete(
        "/me/favourite_offers/:offer_id", OffersCtrl.delOfferFromFavourites).
      get(
        "/me/offers/:offer_id", OffersCtrl.getAct).
      get(
        "/me/offers/:offer_id/is_favourite", OffersCtrl.getIsFavourite).
      put(
        "/me/offers/:offer_id", OffersCtrl.updateAct).
      post(
        "/me/offers/:offer_id/publications", OffersCtrl.publishOffer).
      get(
        "/me/offers/:offer_id/publications", OffersCtrl.listOfferPublications).
      delete(
        "/me/offers/:offer_id/publications/:workgroup_id", OffersCtrl.unpublishOffer).
      post(
        "/me/offers/:offer_id/property/medias", OffersCtrl.createPropertyMediaAct).
      delete(
        "/me/offers/:offer_id/property/medias/:media_key", OffersCtrl.deletePropertyMediaAct).
      put(
        "/me/offers/:offer_id/property/medias/:media_key", OffersCtrl.updatePropertyMediaAct).
      get(
        "/me/offers/:offer_id/property/medias", OffersCtrl.listPropertyMediasAct).
      get(
        "/me/affiliations", WorkgroupMembersCtrl.listAgentAffilitationsAct).
      get(
        "/me/matchings", MatchingsCtrl.listAct).
      post(
        "/me/demands", DemandsCtrl.createAct).
      get(
        "/me/demands", DemandsCtrl.listAct).
      get(
        "/me/demands/:demand_id", DemandsCtrl.readAct).
      put(
        "/me/demands/:demand_id", DemandsCtrl.updateAct).
      get(
        // DEPRECATED: maintained only to avoid errors in not updated applications
        "/me/opportunities", (req, res, next) => res.send([])).
      get(
        "/me/actiontypes", getActiontypesAct).
      get(
        "/me/streettypes", getStreettypesAct).
      get(
        "/me/actions", getActionsAct).
      get(
        "/me/actions/:action_id", getActionAct).
      put(
        "/me/actions/:action_id", putActionAct).
      delete(
        "/me/actions/:action_id", deleteActionAct).
      post(
        "/me/actions", postActionAct).
      get(
        "/me/my_ecom_account", getMyEcomAccountAct).
      get(
        "/me/stripe/conf", StripeCtrl.getStripeConfAct).
      post(
        "/me/stripe/checkout_session", StripeCtrl.createCheckoutSessionAct).
      post(
        "/me/supportrequests", SupportrequestsCtrl.postAct).
      get(
        "/me/ecom/purchases", EcomPurchasesCtrl.listAct).
      //  Api asociada a Agentes por Identificador
      put(
        "/:agent_id", putAgentAct).
      get(
        "/:agent_id", getAgentAct).
      post(
        "/", postAgentAct)
  ).
  post(
    "/agents_validationcodes", postPreSignUp).
  post(
    "/passwords", postPasswordAct).
  post(
    "/passwords_validationcodes", postPasswordValidationcodeAct).
  // Deprecated: use passwords_validationcodes instead
  post(
    "/presignups", postPreSignUp).
  // Deprecated: use passwords_validationcodes instead
  post(
    "/passwords/validationcodes", postPasswordValidationcodeAct).
  // Workgroups
  get(
    "/workgroups", WorkgroupsCtrl.listAct)
  ;