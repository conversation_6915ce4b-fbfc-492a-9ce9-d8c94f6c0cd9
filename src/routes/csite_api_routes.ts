import express from "express";
import { ContactsiteApi } from "controllers/csite-api/ContactsiteApi";


export const contactsiteApiRoutes = express.
  Router({ mergeParams: true }).
  use(express.json({})).
  get(
    "/:site_slug", ContactsiteApi.SitesCtrl.getSiteAct).
  get(
    "/:site_slug/opportunities", ContactsiteApi.OpportunitiesCtrl.getOpportunitiesAct).
  get(
    "/:site_slug/opportunities/:opportunity_id", ContactsiteApi.OpportunitiesCtrl.getOpportunityAct).
  put(
    "/:site_slug/opportunities/:opportunity_id/reaction", ContactsiteApi.OpportunitiesCtrl.putOpportunityReactionAct).
  delete(
    "/:site_slug/opportunities/:opportunity_id/reaction", ContactsiteApi.OpportunitiesCtrl.deleteOpportunityReactionAct).
  post(
      "/:site_slug/opportunities/:opportunity_id/requests", ContactsiteApi.RequestsCtrl.postRequestAct)
  ;