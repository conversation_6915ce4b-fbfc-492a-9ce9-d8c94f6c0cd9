
const session = require("cookie-session");

import { stripe_webhooks } from "./controllers/stripe/WebhookCtrl";
import express from "express";
import { AppContext, setAppContext } from "lib/AppContext";
import { appApiRoutes } from "./routes/app_api_routes";
import { operatorApiRoutes } from "routes/operator_api_routes";
import { contactsiteApiRoutes } from "routes/csite_api_routes";

const argv = require('minimist')(process.argv.slice(2));


export const createAppRoutes = (config:any) =>
  express.
    Router({ mergeParams: true }).
    use(express.static('public/webapp')).
    use(express.static('public/web')).
    use(session(config.session)).
    post("/stripe/webhooks", express.raw({ type: "application/json" }), stripe_webhooks.postEventAct).
    // @deprecated Esperar a siguiente versión cuando la APP de flutter haya empezado a usar /app-api
    use("/api", appApiRoutes).
    use("/app-api", appApiRoutes).
    use("/operator-api", operatorApiRoutes).
    use("/csite-api", contactsiteApiRoutes);

export const createAppContext = (config:any) =>
  new AppContext({
    db: config.db,
    storages: config.storages,
    geoloc: config.geoloc,
    email: config.email,
    stripe: config.stripe,
    tusolucionhipotecaria: config.tusolucionhipotecaria,
    crwidealista_api: config.crwidealista_api,
    crwhaya_api: config.crwhaya_api,
    crwservihabitat_api: config.crwservihabitat_api,
    crwsolvia_api: config.crwsolvia_api,
    crwindividuals_api: config.crwindividuals_api,
    crwunicaja_api: config.crwunicaja_api,
    crwportalnow_api: config.crwportalnow_api,
    crwaliseda_api: config.crwaliseda_api,
    crwuci_api: config.crwuci_api,
    csite: config.csite,
    messagesbroker: config.messagesbroker,
    logger: { level: argv.logger_level ?? 'info' }
  });