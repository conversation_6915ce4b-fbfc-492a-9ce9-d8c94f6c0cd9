import { StringUtils } from "agentor-lib";
import { RestError } from "controllers/lib/RestError";
import { Request } from "express";
import { TypedJsonUtils } from "lib/TypedJsonUtils";

import { Serializable } from "typedjson";
export namespace CtrlParseUtils {

  export function parseParams<T>(rootType: Serializable<T>): (req: Request) => T {
    return (req: Request) => parse_imp(rootType)(req.params, "Request params");
  }

  export function parseQuery<T>(rootType: Serializable<T>): (req: Request) => T {
    return (req: Request) => parse_imp(rootType)(req.query, "Request query");
  }

  export function parseBody<T>(rootType: Serializable<T>): (req: Request) => T {
    return (req: Request) => parse_imp(rootType)(req.body, "Request body");
  }

  export function deserializeInt(s: any): number | null {
    if (isFinite(s))
      return s as number;
    else if (typeof s === "string") {
      s = s.trim();
      if (s === "")
        return null;
      else
        if (Number.isInteger(Number.parseFloat(s)))
          return Number.parseInt(s);
        else
          throw new TypeError("Not a valid integer")

    } else if (s === null || s === void 0) {
      return null;
    } else {
      throw new TypeError("Not a valid integer")
    }
  }

  export function deserializeBoolean(s: any): boolean | null {
    if (s === null || s === undefined)
      return null;
    else if (typeof s === "boolean")
      return s;
    else if (typeof s === "string") {
      s=s.trim();
      if(s==="")
        return null;
      else
        return StringUtils.parseBool(s);
    } else
      throw new TypeError("Not a valid boolean");

  }

  function parse_imp<T>(rootType: Serializable<T>): (o: any, msg: string) => T {
    return (o: any, msg: string) => {
      try { return TypedJsonUtils.parse<T>(o, rootType); } catch (e) {
        if (e instanceof TypeError)
          throw RestError.badRequest({ message: `Invalid ${msg}.  ${(e as TypeError).message}` });
        else throw e;
      }
    };
  }
}