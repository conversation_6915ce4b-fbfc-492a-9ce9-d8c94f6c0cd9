/**
 * Utilidades para controladores que deben trabajar usando API key
 */

import { scrtyApikeysModel } from "model/scrtyApikeysModel";
import { WrappedF, withRequiredHeader } from "./ctrlUtils";
import { scrtyUserModel } from "model/scrtyUserModel";
import { ScrtyUserDTO } from "model/common/dtos/ScrtyUserDTO";
import { Request } from "express";
import { RestError } from "./RestError";

export declare type WithApiUserOptions = {
  // El usuario debe estar asociado a un agente
  agent_is_required?: boolean,

};
/**
 * Revisa el access token correspondiente a una API basada en ApiKey y deduce el usuario asociado.
 * Si la Apikey no existe se devuelve un error 503
 * @returns 
 */
export function withApiKeyUser<T>(options?: WithApiUserOptions): ((req: Request, fUser: WrappedF<ScrtyUserDTO, T>) => Promise<T>) {
  const TXT_unknown_user = options?.agent_is_required ?  "Unknown Api Agent User" : "Unknown Api User";
  
  return (req, fAgent: WrappedF<ScrtyUserDTO, T>) =>
    withRequiredHeader<T>("Topbrokers-Api-Key")(req, async (token) => {
      let [apikey] = await scrtyApikeysModel.list({ token: token, includeDeleted: false })();
      let [user] = apikey?.user?.id ?
        await scrtyUserModel.list({ id: apikey.user.id })() :
        [];

      if (user === void 0  || (options?.agent_is_required && !user.agent?.id) )
        throw new RestError({ status: 503, message: TXT_unknown_user });
      else
        return fAgent(user);
    });
}