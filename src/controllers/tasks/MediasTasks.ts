import { appContext } from "lib/AppContext";
import { AscOrDesc } from "model/common/AscOrDesc";
import { MediaDTO } from "model/common/dtos/MediaDTO";
import { MediasModel } from "model/mediasModel";

export namespace MediasTasks {
  /**
   * Elimina los registros de la tabla "medias" que no están siendo usados por ningún inmueble.
   * Elimina los ficheros de s3 asociados a los registros de "medias" borrados
   * @param ctx 
   */
  export async function removeUnusedMedias(): Promise<void> {
    const { logger } = appContext();

    logger.info("");
    logger.info("START removeUnusedMedias: Removing unused medias");
    try {
      for await (const unusedMedia of enumerateUnusedMedias())
        await removeMedia(unusedMedia);
    } finally {
      logger.info("END removeUnusedMedias");
    }

    async function* enumerateUnusedMedias() {
      const PAGE_SIZE = 100;
      var unusedMedias = await MediasModel.list({ unused: true, pagination: { limit: PAGE_SIZE }, orderby: { key: AscOrDesc.asc } })();
      while (unusedMedias.length !== 0) {
        for (const unusedMedia of unusedMedias)
          yield unusedMedia;
        const last = unusedMedias[unusedMedias.length - 1];
        unusedMedias = await MediasModel.list({ key_bg: last.key, unused: true, pagination: { limit: PAGE_SIZE }, orderby: { key: AscOrDesc.asc } })();
      }
    }
  }

  async function removeMedia(media: MediaDTO): Promise<void> {
    const { storage, logger } = appContext();
    const { key, folder } = media;

    if (folder !== void 0 && key !== void 0) {
      logger.info(`Removing unused media key:${folder}`);
      await storage.removeMediaFiles(folder);
      await MediasModel.remove(key)();
    }
  }
}