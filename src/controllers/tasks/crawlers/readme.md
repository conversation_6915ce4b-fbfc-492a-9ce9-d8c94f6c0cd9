Importación de zonas y anuncios de la base de datos del crawler idealista.

Importación de zonas y anuncios de la base de datos del crawler idealista.

##### zonas

tabla **zones**

campos

* **id**:uuid
* **name**: string
* **parent_id**: uuid

###### anuncios

tabla **ads**

campos

* **id**: uuid  -- Identificador único del anuncio
* **refinedcontent_data**: json -- Estructura de datos refinada (compatible con topbrokers)
* **refinedcontent_when**: datetime -- Fecha en la que se ha generado el contenido refinado (pude usarse para comparar versiones)

h1. zonas
