import { CrwZoneStruct } from "external/crwcommon-api/CrwZoneStruct";
import { appContext } from "lib/AppContext";
import { PropertyzonesModel } from "model/propertyzonesModel";

type TreeZoneStruct = PropertyzonesModel.TreeZoneStruct;
export namespace CrwZonesTasks {

  export async function importZonesFromCrwIdealista(): Promise<void> {
    const { crwidealista_api, db, logger } = appContext();

    logger.info("");
    logger.info("START importZones:  Import Zones from CrwIdealista");
    try {

      const [idealistaZonesRoots, localZonesRoots] = await Promise.all([
        crwidealista_api.getZonesAsTree(),
        PropertyzonesModel.getZonesAsTree()()
      ]);

      const newZones = _listNewZones(idealistaZonesRoots, localZonesRoots);
      logger.info(`creating ${newZones.length} zones`);
      await db.withTransaction(dbTran => PropertyzonesModel.createN(dbTran, newZones))();

      const changedZones = _listChangedZones(idealistaZonesRoots, localZonesRoots);
      logger.info(`updating ${changedZones.length} zones`);
      await db.withTransaction(dbTran => PropertyzonesModel.updateN(dbTran, changedZones))();

      const deletedIds = _listDeletedZonesIds(idealistaZonesRoots, localZonesRoots);
      logger.info(`removing ${deletedIds.length} zones`);
      await db.withTransaction(dbTran => PropertyzonesModel.removeN(dbTran, deletedIds))();

    } finally {
      logger.info("END importZones");
    }

  }

  function _listNewZones(remoteZonesLevel: CrwZoneStruct[], localZonesLevel: TreeZoneStruct[]): TreeZoneStruct[] {
    const newZones = remoteZonesLevel.reduce((all, rz) => {
      const lz = localZonesLevel.find(lz => lz.id === rz.id);
      if (lz === void 0)
        return all.concat([rz, ..._listNewZones(rz.children, [])]);
      else
        return all.concat(_listNewZones(rz.children, lz.children));

    }, [] as TreeZoneStruct[]);
    return newZones;
  }


  function _listChangedZones(remoteZonesLevel: TreeZoneStruct[], localZonesLevel: TreeZoneStruct[]): TreeZoneStruct[] {
    const changedZones = remoteZonesLevel.reduce((all, rz) => {
      const lz = localZonesLevel.find(lz => lz.id === rz.id);
      if (lz === void 0)
        return all;
      else if (lz.name !== rz.name)
        return all.concat([rz, ..._listChangedZones(rz.children, lz.children)]);
      else
        return all.concat(_listChangedZones(rz.children, lz.children));
    }, [] as TreeZoneStruct[]);
    return changedZones;
  }

  function _listDeletedZonesIds(remoteZonesLevel: TreeZoneStruct[], localZonesLevel: TreeZoneStruct[]): string[] {
    // Genera una lista con el contenido del arbol ordenando de hojas a raíz para hacer más eficiente posibles procesos de borrados basados en estos nodos.
    function treeToIdList(nodes: TreeZoneStruct[]): string[] {
      return nodes.flatMap(node => ([...treeToIdList(node.children), node.id]))
    }
    const localIds = treeToIdList(localZonesLevel);
    const remoteIds = treeToIdList(remoteZonesLevel);

    return localIds.reduce((all, lid) => {
      const rid = remoteIds.find(rid => lid === rid);
      if (rid)
        return all;
      else
        return all.concat([lid]);
    }, [] as string[]);
  }

}
