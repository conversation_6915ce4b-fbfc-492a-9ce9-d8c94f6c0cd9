import { InmofactoryAPI } from "external/adevinta/InmofactoryAPI";
import { adevinta } from "external/adevinta/PropertyStruct";
import { appContext } from "lib/AppContext";
import { toNumber } from "lodash";
import { MediaDTO } from "model/common/dtos/MediaDTO";
import { OfferDTO } from "model/common/dtos/OfferDTO";
import { OfferversiontypeCode } from "model/common/dtos/offerversiontypesDTO";
import { AirConditioningCode, ConservationStatusCode, ConsumptionLevelCode, EmissionLevelCode, EnergyCertificateCode, ExternalJoineryCode, FloorCode, GroundCode, HeatingCode, NoneOwnCommunity, OrientationCode, PropertyStatusCode } from "model/common/dtos/PropertyAttributesDTO";
import { PropertytypeCode } from "model/common/dtos/PropertytypeDTO";
import { PropertymediasModel } from "model/propertymediasModel";
import { workgroupOffersModel } from "model/workgroupsOffersModel";
import { jsonMember, jsonObject } from "typedjson";
import { InmofactoryOfferConsts } from "./InmofactoryOfferConsts";


export class InmofactoryOfferMapper {
  private availablePortals: adevinta.PublicationPortalStruct[];
  private options: InmofactoryOfferMapper.Options;

  constructor(options: InmofactoryOfferMapper.Options, availablePortals: adevinta.PublicationPortalStruct[]) {
    this.options = options;
    this.availablePortals = availablePortals
  }


  /* */
  public async mapOffer(offer: OfferDTO): Promise<adevinta.PropertyStruct> {
    if (offer.id === void 0)
      throw new Error("offer id is mandatory!!!");
    if ((offer.agent?.id ?? void 0) === void 0)
      throw new Error("offer agent is mandatory!!!");

    const medias = await PropertymediasModel.list({ offer_id: offer.id })().
      then(propertymedias => {
        const propertyimages = propertymedias.filter(pm => pm.media?.original?.mediatype?.startsWith("image"));
        return [
          // Primero las marcadas como favoritas
          ...propertyimages.filter(pm => pm.isFavourite ?? false),
          // Las marcadas como NO favoritas
          ...propertyimages.filter(pm => !(pm.isFavourite ?? false))
        ].map(pm => pm.media) as MediaDTO[];
      });

    // identificadores de los workgroups que afectan a Adevinta/Inmofactory y que emplearemos para decidir a qué portales se publica
    const offerWorkgroupIds = await readOfferWorkgroupsIds(offer.id);
    // Portales disponibles en adevinta
    const agentOptions = offer.agent!.email!.endsWith("@topbrokers.io") ? this.options.topbrokers_agent_options : this.options.nontopbrokers_agent_options;
    return {
      ExternalId: offer.id as string,
      AgencyReference: offer.id as string,
      TypeId: toTypeId(offer) ?? throwError("No se puede determinar TypeId"),
      SubTypeId: toSubtypeId(offer),
      IsNewConstruction: offer.property?.attributes?.statusCode === PropertyStatusCode.new,
      PropertyStatusId: adevinta.DIC_PropertyStatus.Disponible,
      ShowSurface: true,
      ContactTypeId: adevinta.DIC_PropertyContactType.Especificado,
      ContactName: toContactName(offer) ?? throwError("No se puede determinar el nombre de contacto (del agente)"),
      // OJO: No tenemos nada para determinar si es promoción
      IsPromotion: false,
      PropertyAddress: [{
        ZipCode: toAddressZipCode(offer),
        CountryId: toAddressCountry(offer) ?? throwMissing("CountryId"),
        Zone: offer.property?.zone?.name ?? void 0,
        StreetTypeId: toAddressStreetType(offer) ?? throwMissing("StreetTypeId"),
        Street: offer.property?.address?.streetname ?? void 0,
        Number: offer.property?.address?.number ?? void 0,
        FloorId: toAddressFloorId(offer),
        VisibilityModeId: adevinta.DIC_VisibilityMode.Mostrar_Sólo_zona,
        x: offer.property?.location?.longitude ?? throwMissing("PropertyAddress.x"),
        y: offer.property?.location?.latitude ?? throwMissing("PropertyAddress.y"),
      }],
      PropertyDocument: toPropertyDocuments(medias ?? [], this.options.useWatermark),
      PropertyFeature: ifValue(offer.property?.attributes)(attributes => adevinta.buildFeatures(
        {
          Superficie: attributes.totalSurfaceM2 ?? throwMissing("PropertyFeature.Superficie"),
        },
        {          
          DescripcionBreve: [offer.version?.disclaimer, offer.description].filter( a=> !!a).join("\n"),
          DescripcionExtendida: [offer.version?.disclaimer, offer.description].filter( a=> a!==void 0 && a!==null && a!=="").join("\n"),          
          OcultarSuperficie: false,
          NumHabitaciones: attributes.totalBedroomsCount ?? void 0,
          NumBaños: attributes.bathroomsCount ?? void 0,
          NumAseos: attributes.toiletsCount ?? void 0,
          ...offer.property?.type?.code === "house" ? {
            Ascensor_CASA: attributes.elevatorHas ?? void 0
          } :
            offer.property?.type?.code === "flat" ? {
              Ascensor: attributes.elevatorHas ?? void 0,
            } :
              {},
          Parking: ifValue(attributes.parkingPlacesCount)(count => count > 0),
          Trastero: attributes.storageRoomHas ?? void 0,
          Piscina: ifValue(attributes.swimmingPoolCode)(code => code !== NoneOwnCommunity.none),
          Terraza: attributes.terraceHas ?? void 0,
          Orientacion: toOrientacion(offer),
          Amueblado: attributes.furnishedIs ?? void 0,
          Superficie_solar: attributes.totalSurfaceM2 ?? void 0,
          AñoConstrucción: attributes.constructionYear ?? void 0,
          Conservación: toConservacion(offer),
          AireAcondicionado: toAireAcondicionaldo(offer),
          Lavadero: attributes.buddleHas ?? void 0,
          Suite_con_baño: ifValue(attributes.suiteBedroomsCount)(count => count > 0),
          Parquet: ifValue(attributes.groundCodes)(codes => codes.includes(GroundCode.parquet)),
          Puerta_blindada: attributes.reinforcedDoorHas ?? void 0,
          Gres_Cerámica: ifValue(attributes.groundCodes)(codes => codes.includes(GroundCode.gres)),
          Calefacción: ifValue(attributes.heatingCode)(code => code !== HeatingCode.none),
          Balcones: attributes.balconyHas ?? void 0,
          Jardín_privado: ifValue(attributes.gardenCode)(code => code === NoneOwnCommunity.own),
          Piscina_comunitaria: ifValue(attributes.swimmingPoolCode)(code => code === NoneOwnCommunity.community),
          Zona_comunitaria: ifValue(attributes.gardenCode)(code => code === NoneOwnCommunity.community) ?? ifValue(attributes.swimmingPoolCode)(code => code === NoneOwnCommunity.community),
          No_amueblado: ifValue(attributes.furnishedIs)(furnished => !furnished),
          Escala_eficiencia_consumo: toEscalaEficienciaConsumo(offer),
          Escala_eficiencia_emisiones: toEscalaEficienciaEmisiones(offer),
          Certificado_energético: toCertificadoEnergetico(offer)
        },
        {
          SuperficieUtil: ifValue(attributes.usefulSurfaceM2)(m2 => `${m2}`),
          Calefacción: ifValue(attributes.heatingCode)(code => code !== HeatingCode.none),
          Núm_habitaciones_dobles: attributes.doubleBedroomsCount ?? void 0,
          Núm_habitaciones_individuales: attributes.individualBedroomsCount ?? void 0,
          Núm_suites: attributes.suiteBedroomsCount ?? void 0,
          Núm_plantas: ifValue(offer.property?.type?.code === PropertytypeCode.flat ? attributes.buildingFloorsCount : void 0)(count => count),
          Carpintería_exterior: toCarpinteriaExterior(offer),
          Suelos: toSuelos(offer),
          Agua: attributes.waterSupplyHas ?? void 0,
          Gas: attributes.gasSupplyHas ?? void 0,
          Refrigeración: toRefrigeración(offer),
          Sol: ifValue(attributes?.sunnyIs)(sunny => sunny ? adevinta.DIC_Sol.Soleado : void 0),
          Alarma: attributes?.alarmSystemHas ?? void 0,
          Calefacción_opt: toCalefacciónOpt(offer)
        }
      )) ?? throwError("Impossible: a property without attributes!!!"),
      PropertyContactInfo: [
        {
          TypeId: adevinta.DIC_PropertyContactInfoType.Email,
          Value: agentOptions?.email ?? offer.agent?.email ?? throwMissing("PropertyContactInfo.Email"),
          ValueTypeId: adevinta.DIC_PropertyContactInfoValueType.Otros_datos, // .Agente_del_inmueble
        },
        ...offer.agent?.mobile ? [{
          TypeId: adevinta.DIC_PropertyContactInfoType.Movil,
          // "696 908 934"
          Value: agentOptions?.phoneNumber ?? offer.agent?.mobile ?? throwMissing("PropertyContactInfo.mobile"),
          ValueTypeId: adevinta.DIC_PropertyContactInfoValueType.Otros_datos, //Agente_del_inmueble
        }] : [],
        ...offer.agent?.mobile ? [{
          TypeId: adevinta.DIC_PropertyContactInfoType.Telefono,
          // "696 908 934"
          Value: agentOptions?.phoneNumber ?? offer.agent?.mobile ?? throwMissing("PropertyContactInfo.mobile"),
          ValueTypeId: adevinta.DIC_PropertyContactInfoValueType.Otros_datos, //Agente_del_inmueble
        }] : []
      ],
      PropertyTransaction: emptyToUndefined(mapOfferTransactions(offer)) ?? throwError("Imposible: an offer without sale/rent information"),
      PropertyPublications: emptyToUndefined(getDefaultPublications(this.availablePortals)) ?? throwError("Imposible: no workgroups"),
    };
  }
}
export namespace InmofactoryOfferMapper {


  @jsonObject()
  export class ByAgentOptions {
    @jsonMember(String)
    phoneNumber?: string
    @jsonMember(String)
    email?: string
  }
  @jsonObject
  export class Options {
    /** Usar las imágenes con watermark (default=true) */
    @jsonMember(Boolean, { isRequired: false })
    useWatermark: boolean = true;
    /** Opciones que aplicamos cuando el agente publicador es @topbrokers.io */
    @jsonMember(ByAgentOptions, { isRequired: false })
    topbrokers_agent_options?: ByAgentOptions;
    /** Opciones que aplicamos cuando el agente publicador NO es @topbrokers.io */
    @jsonMember(ByAgentOptions, { isRequired: false })
    nontopbrokers_agent_options?: ByAgentOptions;
  }
}
function readOfferWorkgroupsIds(offer_id: string): Promise<string[]> {
  return workgroupOffersModel.list({ offer_id })().then(
    workgroups => workgroups.
      filter(wgo => wgo.member?.can?.publish).
      map(wgo => wgo.member?.workgroup?.id).
      filter(id => id ? InmofactoryOfferConsts.C_WG_IDS.includes(id) : false) as string[]
  );
}
function toPropertyDocuments(medias: MediaDTO[], useWatermark: boolean = true): adevinta.PropertyDocument[] {
  return medias.
    filter(media => ["image/jpeg", "image/jpg", "image/png"].includes(media?.original?.mediatype ?? "")).
    map(media => useWatermark ? media?.publishing : media?.publishingNoWm).
    filter(mediaVersion => !!mediaVersion).
    map((mediaVersion, idx) => (
      {
        FileTypeId: (mediaType => {
          switch (mediaType) {
            case "image/jpeg":
              return adevinta.DIC_FileType.Documento_JPEG;
            case "image/jpg":
              return adevinta.DIC_FileType.Documento_JPG;
            case "image/png":
              return adevinta.DIC_FileType.Documento_PNG;
            default:
              return throwError(`FiletypeId (${mediaType})`);
          }
        })(mediaVersion!.mediatype ?? throwMissing("mediatype")),
        SortingId: idx,
        TypeId: adevinta.DIC_DocumentType.Foto,
        Url: mediaVersion!.url ?? throwMissing("Url"),
        Visible: true,
      } as adevinta.PropertyDocument
    ));
}
function toTypeId(offer: OfferDTO) {
  switch (offer.property?.type?.code) {
    case PropertytypeCode.flat: return adevinta.DIC_BuildingType.Piso;
    case PropertytypeCode.house: return adevinta.DIC_BuildingType.Casa;
    default: return void 0;
  }
}
function toSubtypeId(offer: OfferDTO): adevinta.DIC_Piso_Subtype | adevinta.DIC_Casa_Subtype | undefined {
  switch (offer.property?.subtype?.code) {

    case "1": //	"flat"	"{""default"":""Semisótano""}"
      return adevinta.DIC_Piso_Subtype.Semisotano;
    case "2": //	"flat"	"{""default"":""Triplex""}"
      return adevinta.DIC_Piso_Subtype.Triplex;
    case "3": //	"flat"	"{""default"":""Dúplex""}"
      return adevinta.DIC_Piso_Subtype.Duplex;
    case "4": //	"flat"	"{""default"":""Buhardilla""}"
      return adevinta.DIC_Piso_Subtype.Buhardilla;
    case "5": //	"flat"	"{""default"":""Ático""}"
      return adevinta.DIC_Piso_Subtype.Ático;
    case "6": //	"flat"	"{""default"":""Estudio""}"
      return adevinta.DIC_Piso_Subtype.Estudio;;
    case "7": //	"flat"	"{""default"":""Loft""}"
      return adevinta.DIC_Piso_Subtype.Loft;
    case "8": //	"flat"	"{""default"":""Otro""}"
      return adevinta.DIC_Piso_Subtype.Otro;
    case "9": //	"flat"	"{""default"":""Piso""}"
      return adevinta.DIC_Piso_Subtype.Piso;
    case "10": //	"flat"	"{""default"":""Apartamento""}"
      return adevinta.DIC_Piso_Subtype.Apartamento;
    case "11": //	"flat"	"{""default"":""Planta baja""}"
      return adevinta.DIC_Piso_Subtype.Planta_baja;
    case "13": //	"house"	"{""default"":""Casa""}"
      return adevinta.DIC_Casa_Subtype.Casa;
    case "14": //	"house"	"{""default"":""Cortijo""}"
      return adevinta.DIC_Casa_Subtype.Cortijo;
    case "17": //	"house"	"{""default"":""Adosada""}"
      return adevinta.DIC_Casa_Subtype.Adosada;
    case "18": //	"house"	"{""default"":""Caserio""}"
      return adevinta.DIC_Casa_Subtype.Caserio;
    case "19": //	"house"	"{""default"":""Pareada""}"
      return adevinta.DIC_Casa_Subtype.Pareada;
    case "20": //	"house"	"{""default"":""Chalet/Torre""}"
      return adevinta.DIC_Casa_Subtype.Chalet_Torre;
    case "21": //	"house"	"{""default"":""Masía""}"
      return adevinta.DIC_Casa_Subtype.Masía;
    case "23": //	"house"	"{""default"":""Unifamiliar""}"
      return adevinta.DIC_Casa_Subtype.Unifamiliar;
    case "24": //	"house"	"{""default"":""Casa rústica""}"
      return adevinta.DIC_Casa_Subtype.Casa_rustica;
    case "25": //	"house"	"{""default"":""Casa de pueblo""}"
      return adevinta.DIC_Casa_Subtype.Casa_de_pueblo;
    case "26": //	"house"	"{""default"":""Casa rural""}"
      return adevinta.DIC_Casa_Subtype.Casa_rural;
    case "27": //	"house"	"{""default"":""Bungalow""}"
      return adevinta.DIC_Casa_Subtype.Bungalow;
    case "28": //	"house"	"{""default"":""Casona""}"
      return adevinta.DIC_Casa_Subtype.Casona;
    default:
      return void 0;

  }
}
function toContactName(offer: OfferDTO): string | undefined {

  let result: string[] = [];
  if (offer.agent?.firstName)
    result.push(offer.agent.firstName);
  if (offer.agent?.lastName)
    result.push(offer.agent.lastName);

  if (result.length !== 0)
    return result.join(" ");
  else
    return void 0;
}
function toAddressZipCode(offer: OfferDTO): string | undefined {
  const postCode = offer.property?.address?.postcode ?? void 0;
  if (postCode !== void 0)
    return postCode.trim();
  else
    return void 0;
}
function toAddressCountry(offer: OfferDTO): adevinta.DIC_LocationCountry | undefined {
  switch (offer.property?.address?.city?.province?.country?.code) {
    case "ES": return adevinta.DIC_LocationCountry.España;
    case "AD": return adevinta.DIC_LocationCountry.Andorra;
    case "PT": return adevinta.DIC_LocationCountry.Portugal;
    case "FR": return adevinta.DIC_LocationCountry.Francia;
    default:
      return void 0;
  }
}
function toAddressStreetType(offer: OfferDTO): adevinta.DIC_StreetType | undefined {
  // DA la casualidad de que hemos usado internamente los tipos de Fotocasa, por lo que el 
  // mapeo es directo... pese a todo, en previsión a cambios futuros, usamos un hash de equivalencias
  switch (offer.property?.address?.streettype?.id) {
    case "1": return adevinta.DIC_StreetType.Calle;
    case "2": return adevinta.DIC_StreetType.Paseo;
    case "3": return adevinta.DIC_StreetType.Avenida;
    case "4": return adevinta.DIC_StreetType.Ronda;
    case "5": return adevinta.DIC_StreetType.Travesía;
    case "6": return adevinta.DIC_StreetType.Carretera;
    case "7": return adevinta.DIC_StreetType.Rambla;
    case "8": return adevinta.DIC_StreetType.Plaza;
    case "9": return adevinta.DIC_StreetType.Pasaje;
    case "10": return adevinta.DIC_StreetType.Bajada;
    case "11": return adevinta.DIC_StreetType.Vía;
    case "13": return adevinta.DIC_StreetType.Urbanización;
    case "14": return adevinta.DIC_StreetType.Camino;
    case "15": return adevinta.DIC_StreetType.Sector;
    case "16": return adevinta.DIC_StreetType.Glorieta;
    case "17": return adevinta.DIC_StreetType.Alameda;
    case "18": return adevinta.DIC_StreetType.Barranco;
    case "19": return adevinta.DIC_StreetType.Calleja;
    case "20": return adevinta.DIC_StreetType.Cuesta;
    case "21": return adevinta.DIC_StreetType.Grupo;
    case "22": return adevinta.DIC_StreetType.Gran_via;
    case "23": return adevinta.DIC_StreetType.Jardines;
    case "24": return adevinta.DIC_StreetType.Muelle;
    case "25": return adevinta.DIC_StreetType.Poligono_industrial;
    case "26": return adevinta.DIC_StreetType.Parque;
    case "27": return adevinta.DIC_StreetType.Prolongación;
    case "28": return adevinta.DIC_StreetType.Riera;
    case "29": return adevinta.DIC_StreetType.Rua;
    case "30": return adevinta.DIC_StreetType.Subida;
    default: return void 0;
  }
}
function toAddressFloorId(offer: OfferDTO): adevinta.DIC_Floor | undefined {
  switch (offer.property?.attributes?.floorCode) {
    case FloorCode.basement: return adevinta.DIC_Floor.Sótano;
    case FloorCode.semibasement: return adevinta.DIC_Floor.Subsótano;
    case FloorCode.ground: return adevinta.DIC_Floor.Planta_baja;
    case FloorCode.mezzanine: return adevinta.DIC_Floor.Entresuelo;
    case FloorCode.main: return adevinta.DIC_Floor.Principal;
    case FloorCode.f1: return adevinta.DIC_Floor.p1;
    case FloorCode.f2: return adevinta.DIC_Floor.p2;
    case FloorCode.f3: return adevinta.DIC_Floor.p3;
    case FloorCode.f4: return adevinta.DIC_Floor.p4;
    case FloorCode.f5: return adevinta.DIC_Floor.p5;
    case FloorCode.f6: return adevinta.DIC_Floor.p6;
    case FloorCode.f7: return adevinta.DIC_Floor.p7;
    case FloorCode.f8: return adevinta.DIC_Floor.p8;
    case FloorCode.f9: return adevinta.DIC_Floor.p9;
    case FloorCode.f10: return adevinta.DIC_Floor.p10;
    case FloorCode.f11: return adevinta.DIC_Floor.p11;
    case FloorCode.f12: return adevinta.DIC_Floor.p12;
    case FloorCode.f13: return adevinta.DIC_Floor.p13;
    case FloorCode.f14: return adevinta.DIC_Floor.p14;
    case FloorCode.f15: return adevinta.DIC_Floor.p15;
    case FloorCode.f16:
    case FloorCode.f17:
    case FloorCode.f18:
    case FloorCode.f19:
    case FloorCode.f20: return adevinta.DIC_Floor.p15_mas
    case FloorCode.penthouse: return adevinta.DIC_Floor.Ático;
    default: return void 0;
  }
}
function toOrientacion(offer: OfferDTO): adevinta.DIC_Orientacion | undefined {
  return ifValue(offer.property?.attributes?.orientationCodes)(orientations => {
    switch (orientations[0]) {
      case OrientationCode.north: return adevinta.DIC_Orientacion.Norte;
      case OrientationCode.northeast: return adevinta.DIC_Orientacion.Noreste;
      case OrientationCode.east: return adevinta.DIC_Orientacion.Este;
      case OrientationCode.southeast: return adevinta.DIC_Orientacion.Sureste;
      case OrientationCode.south: return adevinta.DIC_Orientacion.Sur;
      case OrientationCode.southwest: return adevinta.DIC_Orientacion.Suroeste;
      case OrientationCode.west: return adevinta.DIC_Orientacion.Oeste;
      case OrientationCode.northwest: return adevinta.DIC_Orientacion.Noroeste;
      default:
        return void 0;
    }
  })
}
function toConservacion(offer: OfferDTO): adevinta.DIC_Conservación | undefined {
  switch (offer.property?.attributes?.conservationStatusCode) {
    case ConservationStatusCode.mint: return adevinta.DIC_Conservación.Excelente;
    case ConservationStatusCode.good: return adevinta.DIC_Conservación.Buena;
    case ConservationStatusCode.reformed: return adevinta.DIC_Conservación.Muy_buena;
    case ConservationStatusCode.to_reform: return adevinta.DIC_Conservación.Necesita_reforma;
    default:
      return void 0;
  }
}
function toAireAcondicionaldo(offer: OfferDTO): boolean | undefined {
  return ifValue(offer.property?.attributes?.airConditioningCode)(code => [AirConditioningCode.cold, AirConditioningCode.coldAndHeat].includes(code));
}
function toEscalaEficienciaConsumo(offer: OfferDTO): adevinta.DIC_EscalaEficienciaConsumo | undefined {

  switch (offer.property?.attributes?.consumptionLevelCode) {
    case ConsumptionLevelCode.A: return adevinta.DIC_EscalaEficienciaConsumo.A;
    case ConsumptionLevelCode.B: return adevinta.DIC_EscalaEficienciaConsumo.B;
    case ConsumptionLevelCode.C: return adevinta.DIC_EscalaEficienciaConsumo.C;
    case ConsumptionLevelCode.D: return adevinta.DIC_EscalaEficienciaConsumo.D;
    case ConsumptionLevelCode.E: return adevinta.DIC_EscalaEficienciaConsumo.E;
    case ConsumptionLevelCode.F: return adevinta.DIC_EscalaEficienciaConsumo.F;
    case ConsumptionLevelCode.G: return adevinta.DIC_EscalaEficienciaConsumo.G;
    default: // null y undefined pasan por aquí
      return void 0;
  }
}
function toEscalaEficienciaEmisiones(offer: OfferDTO): adevinta.DIC_EscalaEficienciaEmisiones | undefined {

  switch (offer.property?.attributes?.emissionLevelCode) {
    case EmissionLevelCode.A: return adevinta.DIC_EscalaEficienciaEmisiones.A;
    case EmissionLevelCode.B: return adevinta.DIC_EscalaEficienciaEmisiones.B;
    case EmissionLevelCode.C: return adevinta.DIC_EscalaEficienciaEmisiones.C;
    case EmissionLevelCode.D: return adevinta.DIC_EscalaEficienciaEmisiones.D;
    case EmissionLevelCode.E: return adevinta.DIC_EscalaEficienciaEmisiones.E;
    case EmissionLevelCode.F: return adevinta.DIC_EscalaEficienciaEmisiones.F;
    case EmissionLevelCode.G: return adevinta.DIC_EscalaEficienciaEmisiones.G;
    default: // null y undefined pasan por aquí
      return void 0;
  }
}
function toCertificadoEnergetico(offer: OfferDTO): adevinta.DIC_CertificadoEnergético | undefined {
  switch (offer.property?.attributes?.energyCertificateCode) {
    case EnergyCertificateCode.available: return adevinta.DIC_CertificadoEnergético.Si;
    case EnergyCertificateCode.inProcess: return adevinta.DIC_CertificadoEnergético.En_trámite;
    case EnergyCertificateCode.exempt: return adevinta.DIC_CertificadoEnergético.Exento;
    default: // null y undefined pasan por aquí
      return void 0;
  }
}
function toCarpinteriaExterior(offer: OfferDTO): adevinta.DIC_CarpinteriaExterior | undefined {
  switch (offer.property?.attributes?.externalJoineryCode) {
    case ExternalJoineryCode.aluminium: return adevinta.DIC_CarpinteriaExterior.aluminio;
    case ExternalJoineryCode.pvc: return adevinta.DIC_CarpinteriaExterior.pvc;
    case ExternalJoineryCode.wood: return adevinta.DIC_CarpinteriaExterior.madera;
    default: // No mapeable, null y void 0 pasan por aquí
      void 0
  }
}
function toSuelos(offer: OfferDTO): adevinta.DIC_Suelo | undefined {
  return ifValue(offer.property?.attributes?.groundCodes)(codes => {
    if (codes.length === 0)
      return void 0;
    else
      switch (codes[0]) {
        case GroundCode.carpet: return adevinta.DIC_Suelo.moqueta;
        case GroundCode.gres: return adevinta.DIC_Suelo.de_gres;
        case GroundCode.laminatedFlooring: return adevinta.DIC_Suelo.de_tarima;
        case GroundCode.marble: return adevinta.DIC_Suelo.de_mármol;
        case GroundCode.parquet: return adevinta.DIC_Suelo.de_madera;
        case GroundCode.solidFlooring: return adevinta.DIC_Suelo.de_tarima;
        case GroundCode.terrazzo: return adevinta.DIC_Suelo.de_terrazo;
        default:
          return void 0;
      }
  });

}
function toRefrigeración(offer: OfferDTO): adevinta.DIC_Refrigeración | undefined {
  return ifValue(offer.property?.attributes?.airConditioningCode)(code => {
    switch (code) {
      case AirConditioningCode.cold: return adevinta.DIC_Refrigeración.Aacc_solo_frío;
      case AirConditioningCode.coldAndHeat: return adevinta.DIC_Refrigeración.Aacc_frío_calor;
      default:
        return void 0;
    }
  })
}
function toCalefacciónOpt(offer: OfferDTO): adevinta.DIC_Calefacción | undefined {
  return ifValue(offer.property?.attributes?.heatingCode)(code => {
    switch (code) {
      case HeatingCode.electric: return adevinta.DIC_Calefacción.Electricidad;
      case HeatingCode.gasoil: return adevinta.DIC_Calefacción.Gasóleo;
      case HeatingCode.naturalGas: return adevinta.DIC_Calefacción.Gas_natural;
      default: return void 0;
    }
  })
}
function mapOfferTransactions(offer: OfferDTO): adevinta.PropertyTransaction[] {
  var result: adevinta.PropertyTransaction[] = [];
  let m2: number = ifValue(offer.property?.attributes?.totalSurfaceM2)(m2 => toNumber(m2)) ?? throwMissing("Property M2");
  
  if (offer.version?.type?.code===OfferversiontypeCode.monthlypayment){
    let price: number = ifValue(offer.sale?.monthlyPayment)(v => toNumber(v)) ?? throwMissing("Monthly Payment");   
    result.push(
      {
        TransactionTypeId: adevinta.DIC_TransactionType.Alquiler,
        Price: price,
        PriceM2: Math.floor(price / m2),
        CurrencyId: adevinta.DIC_Currency.Euros,
        ShowPrice: true
      }
    );
    return result;
  }
  
  if (offer.sale?.allowed) {
    let price: number = ifValue(offer.sale?.amount)(v => toNumber(v)) ?? throwMissing("Sale Price");
    result.push(
      {
        TransactionTypeId: adevinta.DIC_TransactionType.Venta,
        Price: price,
        PriceM2: Math.floor(price / m2),
        CurrencyId: adevinta.DIC_Currency.Euros,
        ShowPrice: true
      }
    );
  }
  if (offer.rent?.allowed) {
    let price: number = ifValue(offer.rent?.amount)(v => toNumber(v)) ?? throwMissing("Rent Price");
    result.push(
      {
        TransactionTypeId: adevinta.DIC_TransactionType.Alquiler,
        Price: price,
        PriceM2: Math.floor(price / m2),
        CurrencyId: adevinta.DIC_Currency.Euros,
        ShowPrice: true
      }
    );
  }
  return result;

}
function emptyToUndefined<T>(values: T[]): T[] | undefined {
  if (values.length === 0)
    return void 0;
  else
    return values;
}
function getDefaultPublications(availablePortals: adevinta.PublicationPortalStruct[]): adevinta.PropertyPublication[] {
  const { logger } = appContext();
  // 1/Junio/2022:  Dejamos de discriminar portales... todo lo que se envía a fotocasa se republica a los portales
  //                fotocasa/habitaclia/milanuncios
  const publicationData = availablePortals.find(portal => portal.PublicationId === adevinta.DIC_Publication.Fotocasa_y_Milanuncios);
  if (publicationData)
    return [{
      PublicationId: publicationData.PublicationId,
      PublicationTypeId: publicationData.TypeId
    }];
  else {
    logger.warn(`Fotocasa no está entre las publicaciones disponibles en inmofactory!!!`);
    return [];
  }


}
/**
 * Si el valor de entrada es null o undefined, devuelve undefined.
 * En caso contrario evalúa la función fVal con el valor y devuelve el resultado de dicha evocación
 * @param nullableVal 
 * @param fVal 
 */
function ifValue<T>(nullableVal: T | null | undefined) {
  return <R>(fVal: (val: T) => R): R | undefined => (nullableVal === null || nullableVal === void 0) ? void 0 : fVal(nullableVal);
}
function throwMissing(fieldName: string): never {
  throwError(`No se puede determinar ${fieldName}`);
}
function throwError(message: string): never {
  throw new Error(message);
}