import { pisos_com } from "external/pisoscom/AdStruct";
import { TypedJsonUtils } from "lib/TypedJsonUtils";
import { MediaDTO } from "model/common/dtos/MediaDTO";
import { OfferDTO } from "model/common/dtos/OfferDTO";
import { AirConditioningCode, ConservationStatusCode, ConsumptionLevelCode, EmissionLevelCode, ExternalJoineryCode, GroundCode, HeatingCode, NoneOwnCommunity, OrientationCode } from "model/common/dtos/PropertyAttributesDTO";
import { PropertytypeCode } from "model/common/dtos/PropertytypeDTO";
import { PropertymediasModel } from "model/propertymediasModel";
import { jsonArrayMember, jsonMember, jsonObject } from "typedjson";


export class PisoscomOfferMapper {

  private options: PisoscomOfferMapper.Options;
  constructor(options: PisoscomOfferMapper.Options) {
    this.options = options;
  }

  public async mapOffer(offer: OfferDTO): Promise<pisos_com.Property> {
    if (offer.id === void 0)
      throw new Error("offer id is mandatory!!!");
    if ((offer.agent?.id ?? void 0) === void 0)
      throw new Error("offer agent is mandatory!!!");

    const medias = await PropertymediasModel.list({ offer_id: offer.id })().
      then(propertymedias => {
        const propertyimages = propertymedias.filter(pm => pm.media?.original?.mediatype?.startsWith("image"));
        return [
          // Primero las marcadas como favoritas
          ...propertyimages.filter(pm => pm.isFavourite ?? false),
          // Las marcadas como NO favoritas
          ...propertyimages.filter(pm => !(pm.isFavourite ?? false))
        ].map(pm => pm.media) as MediaDTO[];
      });


    const attributes = offer.property?.attributes;

    return {

      externalId: offer.id as string,
      agencyId: this.options.agencyId,
      reference: offer.id as string,
      propertyTypeId: toTypeId(offer) ?? throwError("No se puede determinar propertyTypeId"),
      propertyTransactions: [
        ...offer.sale?.allowed ? [
          {
            publicationId: pisos_com.TPublicationId.pisos,
            transactionTypeId: pisos_com.TSales.venta,
            price: toInt(offer.sale?.amount) ?? throwError("No se puede determinar price"),
          } as pisos_com.PropertyTransaction
        ] : [],
        ...offer.rent?.allowed ? [
          {
            publicationId: pisos_com.TPublicationId.pisos,
            transactionTypeId: pisos_com.TSales.alquiler,
            price: toInt(offer.rent?.amount) ?? throwError("No se puede determinar price"),
          } as pisos_com.PropertyTransaction
        ] : []
      ],
      propertyLocation: {
        longitudeCoordinate: offer.property?.location?.longitude ?? throwMissing("propertyLocation.longitudeCoordinate"),
        latitudeCoordinate: offer.property?.location?.latitude ?? throwMissing("propertyLocation.latitudeCoordinate"),
        zipCode: offer.property?.address?.postcode ?? throwMissing("propertyLocation.zipCode")
      },
      propertyDescriptions: offer.description !== void 0 ? [
        { description: offer.description ?? void 0, language: "es" }
      ] : [],
      propertyContact: this.options.propertyContact,
      propertyMedias: toPropertyMedias(medias, this.options.useWatermark),
      propertyFeatures: [
        ifValue(attributes?.totalBedroomsCount, pisos_com.featuresFactory.habitacionesTotales),
        ifValue(attributes?.doubleBedroomsCount, pisos_com.featuresFactory.habitacionesDobles),
        ifValue(attributes?.individualBedroomsCount, pisos_com.featuresFactory.habitacionesSimples),
        ifValue(attributes?.bathroomsCount, pisos_com.featuresFactory.bañosTotales),
        ifValue(attributes?.toiletsCount, pisos_com.featuresFactory.bañosAuxiliares),
        ifValue(attributes?.totalSurfaceM2, pisos_com.featuresFactory.superficieConstruida),
        ifValue(attributes?.usefulSurfaceM2, pisos_com.featuresFactory.superficieUtil),
        ifValue(attributes?.solarSurfaceM2, pisos_com.featuresFactory.superficieSolar),
        ifValue(attributes?.neighborsPerFloorCount, pisos_com.featuresFactory.numeroVecinos),
        ifValue(toConsumo(offer), pisos_com.featuresFactory.energiaConsumo),
        ifValue(toEmision(offer), pisos_com.featuresFactory.energiaEmisiones),
        ifValue(toCondicion(offer), pisos_com.featuresFactory.estadoConservacion),
        ifValue(attributes?.constructionYear, pisos_com.featuresFactory.añoConstruccion),
        ifValue(attributes?.dinningRoomHas, pisos_com.featuresFactory.comedor),
        ifValue(attributes?.kitchenHas, pisos_com.featuresFactory.cocina),
        ifValue(attributes?.buddleHas, pisos_com.featuresFactory.lavadero),
        ifValue(attributes?.elevatorHas, pisos_com.featuresFactory.ascensor),
        ifValue(toSuelo(offer), pisos_com.featuresFactory.suelo),
        ifValue(attributes?.storageRoomHas, pisos_com.featuresFactory.trastero),
        ifValue(attributes?.balconyHas, pisos_com.featuresFactory.balcon),
        ifValue(attributes?.terraceHas, pisos_com.featuresFactory.terraza),
        ifValue(attributes?.furnishedIs, pisos_com.featuresFactory.amueblado),
        ifValue(toCalefaccion(offer), pisos_com.featuresFactory.calefaccion),
        ifValue(toAireAcondicionado(offer), pisos_com.featuresFactory.aire_acondicionado),
        ifValue(attributes?.doubleGlassesHas, pisos_com.featuresFactory.vidrios_dobles),
        ifValue(toCarpinteriaExterior(offer), pisos_com.featuresFactory.carpinteria_exterior),
        ifValue(attributes?.fireplaceHas, pisos_com.featuresFactory.chimenea),
        ifValue(attributes?.reinforcedDoorHas, pisos_com.featuresFactory.puerta_blindada),
        ifValue(attributes?.optionalParkingIs, pisos_com.featuresFactory.garaje),
        ifValue(toJardin(offer), pisos_com.featuresFactory.jardin),
        ifValue(toPiscina(offer), pisos_com.featuresFactory.piscina),
        ifValue(attributes?.handicappedAccessibleIs, pisos_com.featuresFactory.accesibilidad),
        ifValue(toOrientacion(offer), pisos_com.featuresFactory.orientacion),
        ifValue(attributes?.sunnyIs, pisos_com.featuresFactory.soleado),
        ifValue(attributes?.communityFeesAmount, pisos_com.featuresFactory.gastos_comunidad),
        ifValue(attributes?.waterSupplyHas, pisos_com.featuresFactory.suministro_agua),
        ifValue(toSuministroElectrico(offer), pisos_com.featuresFactory.suministro_electrico),
        ifValue(toSuministroGas(offer), pisos_com.featuresFactory.suministro_gas),
        ifValue(attributes?.intercomHas, pisos_com.featuresFactory.portero_automatico),

      ].filter(f => !!f) as pisos_com.PropertyFeature[],
    };
  }

}

export namespace PisoscomOfferMapper {
  @jsonObject
  export class PropertyContact {
    @jsonMember(String, { isRequired: true })
    email!: string;
    @jsonMember(String, { isRequired: true })
    phone!: string;
    @jsonMember(Number, { isRequired: true, deserializer: TypedJsonUtils.oneOfDeserializer([pisos_com.TPublicationId.microsite, pisos_com.TPublicationId.piso_compartido, pisos_com.TPublicationId.pisos]) })
    publicationId!: number;
  }
  @jsonObject
  export class Options {
    /** Usar las imágenes con watermark (default=true) */
    @jsonMember(Boolean, { isRequired: false })
    useWatermark: boolean = true;
    @jsonMember(String, { isRequired: true })
    agencyId!: string;
    @jsonArrayMember(PisoscomOfferMapper.PropertyContact, { isRequired: true })
    propertyContact!: PisoscomOfferMapper.PropertyContact[];
  }

}
function toPropertyMedias(medias: MediaDTO[], withWatermark: boolean = true): pisos_com.PropertyMedia[] {
  return medias.
    filter(media => ["image/jpeg", "image/jpg", "image/png"].includes(media?.original?.mediatype ?? "")).
    map(media => withWatermark ? media?.publishing?.url : media?.publishingNoWm?.url).
    filter(url => !!url).
    map((url, idx) => (
      {
        mediaFormatId: pisos_com.TMediaFormatId.fotografia,
        mediaStatusId: pisos_com.TMediaStatusId.activo,
        mediaTypeId: pisos_com.TMediaTypeId.fotografia,
        order: idx,
        url: url!,  // Sabemos que hay URL por el filtro previo (filter(url=>!!url)... pero elcompilador de typescript no.)
      } as pisos_com.PropertyMedia
    ));
}
function toTypeId(offer: OfferDTO) {
  switch (offer.property?.type?.code) {
    case PropertytypeCode.flat:
      switch (offer.property?.subtype?.code) {
        //case "1": //	"flat"	"{""default"":""Semisótano""}"
        //  return pisos_com.TInmueblesCategoria.piso;
        case "2": //	"flat"	"{""default"":""Triplex""}"
          return pisos_com.TInmueblesCategoria.triplex;
        case "3": //	"flat"	"{""default"":""Dúplex""}"
          return pisos_com.TInmueblesCategoria.duplex;
        case "4": //	"flat"	"{""default"":""Buhardilla""}"
          return pisos_com.TInmueblesCategoria.atico;
        case "5": //	"flat"	"{""default"":""Ático""}"
          return pisos_com.TInmueblesCategoria.atico;
        case "6": //	"flat"	"{""default"":""Estudio""}"
          return pisos_com.TInmueblesCategoria.estudio;
        case "7": //	"flat"	"{""default"":""Loft""}"
          return pisos_com.TInmueblesCategoria.loft;
        //  case "8": //	"flat"	"{""default"":""Otro""}"
        //   return pisos_com.TInmueblesCategoria.piso
        case "9": //	"flat"	"{""default"":""Piso""}"
          return pisos_com.TInmueblesCategoria.piso;
        case "10": //	"flat"	"{""default"":""Apartamento""}"
          return pisos_com.TInmueblesCategoria.apartamento;
        // case "11": //	"flat"	"{""default"":""Planta baja""}"
        //   return pisos_com.TInmueblesCategoria.piso;
        default:
          return pisos_com.TInmueblesCategoria.piso;
      }
    case PropertytypeCode.house:
      switch (offer.property?.subtype?.code) {
        case "13": //	"house"	"{""default"":""Casa""}"
          return pisos_com.TInmueblesCategoria.casa;
        case "14": //	"house"	"{""default"":""Cortijo""}"
          return pisos_com.TInmueblesCategoria.cortijo;
        case "17": //	"house"	"{""default"":""Adosada""}"
          return pisos_com.TInmueblesCategoria.casa_adosada;
        case "18": //	"house"	"{""default"":""Caserio""}"
          return pisos_com.TInmueblesCategoria.casa_de_campo;
        case "19": //	"house"	"{""default"":""Pareada""}"
          return pisos_com.TInmueblesCategoria.casa_pareada;
        case "20": //	"house"	"{""default"":""Chalet/Torre""}"
          return pisos_com.TInmueblesCategoria.chalet;
        case "21": //	"house"	"{""default"":""Masía""}"
          return pisos_com.TInmueblesCategoria.masia;
        case "23": //	"house"	"{""default"":""Unifamiliar""}"
          return pisos_com.TInmueblesCategoria.casa_unifamiliar;
        case "24": //	"house"	"{""default"":""Casa rústica""}"
          return pisos_com.TInmueblesCategoria.casa_rustica;
        // case "25": //	"house"	"{""default"":""Casa de pueblo""}"
        //   return pisos_com.TInmueblesCategoria.casa
        case "26": //	"house"	"{""default"":""Casa rural""}"
          return pisos_com.TInmueblesCategoria.casa_rustica;
        case "27": //	"house"	"{""default"":""Bungalow""}"
          return pisos_com.TInmueblesCategoria.bungalow;
        // case "28": //	"house"	"{""default"":""Casona""}"
        //   return pisos_com.TInmueblesCategoria.casa
        default:
          return pisos_com.TInmueblesCategoria.casa;
      }

    default: return void 0;
  }

}
/**
   * El valor a deserializar deben existir en el conjunto de valores aceptados
   * @param acceptedValues 
   * @returns 
   */

function toAireAcondicionado(offer: OfferDTO): pisos_com.TAireAcondicionado | undefined {
  const airConditioningCodes = offer.property?.attributes?.airConditioningCode;
  if (airConditioningCodes) {
    switch (airConditioningCodes[0]) {
      case AirConditioningCode.cold: return pisos_com.TAireAcondicionado.frio;
      case AirConditioningCode.coldAndHeat: return pisos_com.TAireAcondicionado.frio_y_calor;
      default: return undefined;
    }
  }
}
function toCalefaccion(offer: OfferDTO): pisos_com.TCalefaccion | undefined {
  const heatingCodes = offer.property?.attributes?.heatingCode;
  if (heatingCodes) {
    switch (heatingCodes[0]) {
      case HeatingCode.central: return pisos_com.TCalefaccion.central;
      case HeatingCode.electric: return pisos_com.TCalefaccion.electrica;
      case HeatingCode.gasoil: return pisos_com.TCalefaccion.gasoil;
      case HeatingCode.naturalGas: return pisos_com.TCalefaccion.gas_natural;
      default: return undefined;
    }
  }
}
function toCondicion(offer: OfferDTO): pisos_com.TCondicion | undefined {
  const conservationStatusCodes = offer.property?.attributes?.conservationStatusCode;
  if (conservationStatusCodes) {
    switch (conservationStatusCodes[0]) {
      case ConservationStatusCode.mint: return pisos_com.TCondicion.a_estrenar;
      case ConservationStatusCode.good: return pisos_com.TCondicion.en_buen_estado;
      case ConservationStatusCode.to_reform: return pisos_com.TCondicion.a_reformar;
      case ConservationStatusCode.reformed: return pisos_com.TCondicion.reformado;
      default: return undefined;
    }
  }
}
function toConsumo(offer: OfferDTO): pisos_com.TEnergia | undefined {
  const consumptionCodes = offer.property?.attributes?.consumptionLevelCode;
  if (consumptionCodes) {
    switch (consumptionCodes[0]) {
      case ConsumptionLevelCode.A: return pisos_com.TEnergia.A;
      case ConsumptionLevelCode.B: return pisos_com.TEnergia.B;
      case ConsumptionLevelCode.C: return pisos_com.TEnergia.C;
      case ConsumptionLevelCode.D: return pisos_com.TEnergia.D;
      case ConsumptionLevelCode.E: return pisos_com.TEnergia.E;
      case ConsumptionLevelCode.F: return pisos_com.TEnergia.F;
      case ConsumptionLevelCode.G: return pisos_com.TEnergia.G;
      default: return undefined;
    }
  }
}
function toEmision(offer: OfferDTO): pisos_com.TEnergia | undefined {
  const emissionCodes = offer.property?.attributes?.emissionLevelCode;
  if (emissionCodes) {
    switch (emissionCodes[0]) {
      case EmissionLevelCode.A: return pisos_com.TEnergia.A;
      case EmissionLevelCode.B: return pisos_com.TEnergia.B;
      case EmissionLevelCode.C: return pisos_com.TEnergia.C;
      case EmissionLevelCode.D: return pisos_com.TEnergia.D;
      case EmissionLevelCode.E: return pisos_com.TEnergia.E;
      case EmissionLevelCode.F: return pisos_com.TEnergia.F;
      case EmissionLevelCode.G: return pisos_com.TEnergia.G;
      default: return undefined;
    }
  }
}
function toCarpinteriaExterior(offer: OfferDTO): pisos_com.TCarpinteria | undefined {
  const joineryCodes = offer.property?.attributes?.externalJoineryCode;
  if (joineryCodes) {
    switch (joineryCodes[0]) {
      case ExternalJoineryCode.aluminium: return pisos_com.TCarpinteria.aluminio;
      case ExternalJoineryCode.pvc: return pisos_com.TCarpinteria.PVC;
      case ExternalJoineryCode.wood: return pisos_com.TCarpinteria.madera;
      default: return undefined;
    }
  }
}
function toOrientacion(offer: OfferDTO): pisos_com.TOrientacion | undefined {
  const orientationCodes = offer.property?.attributes?.orientationCodes;
  if (orientationCodes)
    switch (orientationCodes[0]) {
      case OrientationCode.north: return pisos_com.TOrientacion.Norte;
      case OrientationCode.northeast: return pisos_com.TOrientacion.Noreste;
      case OrientationCode.east: return pisos_com.TOrientacion.Este;
      case OrientationCode.southeast: return pisos_com.TOrientacion.Sureste;
      case OrientationCode.south: return pisos_com.TOrientacion.Sur;
      case OrientationCode.southwest: return pisos_com.TOrientacion.Suroeste;
      case OrientationCode.west: return pisos_com.TOrientacion.Oeste;
      case OrientationCode.northwest: return pisos_com.TOrientacion.Noroeste;
      default: return undefined;
    }
}
function toSuelo(offer: OfferDTO): pisos_com.TSuelo | undefined {
  const groundCodes = offer.property?.attributes?.groundCodes;
  if (groundCodes)
    switch (groundCodes[0]) {
      case GroundCode.gres: return pisos_com.TSuelo.gres;
      case GroundCode.marble: return pisos_com.TSuelo.marmol;
      case GroundCode.carpet: return pisos_com.TSuelo.moqueta;
      case GroundCode.parquet: return pisos_com.TSuelo.parquet;
      case GroundCode.laminatedFlooring: return pisos_com.TSuelo.tarima_flotante;
      case GroundCode.solidFlooring: return pisos_com.TSuelo.tarima_maciza;
      case GroundCode.terrazzo: return pisos_com.TSuelo.terrazo;
      default: return undefined;
    }
}
function toJardin(offer: OfferDTO): pisos_com.TJardin | undefined {
  return ifValue(offer.property?.attributes?.gardenCode, code => {
    switch (code) {
      case NoneOwnCommunity.own: return pisos_com.TJardin.propio;
      case NoneOwnCommunity.community: return pisos_com.TJardin.comunitario;
      default: return undefined;
    }
  });
}
function toPiscina(offer: OfferDTO): pisos_com.TPiscina | undefined {
  return ifValue(offer.property?.attributes?.swimmingPoolCode, code => {
    switch (code) {
      case NoneOwnCommunity.own: return pisos_com.TPiscina.propia;
      case NoneOwnCommunity.community: return pisos_com.TPiscina.comunitaria;
      default: return undefined;
    }
  });
}

// Due to our value being boolean, and theirs a list of provider, we only specify the 
// ones that do not have Gas or Electricity (in next function)
function toSuministroGas(offer: OfferDTO): pisos_com.TSuministroGas | undefined {
  return ifValue(offer.property?.attributes?.gasSupplyHas, code => {
    if (code === false) {
      return pisos_com.TSuministroGas.sin_gas;
    } else {
      return undefined;
    }
  })
}
function toSuministroElectrico(offer: OfferDTO): pisos_com.TSuministroElectrico | undefined {
  return ifValue(offer.property?.attributes?.powerSupplyHas, code => {
    if (code === false) {
      return pisos_com.TSuministroElectrico.sin_luz;
    } else {
      return undefined;
    }
  })
}

function toInt(price: string | number | undefined | null): number | undefined {
  switch (typeof (price)) {
    case "string":
      return parseInt(price as string);
    case "number":
      return price as number;
    default:
      return void 0;
  }
}

/**
 * Si el valor de entrada es null o undefined, devuelve undefined.
 * En caso contrario evalúa la función fVal con el valor y devuelve el resultado de dicha evocación
 * @param nullableVal 
 * @param fVal 
 */
function ifValue<T, R>(nullableVal: T | null | undefined, fVal: (val: T) => R): R | undefined {
  if (nullableVal === null || nullableVal === void 0)
    return void 0;
  else
    return fVal(nullableVal);
}

function throwMissing(fieldName: string): never {
  throw throwError(`No se puede determinar ${fieldName}`);
}
function throwError(message: string): never {
  throw new Error(message);
}
