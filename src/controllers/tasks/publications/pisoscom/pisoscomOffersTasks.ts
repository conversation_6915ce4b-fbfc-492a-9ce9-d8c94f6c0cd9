import { RestError, throwUndefinedError, throwUnexpectedError } from "agentor-lib";
import { PisoscomAPI } from "external/pisoscom/PisoscomAPI";
import { withAppContext } from "lib/AppContext";
import { TypedJsonUtils } from "lib/TypedJsonUtils";
import { AscOrDesc } from "model/common/AscOrDesc";
import { OfferstatusCode } from "model/common/dtos/OfferstatusDTO";
import { OffersModel } from "model/offersModel";
import { taskpubOplogDAO } from "../common/taskpubOplogsDAO";
import { taskpubProvidersDAO } from "../common/taskpubProvidersDAO";
import { taskpubPublishedofferDAO } from "../common/taskpubPublishedoffersDAO";
import { taskpubWorkgroupprovidersDAO } from "../common/taskpubWorkgroupprovidersDAO";
import { PisoscomOfferConsts } from "./pisoscomOfferConsts";
import { PisoscomOfferMapper } from "./pisoscomOfferMapper";


const provider_code = "pisoscom"
const provider_api_code = "pisoscom";
export namespace PisoscomOffersTasks {
  export type publishOffersTasksOptions = {
    /** 
     * Código del proveedor de publicación que se desea sincronizar.
     * De no indicarse, se sincronizarán todos los proveedores del tipo "inmofactory" */
    provider_code?: string,
    /** 
     * Conciliar la lista de ofertas que creemos haber publicado en pisoscom con los anuncios que realmente están
     * publicados en pisoscom
     * Pisoscom no ofrece nada en su API para conocer los anuncios publicados, por lo que este parámetro, de indicarse, debe ser FALSE
     */
    conciliate?: false,
    /**
     * En log, volcar JSON de los anuncios enviados a pisos.com
     */
    logJson?: boolean
  };
  export async function publishOffersTask(options: publishOffersTasksOptions) {
    const { provider_code, conciliate = false, logJson = false } = options;
    const { upset, remove } = taskpubPublishedofferDAO;
    await withAppContext(async ({ environment, logger }) => {

      logger.info(`START PisoscomOffersTasks.publishOffersTask`);

      const providers = await taskpubProvidersDAO.list({ code: provider_code, api_code: taskpubProvidersDAO.ApiCode.pisoscom })();
      for (const provider of providers) {

        logger.info(`> Actualizando inmuebles en ${provider.name}`);

        try {
          logger.debug(">> Obteniendo API y mappeador de ofertas")

          const
            pisoscomApi = new PisoscomAPI(TypedJsonUtils.parse(
              provider.api.conf,
              PisoscomAPI.Options
            )),
            mapper = new PisoscomOfferMapper(TypedJsonUtils.parse(
              provider.offermapper?.options ?? {},
              PisoscomOfferMapper.Options
            ));

          logger.debug(">> Sincronizando");

          const publishedVersions = await listPublishedVersions(provider.code, conciliate);
          const sharedOffers = enumerateSharedOffers(provider.code);
          let sharedOffer = await sharedOffers.next();
          while (!sharedOffer.done) {
            const offer = sharedOffer.value;

            const offer_id = offer.id ?? "";
            const revision = offer.updated?.at ?? new Date(0);

            if (publishedVersions.has(offer_id)) {
              if (publishedVersions.get(offer_id)?.getTime() !== revision.getTime()) {
                await logOp({ op: "update", provider_code: provider.code, offer_id, revision })(async () => {
                  let ad = await mapper.mapOffer(offer);
                  if (logJson) logger.info(JSON.stringify(ad));
                  await pisoscomApi.putProperty(ad);
                  await upset({ provider_code: provider.code, offer_id, revision });
                });
              }
              publishedVersions.delete(offer_id);

            } else {
              await logOp({ op: "create", provider_code: provider.code, offer_id, revision })(async () => {
                const ad = await mapper.mapOffer(offer);
                if (logJson) logger.info(JSON.stringify(ad));
                await pisoscomApi.putProperty(ad);
                await upset({ provider_code: provider.code, offer_id, revision });
              });
            }

            sharedOffer = await sharedOffers.next();
          }

          // Remaining adevinta IDs must be removed
          for (const publishedOfferId of publishedVersions.keys())
            await logOp({ provider_code: provider.code, op: "delete", offer_id: publishedOfferId })(async () => {
              try {
                await pisoscomApi.
                  delProperty(publishedOfferId).
                  catch(e => {
                    if (e instanceof RestError && e.status === 404)
                      // La propiedad que intentamos borrar en pisoscom no existe... lo damos por bueno
                      return;
                    else
                      throw e;
                  });
              } catch (e) {

              }
              await remove(provider.code, publishedOfferId);
            });

        } catch (e) {
          logger.error("Uncontrolled exception", e);
        } finally {
          logger.info("END PisoscomOffersTasks.publishOffersTask");
        }
      }
    });
  }

  /**
   * Consultamos qué ofertas y en qué versión están publicadas en inmofactory
   * Esta información reside en nuestra BBDD.  
   * @param conciliate Indica si la lista de ofertas publicadas debe conciliarse con el servicio remoto de ofertas de inmofactory.
   * @returns Un Map usando como clave el identificador de la oferta y como valor la fecha (o versión) publicada.
   * 
   */
  async function listPublishedVersions(provider_code: string, conciliate: false): Promise<Map<string, Date>> {
    const result = await taskpubPublishedofferDAO.list(provider_code);

    if (conciliate) {
      throw new Error("Not inplemented yet!!!");
    } else {
      return new Map(result.map(puboff => ([puboff.offer_id, puboff.revision])));
    }

  }


  /**
   * Registramos en la tabla "tasks_inmofactory_offerlog" cada operación crear/modificar/borrar y si su resultado a sido ok o error (en caso de error, con el detalle del mensaje de error).
   * 
   * Los errores no son absorvidos y logeados, pero no se "relanzan"
   * 
   * @param offer identificador y revisión de la oferta sobre la que operamos
   * @param op "update"."create" o "delete"
   * @param f función asíncrona que ejecuta la operación y cuyo error, en caso de suceder, es registrado
   */
  function logOp<R>({ provider_code, op, offer_id, revision }: { provider_code: string, op: string, offer_id: string, revision?: Date }) {

    return async (f: () => Promise<R>) =>
      withAppContext(async ({ logger }) => {
        try {
          logger.debug(`START ${op} ${offer_id} ${revision ? revision.toISOString() : ""}`)
          const r = await f()
          await insertLog()
          logger.info(`OK ${op} ${offer_id} ${revision ? revision.toISOString() : ""}`)
          return r
        } catch (e) {
          logger.error(`${op} ${offer_id} ${revision ? revision.toISOString() : ""}`, e)
          await insertLog(e as Error)
        }

        async function insertLog(error?: Error) {
          try {
            await taskpubOplogDAO.insert({
              provider_code,
              offer_id,
              revision: revision ?? null,
              op,
              ok: !error,
              error: error ? `${error.name}:${error.message}` : null
            });
          } catch (e) {
            logger.error("can't insert in tasks_publications_offerlog", (e as Error).message ?? e);
          }
        }

      });
  }
  /**
   * Enumera las ofertas de topbrokers compartidas con el proveedor de publicación indicado
   */
  async function* enumerateSharedOffers(provider_code: string) {
    let worgroupIds = await taskpubWorkgroupprovidersDAO.listWorkgroupsIds({ provider_code: provider_code })();
    const filter = {
      sharedInWorkgroupIds: worgroupIds,
      status_codes: [OfferstatusCode.commercialization],
      orderBy: { id: AscOrDesc.asc },
      pagination: { limit: 50 }
    };
    let offers = await OffersModel.list(filter, { includeAgentData: true })();
    while (offers.length !== 0) {
      for (const offer of offers) yield offer;
      offers = await OffersModel.list({ ...filter, id_greaterThan: offers[offers.length - 1].id }, { includeAgentData: true })();
    }
  }

}