import { appContext, withAppContext } from "lib/AppContext";
import { PoolClient } from "pg";
import { SqlWhereHelpers } from "agentor-lib";
const { addEq } = SqlWhereHelpers;

enum WorkgroupprovidersFlds {
  workgroup_id = "workgroup_id",
  provider_code = "provider_code",
}

export namespace taskpubWorkgroupprovidersDAO {
  export type ListFilter = { provider_code?: string, workgroup_id?: string };

  export type WorkgroupProviderDTO = {
    workgroup: {
      id: string
    },
    provider: {
      code: string
    }
  }

  /**
   * Lista de todos los publishedoffers del proveedor
   * @returns 
   */
  export function list(filter: ListFilter): (dbCli?: PoolClient) => Promise<WorkgroupProviderDTO[]> {
    const { db } = appContext();
    let { terms, params } = buildConditions(filter);

    const qry = `
    select 
      ${WorkgroupprovidersFlds.workgroup_id},
      ${WorkgroupprovidersFlds.provider_code}
    from 
      tasks_publications_workgroupproviders 
    where ${terms.join(" and ")}
    `;

    return db.withClient(dbCli =>
      dbCli.query(qry, params).then(({ rows }) => rows.map(
        row => ({
          workgroup: { id: row[WorkgroupprovidersFlds.workgroup_id] },
          provider: { code: row[WorkgroupprovidersFlds.provider_code] }
        } as WorkgroupProviderDTO)
      ))
    );

  }

  export function listWorkgroupsIds(filter: ListFilter): (dbCli?: PoolClient) => Promise<string[]> {
    const { db } = appContext();
    let { terms, params } = buildConditions(filter);

    const qry = `
    select 
      distinct ${WorkgroupprovidersFlds.workgroup_id}
    from 
      tasks_publications_workgroupproviders 
    where ${terms.join(" and ")}
    `;

    return db.withClient(dbCli =>
      dbCli.query(qry, params).then(result => result.rows.map(row => row[WorkgroupprovidersFlds.workgroup_id]))
    );

  }

  function buildConditions(filter: ListFilter) {

    let terms: string[] = ["true"];
    let params: any[] = [];

    addEq(terms, params, WorkgroupprovidersFlds.workgroup_id, filter.workgroup_id);
    addEq(terms, params, WorkgroupprovidersFlds.provider_code, filter.provider_code);

    return { terms, params };
  }


}