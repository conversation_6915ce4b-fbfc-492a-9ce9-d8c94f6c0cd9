import { appContext } from "lib/AppContext";

enum OpLogFld {
  provider_code = "provider_code",
  offer_id = "offer_id",
  revision = "revision",
  op = "op",
  ok = "ok",
  error = "error"
}
export namespace taskpubOplogDAO {
  export type OpLogDTO = {
    provider_code: string,
    offer_id: string,
    revision: Date | null,
    op: String,
    ok: boolean,
    error: String | null
  }
  
  /**
   * Registramos en la tabla "tasks_inmofactory_offerlog" cada operación crear/modificar/borrar y si su resultado a sido ok o error (en caso de error, con el detalle del mensaje de error).
   * 
   * @param offer identificador y revisión de la oferta sobre la que operamos
   * @param op "update"."create" o "delete"
   * @param f función asíncrona que ejecuta la operación y cuyo error, en caso de suceder, es registrado
   */
  export async function insert(data: taskpubOplogDAO.OpLogDTO) {
    const { db } = appContext();
    await db.withTransaction(dbTran =>
      dbTran.query(
        `Insert into 
            tasks_publications_offerlog(
              ${OpLogFld.provider_code}, 
              ${OpLogFld.offer_id}, 
              ${OpLogFld.revision}, 
              ${OpLogFld.op}, 
              ${OpLogFld.ok}, 
              ${OpLogFld.error}
            ) values ($1, $2, $3, $4, $5, $6)`,
        [
          data.provider_code,
          data.offer_id,
          data.revision,
          data.op,
          data.ok,
          data.error
        ]
      )
    )();
  }
}