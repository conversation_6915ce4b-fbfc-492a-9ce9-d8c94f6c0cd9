import { appContext, withAppContext } from "lib/AppContext";

enum PublishedoffersFld {
  provider_code = "provider_code",
  offer_id = "offer_id",
  revision = "revision"
}

export namespace taskpubPublishedofferDAO {
  export type PublishedofferDTO = {
    provider_code: string
    offer_id: string
    revision: Date
  }

  /**
   * Lista de todos los publishedoffers del proveedor
   * @returns 
   */
  export async function list(provider_code: string): Promise<taskpubPublishedofferDAO.PublishedofferDTO[]> {
    const { db } = appContext();
    return db.withClient(dbCli => dbCli.query(
      `
      select 
        ${PublishedoffersFld.offer_id}, 
        ${PublishedoffersFld.revision} 
      from 
        tasks_publications_publishedoffers 
      where 
        ${PublishedoffersFld.provider_code}=$1
      `,
      [provider_code]
    ))().then(result => result.rows.map(row => (
      {
        provider_code,
        offer_id: row[PublishedoffersFld.offer_id] as string,
        revision: row[PublishedoffersFld.revision] as Date
      }
    )));


  }
  /**
  * Registramos la versión publicada en inmofactory
  * @param offer 
  */
  export async function upset(data: taskpubPublishedofferDAO.PublishedofferDTO): Promise<number> {
    return withAppContext(({ db }) => db.withTransaction(async (dbTran) =>

      dbTran.query(
        `
          Insert 
            into tasks_publications_publishedoffers( 
              ${PublishedoffersFld.provider_code}, 
              ${PublishedoffersFld.offer_id}, 
              ${PublishedoffersFld.revision}
            )
            values ($1, $2, $3)
          On conflict(${PublishedoffersFld.provider_code}, ${PublishedoffersFld.offer_id}) Do
            Update Set ${PublishedoffersFld.revision} = EXCLUDED.${PublishedoffersFld.revision}
          `,
        [
          data.provider_code,
          data.offer_id,
          data.revision
        ]
      ).then(result => result.rowCount ?? 0)
    )());
  }
  /**
   * Registramos en la tabla "tasks_inmofactory_offerlog" cada operación crear/modificar/borrar y si su resultado a sido ok o error (en caso de error, con el detalle del mensaje de error).
   * 
   * @param offer identificador y revisión de la oferta sobre la que operamos
   * @param op "update"."create" o "delete"
   * @param f función asíncrona que ejecuta la operación y cuyo error, en caso de suceder, es registrado
   */
  export async function remove(provider_code: string, offer_id: string): Promise<void> {

    await withAppContext(({ db }) =>
      db.withTransaction(dbTran => dbTran.query(
        `
        delete from 
          tasks_publications_publishedoffers 
        where 
          ${PublishedoffersFld.provider_code}=$1 and 
          ${PublishedoffersFld.offer_id}=$2
        `,
        [
          provider_code,
          offer_id
        ]
      ))()
    );
  }

}