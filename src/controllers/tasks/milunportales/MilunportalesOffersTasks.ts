import { get, to<PERSON><PERSON><PERSON> } from "lodash";
import { appContext } from "lib/AppContext";
import { AscOrDesc } from "model/common/AscOrDesc";
import { MediaDTO } from "model/common/dtos/MediaDTO";
import { MultilingualStrDTO } from "model/common/dtos/MultilingualStrDTO";
import { OfferDTO } from "model/common/dtos/OfferDTO";
import { OfferstatusCode } from "model/common/dtos/OfferstatusDTO";
import { PropertyAddressDTO } from "model/common/dtos/PropertyAddressDTO";
import { AirConditioningCode, ConservationStatusCode, ConsumptionLevelCode, EnergyCertificateCode, ExternalJoineryCode, FloorCode, GroundCode, HeatingCode, NoneOwnCommunity, OrientationCode, PropertyStatusCode } from "model/common/dtos/PropertyAttributesDTO";
import { PropertytypeCode } from "model/common/dtos/PropertytypeDTO";
import { OffersModel } from "model/offersModel";
import { PropertymediasModel } from "model/propertymediasModel";
import { milunportales } from "./AdStruct";
import { XmlWriter } from "./lib/XmlWriter";



const exportaciones = [
  { nombre: "1001portales gratuitos", workgroup_id: '7ad4b302-5fb8-11eb-859f-6be55d83bfbc', dst: "1001portales/gratuitos_BnXXhfMjEY8rKu2fEtqXWwvh6pdkRHYkL0zURpGKlaBQ.xml" },
  { nombre: "1001portales yaencontre.com", workgroup_id: '7e51c5c6-7b63-11eb-a6ba-06d99be0472b', dst: "1001portales/yaencontre_com_ZRVevZi3OAGOIh8dgjUwoguY4by5FoWsdomE7WhzqWw.xml" },
  { nombre: "1001portales idealista.com", workgroup_id: '933e993c-7b63-11eb-a6bb-06d99be0472b', dst: "1001portales/idealista_com_RKFH80FXLmkmAsJFwdWS5wNAPeXpiClThjpxBZx5L1w.xml" },
  { nombre: "1001portales pisos.com", workgroup_id: 'acaea93e-7b63-11eb-a6bc-06d99be0472b', dst: "1001portales/pisos_com_ZZBZdoWyJbEoUhD3Ws1uvQKCKXFd2UbwXVtqVBTvZrfw.xml" },
];

export namespace MilunportalesOffersTasks {
  /** 
   * Genera los ficheros JSON con los anuncios de 1001anuncios y los deposita en la carpeta de S3 configurada con ese fin
   */
  export async function publishOffersTask() {
    const { logger, tmpfiles, storages } = appContext();

    for (const exportacion of exportaciones) {
      logger.info("");
      logger.info(`START publishOffersTask:  ${exportacion.nombre}, Generando feed`);
      try {
        await tmpfiles.withTmpFile(".xml", async path => {
          const xml = await XmlWriter.createXml(path);
          await xml.writeChildTag("ads", async (xml) => {
            var offers = await listPublishedOffers(exportacion.workgroup_id, void 0);
            while (offers.length !== 0) {
              for (const offer of offers) {
                try {
                  const ad = await mapOffer(offer);
                  await writeAd(xml, ad);
                  logger.info(`OK: oferta ${offer.id}`);
                } catch (e) {
                  logger.error(`ERR: oferta ${offer.id}`, (e as Error).message ?? e);
                }
              }
              offers = await listPublishedOffers(exportacion.workgroup_id, offers[offers.length - 1].id);
            }
          });
          logger.info("Publicando fichero.");
          const result = await storages.get("feeds")?.uploadFile(path, "text/xml", exportacion.dst);
          logger.info(`Fichero publicado: ${result?.url}`);
        });
      } finally {
        logger.info("END publishOffersTask");
      }
    }
  }

  async function mapOffer(offer: OfferDTO): Promise<milunportales.AdStruct> {
    if (offer.id === void 0) {
      throw new Error("offer id is mandatory!!!");
    }

    const medias = await PropertymediasModel.list({ offer_id: offer.id })().
      then(propertymedias => {
        const propertyimages = propertymedias.filter(pm => pm.media?.original?.mediatype?.startsWith("image") ?? false);
        return [
          // Primero las marcadas como favoritas
          ...propertyimages.filter(pm => pm.isFavourite ?? false),
          // Las marcadas como NO favoritas
          ...propertyimages.filter(pm => !(pm.isFavourite ?? false))
        ].map(pm => pm.media as MediaDTO);
      });
    const attributes = offer.property?.attributes;
    const address = offer.property?.address;
    return {
      id: offer.id,
      reference: `${offer.id}`,
      title: toAdTitle(offer, "es"),
      sales_type: (offer.rent?.allowed ?? false) ? milunportales.TSales.alquiler : milunportales.TSales.venta, // Alquiler o Venta... no hay posibilidad de "nada"
      property_type: toPropertyType(offer.property?.type?.code) ?? doMissing("property_type"),//doException("Tipo de inmueble desconocido"),
      property_category: toPropertyCategory(offer.property?.subtype?.code) ?? toDefaultPropertyCategory(offer.property?.type?.code) ?? doMissing("property_category"), //doException("Subtipo de inmueble desconocido"),
      new_development: attributes?.statusCode === PropertyStatusCode.new,
      zip: toAdZip(address) ?? doMissing("zip", "postcode"),
      ...address?.city?.province?.country?.code ? {
        country: address.city.province.country.code
      } : {},
      ...address?.city?.province?.label ? {
        province: getLabelValue(address.city.province.label, "es")
      } : {},
      ...address?.city?.label ? {
        city: getLabelValue(address.city.label, "es")
      } : {},
      ...offer.property?.zone?.name ? {
        location: offer.property.zone.name
      } : {},
      ...address?.streetname ? {
        street: address.streetname
      } : {},
      show_street: false,
      ...address?.number ? {
        num_street: address.number
      } : {},
      show_num: false,
      ...offer.property?.location ? {
        longitude: offer.property.location.longitude,
        latitude: offer.property.location.latitude
      } : {},
      currency: offer.currency?.code ?? "EUR",
      price: toAdPrice(offer) ?? doMissing("price"),
      ...attributes?.communityFeesAmount && offer.sale?.allowed ? {
        community_charges: attributes.communityFeesAmount
      } : {},
      ...attributes?.totalBedroomsCount ? {
        rooms: attributes.totalBedroomsCount
      } : {},
      ...attributes?.bathroomsCount ? {
        bathroom: attributes.bathroomsCount
      } : {},
      ...attributes?.toiletsCount ? {
        half_bathroom: attributes.toiletsCount
      } : {},
      ...attributes?.constructionYear ? {
        build_year: attributes.constructionYear
      } : {},
      condition: toAdCondition(attributes?.conservationStatusCode) ?? doMissing("condition", "estado de conservación"),
      building_height: toAdBuildingHeight(attributes?.floorCode) ?? doMissing("building_height", "altura real del inmueble"),
      // Petición de Sergio Lago:  La orientación deja de ser un campo obligatorio para ofertas en estado de comercialización.
      //                           Para 1001 portales sí lo es:  forzamos orientación Sur
      orientacion: toAdOrientacion(attributes?.orientationCodes) ?? milunportales.TOrientacion.Sur, // ?? doMissing("orientacion"),
      energy: toAdEnergy(attributes?.energyCertificateCode, attributes?.consumptionLevelCode) ?? doMissing("energy"),
      constructed_area: attributes?.totalSurfaceM2 ?? doMissing("constructed_area", "totalSurfaceM2"),
      ...attributes?.usefulSurfaceM2 ? {
        usefull_area: attributes.usefulSurfaceM2
      } : {},
      ...attributes?.solarSurfaceM2 ? {
        plot_size: attributes.solarSurfaceM2
      } : {},
      ...offer.updated?.at ? {
        date_ad: offer.updated.at
      } : {},
      ...attributes?.airConditioningCode ? {
        air_conditioned: attributes.airConditioningCode !== AirConditioningCode.none
      } : {},
      furnished: attributes?.furnishedIs ?? void 0, // Al ser lógico, asignamos lógico o undefined
      lift: attributes?.elevatorHas ?? void 0,
      balcony: attributes?.balconyHas ?? void 0,
      ...attributes?.heatingCode ? {
        heating: attributes.heatingCode !== HeatingCode.none
      } : attributes?.airConditioningCode ? {
        heating: attributes.airConditioningCode === AirConditioningCode.coldAndHeat
      } : {},
      fireplace: attributes?.fireplaceHas ?? void 0,
      //solar_energy: ...
      washroom: attributes?.buddleHas ?? void 0,
      wooden_aluminium: attributes?.externalJoineryCode === ExternalJoineryCode.aluminium,
      parking: (attributes?.parkingPlacesCount ?? 0) !== 0,
      parquet: (attributes?.groundCodes ?? []).includes(GroundCode.parquet),
      //patio: ...
      ...attributes?.swimmingPoolCode ? {
        swimmingpool: attributes.swimmingPoolCode !== NoneOwnCommunity.none
      } : {},
      terrace: attributes?.terraceHas ?? void 0,
      // wardroves
      spareroom: attributes?.storageRoomHas ?? void 0,
      // doorkeeper
      // common_areas
      // private_security
      // gatekeeper
      securirty_door: attributes?.reinforcedDoorHas ?? void 0,
      ...attributes?.gardenCode ? {
        garden: attributes.gardenCode !== NoneOwnCommunity.none
      } : {},
      // office: ...
      // electrical_appliances
      wheelchair_friendly: attributes?.handicappedAccessibleIs ?? void 0,
      description_es_short: offer?.description ?? void 0,
      description_es: offer?.description ?? void 0,
      //description_en_short
      //description_en
      pictures: medias.
        map(media => media?.publishing?.url ? { url: media.publishing.url } : media?.original?.url ? { url: media.original.url } : void 0).
        filter(m => m) as milunportales.AdPicture[]

    };


    function doException<T>(msg: string): T {
      throw new Error(msg);
    }
    function doMissing<T>(field: string, details?: string): T {
      return doException(`No se puede determinar o falta '${field}'${details !== void 0 ? ` (${details})` : ""}`);
    }


    function toAdEnergy(energyCertificateCode: EnergyCertificateCode | null | undefined, consumptionLevelCode: ConsumptionLevelCode | null | undefined): milunportales.TEnergia {
      switch (energyCertificateCode) {
        case EnergyCertificateCode.inProcess: return milunportales.TEnergia.en_tramite;
        case EnergyCertificateCode.exempt: return milunportales.TEnergia.no_aplicable;
        default:
          switch (consumptionLevelCode) {
            case null:
            case void 0:
              throw new Error(`Missing consumptionLevelCode`);
            case ConsumptionLevelCode.A: return milunportales.TEnergia.A;
            case ConsumptionLevelCode.B: return milunportales.TEnergia.B;
            case ConsumptionLevelCode.C: return milunportales.TEnergia.C;
            case ConsumptionLevelCode.D: return milunportales.TEnergia.D;
            case ConsumptionLevelCode.E: return milunportales.TEnergia.E;
            case ConsumptionLevelCode.F: return milunportales.TEnergia.F;
            case ConsumptionLevelCode.G: return milunportales.TEnergia.G;
            default:
              throw new Error(`Unknown ennergy certificate code "${energyCertificateCode}"`);
          }
      }
    }

    function toAdOrientacion(orientationCodes: OrientationCode[] | null | undefined): milunportales.TOrientacion | undefined {
      if (orientationCodes === null || orientationCodes === void 0 || orientationCodes.length === 0) {
        return void 0;
      } else {
        switch (orientationCodes[0]) {
          case OrientationCode.north: return milunportales.TOrientacion.Norte;
          case OrientationCode.south: return milunportales.TOrientacion.Sur;
          case OrientationCode.east: return milunportales.TOrientacion.Este;
          case OrientationCode.west: return milunportales.TOrientacion.Oeste;
          case OrientationCode.northeast: return milunportales.TOrientacion.Noreste;
          case OrientationCode.northwest: return milunportales.TOrientacion.Noroeste;
          case OrientationCode.southeast: return milunportales.TOrientacion.Sureste;
          case OrientationCode.southwest: return milunportales.TOrientacion.Suroeste;
          default:
            throw new Error(`Código de orientación "${orientationCodes[0]}" desconocido`);
        }
      }

    }
    function toAdBuildingHeight(floor: FloorCode | null | undefined): milunportales.TAltura | undefined {
      switch (floor) {
        case null:
        case void 0:
          return void 0;
        //throw new Error("Missing floorCode");
        case FloorCode.basement:
          return milunportales.TAltura.sotano;
        case FloorCode.semibasement:
          return milunportales.TAltura.semisotano;
        case FloorCode.mezzanine:
          return milunportales.TAltura.entresuelo;
        case FloorCode.main:
          return milunportales.TAltura.principal;
        case FloorCode.ground:
          return milunportales.TAltura.planta_baja;
        case FloorCode.f1:
          return milunportales.TAltura.p1;
        case FloorCode.f2:
          return milunportales.TAltura.p2;
        case FloorCode.f3:
          return milunportales.TAltura.p3;
        case FloorCode.f4:
          return milunportales.TAltura.p4;
        case FloorCode.f5:
          return milunportales.TAltura.p5;
        case FloorCode.f6:
          return milunportales.TAltura.p6;
        case FloorCode.f7:
          return milunportales.TAltura.p7;
        case FloorCode.f8:
          return milunportales.TAltura.p8;
        case FloorCode.f9:
          return milunportales.TAltura.p9;
        case FloorCode.f10:
          return milunportales.TAltura.p10;
        case FloorCode.f11:
          return milunportales.TAltura.p11;
        case FloorCode.f12:
          return milunportales.TAltura.p12;
        case FloorCode.f13:
        case FloorCode.f14:
        case FloorCode.f15:
        case FloorCode.f16:
        case FloorCode.f17:
        case FloorCode.f18:
        case FloorCode.f19:
        case FloorCode.f20:
          return milunportales.TAltura.encima_12;
        default: // Ej: no hay mapeo para "penthouse"
          return milunportales.TAltura.encima_12;
      }
    }
    function toPropertyType(propertyTypeCode: PropertytypeCode | undefined): milunportales.TInmueble {
      switch (propertyTypeCode) {
        case "house": return milunportales.TInmueble.casas;
        case "flat": return milunportales.TInmueble.pisos;
        default: return milunportales.TInmueble.otros;
      }
    }
    function toAdZip(address: PropertyAddressDTO | undefined): string | undefined {
      if (!address?.postcode) {
        return void 0;
      } else {
        return address.postcode;
      }
    }
    function toAdPrice(offer: OfferDTO): number | undefined {
      const amount = offer.sale?.amount ?? offer.rent?.amount;
      if (!amount) {
        return void 0;
      } else {
        return toNumber(amount);
      }
    }
    function toDefaultPropertyCategory(propertyTypeCode: PropertytypeCode | undefined): milunportales.TPisosCategoria | milunportales.TCasasCategoria | undefined {
      switch (propertyTypeCode) {
        case "flat": return milunportales.TPisosCategoria.piso;
        case "house": return milunportales.TCasasCategoria.casa_chalet;
        default: return void 0
      }
    }
    function toPropertyCategory(propertySubtypeCode: string | undefined): milunportales.TPisosCategoria | milunportales.TCasasCategoria | undefined {
      switch (propertySubtypeCode) {
        case "1": //	"flat"	"{""default"":""Semisótano""}"
          return milunportales.TPisosCategoria.piso
        case "2": //	"flat"	"{""default"":""Triplex""}"
          return milunportales.TPisosCategoria.duplex;
        case "3": //	"flat"	"{""default"":""Dúplex""}"
          return milunportales.TPisosCategoria.duplex;
        case "4": //	"flat"	"{""default"":""Buhardilla""}"
          return milunportales.TPisosCategoria.estudio;
        case "5": //	"flat"	"{""default"":""Ático""}"
          return milunportales.TPisosCategoria.atico;
        case "6": //	"flat"	"{""default"":""Estudio""}"
          return milunportales.TPisosCategoria.estudio;
        case "7": //	"flat"	"{""default"":""Loft""}"
          return milunportales.TPisosCategoria.loft;
        case "8": //	"flat"	"{""default"":""Otro""}"
          return milunportales.TPisosCategoria.piso;
        case "9": //	"flat"	"{""default"":""Piso""}"
          return milunportales.TPisosCategoria.piso;
        case "10": //	"flat"	"{""default"":""Apartamento""}"
          return milunportales.TPisosCategoria.apartamento;
        case "11": //	"flat"	"{""default"":""Planta baja""}"
          return milunportales.TPisosCategoria.piso;
        case "13": //	"house"	"{""default"":""Casa""}"
          return milunportales.TCasasCategoria.casa_chalet;
        case "14": //	"house"	"{""default"":""Cortijo""}"
          return milunportales.TCasasCategoria.casa_de_campo_o_masia;
        case "17": //	"house"	"{""default"":""Adosada""}"
          return milunportales.TCasasCategoria.adosada;
        case "18": //	"house"	"{""default"":""Caserio""}"
          return milunportales.TCasasCategoria.casa_de_campo_o_masia;
        case "19": //	"house"	"{""default"":""Pareada""}"
          return milunportales.TCasasCategoria.adosada;
        case "20": //	"house"	"{""default"":""Chalet/Torre""}"
          return milunportales.TCasasCategoria.casa_chalet;
        case "21": //	"house"	"{""default"":""Masía""}"
          return milunportales.TCasasCategoria.casa_de_campo_o_masia;
        case "23": //	"house"	"{""default"":""Unifamiliar""}"
          return milunportales.TCasasCategoria.casa_chalet;
        case "24": //	"house"	"{""default"":""Casa rústica""}"
          return milunportales.TCasasCategoria.casa_de_pueblo;
        case "25": //	"house"	"{""default"":""Casa de pueblo""}"
          return milunportales.TCasasCategoria.casa_de_pueblo;
        case "26": //	"house"	"{""default"":""Casa rural""}"
          return milunportales.TCasasCategoria.casa_de_pueblo;
        case "27": //	"house"	"{""default"":""Bungalow""}"
          return milunportales.TCasasCategoria.casa_chalet;
        case "28": //	"house"	"{""default"":""Casona""}"
          return milunportales.TCasasCategoria.casa_de_campo_o_masia;
        default:
          return void 0;
      }
    }

    function toAdCondition(conservation: ConservationStatusCode | null | undefined): milunportales.TCondicion | undefined {

      switch (conservation) {
        //case null:
        //case void 0:
        //  throw new Error("Se necesita un estado de conservación");
        case ConservationStatusCode.good:
          return milunportales.TCondicion.bueno;
        case ConservationStatusCode.mint:
          return milunportales.TCondicion.a_estrenar;
        case ConservationStatusCode.reformed:
          return milunportales.TCondicion.reformado;
        case ConservationStatusCode.to_reform:
          return milunportales.TCondicion.a_reformar;
        default:
          return void 0; //throw new Error("Estado de conservación desconocido")
      }


    }

    function toAdTitle(offer: OfferDTO, lang = "es"): string {
      var result = [];

      const propertySubtype = getLabelValue(offer.property?.subtype?.label, lang);
      if (propertySubtype) {
        result.push(`${propertySubtype}`);
      } else {
        const propertyType = getLabelValue(offer.property?.type?.label, lang);
        if (propertyType) {
          result.push(propertyType);
        }
      }

      const offerType = lang == "en" ? (
        (offer.rent?.allowed ?? false) ? "for rent" :
          (offer.sale?.allowed ?? false) ? "for sale"
            : ""
      ) : (
        (offer.rent?.allowed ?? false) ? "en alquiler" :
          (offer.sale?.allowed ?? false) ? "en venta" :
            ""
      );
      if (offerType) {
        result.push(`${offerType}`)
      }
      const cityName = getLabelValue(address?.city?.label, lang);
      if (cityName) {
        result.push(
          lang == "en" ? `in ${cityName}` : `en ${cityName}`
        )
      }

      return result.filter(token => !!token).join(" ");

    }

    function getLabelValue(label: MultilingualStrDTO | undefined, lang: string) {
      if (!label)
        return "";
      else
        return get(label, lang) ?? get(label, "default") ?? "";
    }
  }
  async function writeAd(xml: XmlWriter, ad: milunportales.AdStruct) {
    await xml.writeChildTag("ad", async (xml): Promise<void> => {
      await xml.writeCDATATag("id", ad.id);
      await xml.writeCDATATag("reference", ad.reference);
      await xml.writeCDATATag("title", ad.title);
      await xml.writeCDATATag("title_en", ad.title_en);
      await xml.writeValueTag("sales_type", ad.sales_type?.toString() ?? "");
      await xml.writeValueTag("property_type", ad.property_type.toString());
      await xml.writeValueTag("property_category", ad.property_category.toString());
      await xml.writeCDATATag("new_development", ad.new_development);
      await xml.writeCDATATag("zip", ad.zip);
      await xml.writeCDATATag("country", ad.country);
      await xml.writeCDATATag("province", ad.province);
      await xml.writeCDATATag("city", ad.city);
      await xml.writeCDATATag("location", ad.location);
      await xml.writeCDATATag("street", ad.street);
      await xml.writeValueTag("show_street", ad.show_street);
      await xml.writeCDATATag("num_street", ad.num_street);
      await xml.writeCDATATag("num_floor", ad.num_floor);
      await xml.writeCDATATag("num_door", ad.num_door);
      await xml.writeValueTag("show_num", ad.show_num);
      await xml.writeCDATATag("longitude", ad.longitude);
      await xml.writeCDATATag("latitude", ad.latitude);
      await xml.writeValueTag("currency", ad.currency);
      await xml.writeCDATATag("price", ad.price);
      if (ad.sales_type === milunportales.TSales.alquiler)
        await xml.writeCDATATag("housing_deposit", ad.housing_deposit);
      if (ad.sales_type === milunportales.TSales.alquiler)
        await xml.writeCDATATag("guarantee", ad.guarantee);
      if (ad.sales_type === milunportales.TSales.alquiler)
        await xml.writeCDATATag("purchase_option", ad.purchase_option);
      if (ad.sales_type === milunportales.TSales.venta)
        await xml.writeCDATATag("price_purchase_option", ad.price_purchase_option);
      if (ad.sales_type === milunportales.TSales.venta)
        await xml.writeCDATATag("community_charges", ad.community_charges);
      if (ad.sales_type === milunportales.TSales.venta)
        await xml.writeCDATATag("tenant", ad.tenant);
      if (ad.sales_type === milunportales.TSales.venta)
        await xml.writeCDATATag("other_expenses", ad.other_expenses);
      if (ad.sales_type === milunportales.TSales.venta)
        await xml.writeCDATATag("town_taxes", ad.town_taxes);
      await xml.writeCDATATag("rooms", ad.rooms);
      await xml.writeCDATATag("bathroom", ad.bathroom);
      await xml.writeCDATATag("half_bathroom", ad.half_bathroom);
      await xml.writeCDATATag("build_year", ad.build_year);
      await xml.writeCDATATag("condition", ad.condition.toString());
      await xml.writeCDATATag("building_height", ad.building_height.toString());
      await xml.writeCDATATag("orientacion", ad.orientacion.toString());
      await xml.writeCDATATag("energy", ad.energy.toString());
      await xml.writeCDATATag("constructed_area", ad.constructed_area);
      await xml.writeCDATATag("usefull_area", ad.usefull_area);
      if (ad.property_type === milunportales.TInmueble.casas || ad.property_type === milunportales.TInmueble.suelo)
        await xml.writeCDATATag("plot_size", ad.plot_size);
      if (ad.property_type === milunportales.TInmueble.pisos)
        await xml.writeCDATATag("size_terrace", ad.size_terrace);
      await xml.writeCDATATag("date_ad", ad.date_ad);
      await xml.writeCDATATag("url_link", ad.url_link);
      await xml.writeCDATATag("url_video", ad.url_video);
      await xml.writeCDATATag("url_virtualtour", ad.url_virtualtour);
      await xml.writeValueTag("air_conditioned", ad.air_conditioned);
      await xml.writeValueTag("furnished", ad.furnished);
      await xml.writeValueTag("lift", ad.lift);
      await xml.writeValueTag("balcony", ad.balcony);
      await xml.writeValueTag("heating", ad.heating);
      await xml.writeValueTag("kitchen_table", ad.kitchen_table);
      await xml.writeValueTag("fireplace", ad.fireplace);
      await xml.writeValueTag("solar_energy", ad.solar_energy);
      await xml.writeValueTag("washroom", ad.washroom);
      await xml.writeValueTag("wooden_aluminium", ad.wooden_aluminium);
      await xml.writeValueTag("parking", ad.parking);
      await xml.writeValueTag("parquet", ad.parquet);
      await xml.writeValueTag("patio", ad.patio);
      await xml.writeValueTag("swimmingpool", ad.swimmingpool);
      await xml.writeValueTag("wardroves", ad.wardroves);
      await xml.writeValueTag("spareroom", ad.spareroom);
      await xml.writeValueTag("doorkeeper", ad.doorkeeper);
      await xml.writeValueTag("common_areas", ad.common_areas);
      await xml.writeValueTag("private_security", ad.private_security);
      await xml.writeValueTag("gatekeeper", ad.gatekeeper);
      await xml.writeValueTag("securirty_door", ad.securirty_door);
      await xml.writeValueTag("garden", ad.garden);
      await xml.writeValueTag("office", ad.office);
      await xml.writeValueTag("electrical_appliances", ad.electrical_appliances);
      await xml.writeValueTag("wheelchair_friendly", ad.wheelchair_friendly);
      await xml.writeCDATATag("description_es", ad.description_es);
      await xml.writeCDATATag("description_es_short", ad.description_es_short);
      await xml.writeCDATATag("description_en", ad.description_en);
      await xml.writeCDATATag("description_en_short", ad.description_en_short);
      await xml.writeChildTag("pictures", async (xml) => {
        for (const picture of ad.pictures) {
          await xml.writeChildTag("picture", async (xml) => {
            await xml.writeCDATATag("url", picture.url);
            await xml.writeCDATATag("title", picture.title);
          });
        }
      });
    });
  }
  async function listPublishedOffers(workgroup_id: string, lastOfferId: string | undefined): Promise<OfferDTO[]> {
    const filter = {
      sharedInWorkgroupIds: [workgroup_id],
      ...lastOfferId !== void 0 ? { id_greaterThan: lastOfferId } : {},
      status_codes: [OfferstatusCode.commercialization],
      orderBy: { id: AscOrDesc.asc },
      pagination: { limit: 50 }
    };
    const options = {
      includeAgentData: true
    }
    const offers = await OffersModel.list(filter, options)();
    return offers;
  }

}
