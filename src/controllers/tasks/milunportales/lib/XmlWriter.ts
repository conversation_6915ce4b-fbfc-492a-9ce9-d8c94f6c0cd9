import * as fs from "fs";
import { isBoolean, isDate, isNumber, isString } from "lodash";

const { appendFile, writeFile } = fs.promises;


export class XmlWriter {
  private path: string;

  constructor(path: string) {
    this.path = path;
  }

  public static async createXml(path: string): Promise<XmlWriter> {
    await writeFile(path, '<?xml version="1.0" encoding="utf-8"?>');
    return new XmlWriter(path);
  }

  async writeChildTag(name: string, fContent: (xmlwriter: XmlWriter) => Promise<void>) {
    await appendFile(this.path, `<${name}>`);
    try {
      const childWriter = new XmlWriter(this.path);
      await fContent(childWriter);
    } finally {
      await appendFile(this.path, `</${name}>`);
    }
  }

  async writeValueTag(name: string, value: string | boolean | undefined | null) {
    const strValue = isBoolean(value) ? this.buildBooleanString(value as boolean) :
      value ?? "";
    await appendFile(this.path, `<${name}>${strValue}</${name}>`);
  }
  async writeBoolTag(name: string, content: boolean | undefined | null) {
    const sValue = (content === undefined || content === null) ? "" : (content) ? "1" : "0";
    await appendFile(this.path, `<${name}>${sValue}</${name}>`);
  }
  async writeCDATATag(name: string, content: string | Date | boolean | number | undefined | null) {
    let value =
      isDate(content) ? this.buildDateString(content as Date)
        : isString(content) ? content as string
          : isBoolean(content) ? this.buildBooleanString(content as boolean)
            : isNumber(content) ? `${content}`
              : void 0;

    await appendFile(this.path, `<${name}>${this.buildCDATAString(value)}</${name}>`);
  }


  async writeDateTag(name: string, content: Date | undefined | null) {
    await appendFile(this.path, `<${name}>${this.buildDateString(content)}</${name}>`)
  }


  private buildCDATAString(content: string | undefined | null): string {
    return (content === null || content === void 0)
      ? '<![CDATA[]]>'
      : content.split("]]>").map(token => `<![CDATA[${token}]]>`).join("");
  }
  private buildBooleanString(b: boolean | undefined | null): string {
    return b === undefined || b === null ? ""
      : b ? "1" : "0";
  }
  private buildDateString(d: Date | undefined | null): string {
    return (d === null || d === void 0)
      ? ""
      : `${to2Digits(d.getUTCDate())}/${to2Digits(d.getUTCMonth())}/${d.getUTCFullYear()}`;

    function to2Digits(n: number): string {
      return `00${n}`.substr(-2);
    }
  }

}
