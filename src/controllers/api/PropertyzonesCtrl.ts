import { asRest, asValidRequest, withMyAgentId } from "controllers/lib/ctrlUtils";
import { NextFunction, Request, Response } from 'express';
import { withAppContext } from 'lib/AppContext';
import { StringUtils } from 'agentor-lib';
import { PropertyzonesModel } from 'model/propertyzonesModel';
const { parseBool } = StringUtils;

enum ParNames {
  id = "id",
  parent_id = "parent_id",
  include_parent = "include_parent",
  //include_path = "include_path",
  offset = "offset",
  limit = "limit",
}
export namespace PropertyzonesCtrl {

  export function listAct(req: Request, res: Response, next: NextFunction) {
    asRest({ nullAs404: true })(res, next, () => asValidRequest("/api/propertyzones/getPropertyzonesRequest")(req, () => withMyAgentId()(req, _ =>
      PropertyzonesModel.list(parseFilter(req))()
    )));
  }

  export function getPathAct(req: Request, res: Response, next: NextFunction) {
    asRest({ nullAs404: true })(res, next, () => asValidRequest("/api/propertyzones/getPropertyzonesRequest")(req, () => withMyAgentId()(req, myAgent_id =>

      PropertyzonesModel.listPath(req.params["id"] as string)()

    )));
  }

  function parseFilter(req: Request): PropertyzonesModel.Filter {
    const q = req.query;
    const filter: PropertyzonesModel.Filter = {
      ...q[ParNames.id] ? { id: q[ParNames.id] as string } : {},
      ...q[ParNames.parent_id] ? { parent_id: q[ParNames.parent_id] as string } : {},
      // Si no nos filtran por id ni por parent_id... asumimos que solo se listarán los nodos "raíz"
      ...(!q[ParNames.id] && !q[ParNames.parent_id]) ? { parent_id: null } : {},
      ...q[ParNames.include_parent] && parseBool(q[ParNames.include_parent] as string) ? { select: { include_parent: true } } : {},
      // ...q[ParNames.include_path] && parseBool(q[ParNames.include_path] as string) ? { select: { include_path: true } } : {},
      pagination: {
        ...q.offset ? { offset: parseInt(q.offset as string) } : {},
        ...q.limit ? { limit: parseInt(q.limit as string) } : {}
      }
    };
    return filter;
  }
}