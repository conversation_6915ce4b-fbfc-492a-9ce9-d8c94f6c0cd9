import { Request, Response, NextFunction } from 'express'
import { asRest, withMyAgentId } from "controllers/lib/ctrlUtils"
import { EcomAccountsModel } from 'model/ecomaccountsModel';

export function getMyEcomAccountAct(req: Request, res: Response, next: NextFunction) {
  asRest({ nullAs404: true })(res, next, () => withMyAgentId()(req, myAgent_id =>
    EcomAccountsModel.list({ agent_id: myAgent_id })().then(([account]) => account)
  ));
}
