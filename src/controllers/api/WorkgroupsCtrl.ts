import { StringUtils } from 'agentor-lib';
import { asRest, asValidRequest, withMyAgentId } from "controllers/lib/ctrlUtils";
import { NextFunction, Request, Response } from 'express';
import { workgroupsModel } from 'model/workgroupsModel';
const { parseBool } = StringUtils;

export namespace WorkgroupsCtrl {

  export function listAct(req: Request, res: Response, next: NextFunction) {
    asRest({})(res, next, () => asValidRequest("/api/workgroups/getWorkgroupsRequest")(req, () => withMyAgentId()(req, myAgent_id =>

      workgroupsModel.list(parseFilter(req, myAgent_id))()

    )));
  }

}


function parseFilter(req: Request, myAgent_id: string): workgroupsModel.Filter {
  const q = req.query;
  return {
    // Propietario del grupo
    ...q.owner_id !== void 0 ? { owner_id: q.owner_id as string } : {},
    // Identificador del grupo
    ...q.id !== void 0 ? { id: q.id as string } : {},
    accessorCondition: {
      // Agente que accede:  es obligatorio.
      id: myAgent_id,
      // grupos de los que soy propietario
      ...q.include_mine ? { includeMine: parseBool(q.include_mine as string) } : {},
      // grupos de los que no soy propietario
      ...q.include_not_mine ? { includeNotMine: parseBool(q.include_not_mine as string) } : {},
      // grupos delos que puedo leer
      ...q.include_can_read ? { includeCanRead: parseBool(q.include_can_read as string) } : {},
      // grupos en los que puedo publicar
      ...q.include_can_publish ? { includeCanPublish: parseBool(q.include_can_publish as string) } : {},
    },
    pagination: {
      ...q.offset ? { offset: parseInt(q.offset as string) } : {},
      ...q.limit ? { limit: parseInt(q.limit as string) } : {}
    }
  }

}