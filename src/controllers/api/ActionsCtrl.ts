import { ActionBusiness } from 'business/actionBusiness';
import { NextFunction, Request, Response } from 'express';
import { withAppContext } from 'lib/AppContext';
import { StringUtils } from 'agentor-lib';
import { ActionsModel } from "model/actionsModel";
import { AscOrDesc } from 'model/common/AscOrDesc';
import { ContactsModel } from 'model/contactsModel';
import { OffersModel } from 'model/offersModel';
import { asRest, asValidRequest, withMyAgentId } from "controllers/lib/ctrlUtils";
import { withDbTrx } from 'lib/AppContextUtils';

export const getActionsAct = (req: Request, res: Response, next: NextFunction) => void
  asRest({})(res, next, () => asValidRequest("/api/actions/getActionsRequest")(req, () => withMyAgentId()(req, myAgent_id =>
    ActionsModel.list({ ...parseFilter(req), agent_id: myAgent_id })()
  )));


export const getActionAct = (req: Request, res: Response, next: NextFunction) => void
  asRest({ nullAs404: true })(res, next, () => asValidRequest("/api/actions/getActionRequest")(req, () => withMyAgentId()(req, async myAgent_id => {
    const action_id = req.params["action_id"] as string;
    let [action] = await ActionsModel.list({ ...parseFilter(req), id: action_id, agent_id: myAgent_id })();
    return action;
  })));

export const deleteActionAct = (req: Request, res: Response, next: NextFunction) => void
  asRest({ nullAs404: true })(res, next, async () => {
    throw new Error("No implementado");
  });

export const postActionAct = (req: Request, res: Response, next: NextFunction) => void
  asRest({ nullAs404: true })(res, next, () => asValidRequest("/api/actions/postActionRequest")(req, () => withMyAgentId()(req, async myAgent_id =>
    ActionBusiness.createByAgent(myAgent_id, req.body)()
  )));

export const putActionAct = (req: Request, res: Response, next: NextFunction) => void
  asRest({ nullAs404: true })(res, next, () => asValidRequest("/api/actions/putActionRequest")(req, () => withMyAgentId()(req, myAgent_id =>

    withDbTrx(async dbTran => {
      const id = req.params.action_id;
      const data = req.body;
      // 1.- Debemos verificar que las entidades asociadas a la acción (y la propia acción) pertenecen al agente
      if (data.offer !== null && data.offer !== void 0 && data.offer.id != null /* El schema se asegura de que id no sea nulo... pero aún así lo incluimos */) {
        if (0 === await OffersModel.count({ id: data.offer.id, accessorCondition: { id: myAgent_id, includeMine: true, includeNotMine: true } })(dbTran)) {
          return null;
        }
      }
      if (data.contact !== null && data.contact !== void 0 && data.contact.id != null /* El schema se asegura de que id no sea nulo... pero aún así lo incluimos */) {
        if (0 === await ContactsModel.count({ id: data.contact.id, agent_id: myAgent_id })(dbTran)) {
          return null;
        }
      }
      // 2.- Modificamos la acción
      const updatedCount = await ActionsModel.update({
        ...data,
        id
      })(dbTran);
      // Devolvemos el registro modificado
      if (updatedCount !== 0) {
        const [contact] = await ActionsModel.list({ id })(dbTran);
        return contact ?? null;
      }
    })()

  )));

function parseFilter(req: Request): ActionsModel.Filter {
  const q = req.query;

  const detailsSet = new Set(
    (q.details as string || "").split(",").map(s => s.trim())
  );

  return {
    ...q.id ? { id: q.id as string } : {},
    ...q.type_id ? { type_id: q.type_id as string } : {},
    ...q.offer_id ? { offer_id: q.offer_id as string } : {},
    ...q.contact_id ? { contact_id: q.contact_id as string } : {},
    ...q.done ? { done: StringUtils.parseBool(q.done as string) } : {},
    ...q.when_min ? { when_min: new Date(q.when_min as string) } : {},
    ...q.when_max ? { when_max: new Date(q.when_max as string) } : {},
    pagination: {
      ...q.offset ? { offset: parseInt(q.offset as string) } : {},
      ...q.limit ? { limit: parseInt(q.limit as string) } : {}
    },
    orderby: {
      ...q.oby_when ? { when: q.oby_when === "desc" ? AscOrDesc.desc : AscOrDesc.asc } : {}
    },
    details: {
      ...detailsSet.has("offer") ? { offer: true } : {},
      ...detailsSet.has("contact") ? { contact: true } : {}
    }
  };

}
