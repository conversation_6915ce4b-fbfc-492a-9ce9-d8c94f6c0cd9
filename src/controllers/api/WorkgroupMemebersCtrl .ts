import { asRest, asValidRequest, withMyAgentId } from "controllers/lib/ctrlUtils";
import { NextFunction, Request, Response } from 'express';
import { withAppContext } from 'lib/AppContext';
import { StringUtils } from 'agentor-lib';
import { workgroupMembersModel } from 'model/workgroupsMembersModel';
const { parseBool } = StringUtils;

export namespace WorkgroupMembersCtrl {

  export const listAgentAffilitationsAct = (req: Request, res: Response, next: NextFunction) => void

    asRest({})(res, next, () => asValidRequest("/api/workgroupmembers/getAgentAffiliationsRequest")(req, () => withMyAgentId()(req, myAgent_id =>
      workgroupMembersModel.list(parseFilter(req, myAgent_id))()
    )));

}

function parseFilter(req: Request, myAgent_id: string): workgroupMembersModel.Filter {
  const q = req.query;
  return {
    agent_id: myAgent_id,
    include_workgroup_info: true,
    ...q.include_can_publish !== void 0 ? { can_publish: parseBool(q.include_can_publish as string) } : {},
    ...q.include_can_read !== void 0 ? { can_read: parseBool(q.include_can_read as string) } : {},
    pagination: {
      ...q.offset !== void 0 ? { offset: parseInt(q.offset as string) } : {},
      ...q.limit !== void 0 ? { limit: parseInt(q.limit as string) } : {}
    }
  }
}