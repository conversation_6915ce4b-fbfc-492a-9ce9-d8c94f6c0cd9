import { asRest, asValidRequest, withMyAgentId } from "controllers/lib/ctrlUtils";
import { NextFunction, Request, Response } from 'express';
import { StringUtils } from 'agentor-lib'; const {parseBool} = StringUtils;
import { MatchingsModel } from 'model/matchingsModel';

export namespace MatchingsCtrl {

  type ListActQuery = {
    offer_id?: string,
    demand_id?: string,
    offset?: string,
    limit?: string,
    include_demand_data?: string,
    include_offer_data?: string,
    include_mine?: string,
    include_not_mine?: string,
  };

  export const listAct = (req: Request, res: Response, next: NextFunction) =>

    asRest({ nullAs404: true })(res, next, () => asValidRequest("/api/matchings/getMatchingsRequest")(req, () => withMyAgentId()(req, myAgent_id =>
      MatchingsModel.list(
        { ...parseListFilter(req, myAgent_id) },
        parseListOptions(req)
      )()
    )));

  function parseListFilter(req: Request, myAgent_id: string): MatchingsModel.ListFilter {
    const { offer_id, demand_id, include_mine, include_not_mine, offset, limit }: ListActQuery = req.query;
    return {
      ...offer_id ? { offer_id } : {},
      ...demand_id ? { demand_id } : {},
      accessorCondition: {
        id: myAgent_id,
        ...include_mine ? { includeMine: parseBool(include_mine as string) } : { includeMine: true },
        ...include_not_mine ? { includeNotMine: parseBool(include_not_mine as string) } : { includeNotMine: true },
      },
      pagination: {
        ...offset ? { offset: parseInt(offset) } : {},
        ...limit ? { limit: parseInt(limit) } : {}
      }
    };
  }
  function parseListOptions(req: Request): MatchingsModel.ListOptions {
    const { include_demand_data, include_offer_data }: ListActQuery = req.query;
    return {
      ...include_demand_data ? { includeDemandData: parseBool(include_demand_data) } : {},
      ...include_offer_data ? { includeOfferData: parseBool(include_offer_data) } : {},
    };
  }
}