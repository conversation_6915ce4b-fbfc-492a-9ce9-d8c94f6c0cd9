import { asRest, withDecodedAgtAccessToken } from "controllers/lib/ctrlUtils"
import { RestError } from 'controllers/lib/RestError'
import { NextFunction, Request, Response } from 'express'
import { body, validationResult } from "express-validator"
import { withAppContext } from 'lib/AppContext'
import { generateAccessToken } from 'lib/Jwt'
import { AgentsModel } from "model/agentsModel"
import { scrtyCredentialsModel } from "model/scrtyCredentialsModel"
import { scrtyUserModel } from "model/scrtyUserModel"
import { createSession } from "model/sessionsModel"


const DEFAULT_EXPIRATION = 30 * 24 * 3600; // 30 days

export const createTokenAct = [
  body("username").isLength({ min: 5 }),
  body("password").isLength({ min: 3 }),
  (req: Request, res: Response, next: NextFunction) =>
    asRest({})(res, next, () => withAppContext(async ({ db }) => {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const [firstErr] = errors.array();
        throw new RestError({ status: 422, message: `${firstErr.location}[${firstErr.param}]: ${firstErr.msg}` });
      } else {
        let [credentials] = await scrtyCredentialsModel.list({
          username: req.body["username"],
          password: req.body["password"]
        })();
        

        if (credentials && credentials.user?.agent?.id) {
          let {user} = credentials;
          // Solo credenciales de usuarios asociados a un agente
          let token = generateAccessToken({ user_id: user.id, agent_id: user.agent?.id }, DEFAULT_EXPIRATION);
          await db.withTransaction(createSession({ user: user, token }))();
          return token;
        } else {
          throw new RestError({ status: 403, message: "Unknown credentials" });
        }

      }
    }))

];

/**
 * La aplicación cliente usa este método cada vez que el usuario accede a ella para asegurar que el token actual sigue activo.
 * La función devuelve un nuevo token si el actual está cerca de expirar.
 */
export const renewTokenAct = [
  (req: Request, res: Response, next: NextFunction) =>
    asRest({})(res, next, () =>
      withDecodedAgtAccessToken()(req, async (payload, token) => {
        // todo: expirar viejo token en BBDD (dándole unos segundos de margen) y añadir el nuevo token.
        if (payload.exp !== void 0 && lessThanAWeek(payload.exp))
          generateAccessToken({ user_id: payload.user_id, agent_id: payload.agent_id }, DEFAULT_EXPIRATION);
        else
          // Si el token tiene aún vida para 1 semana o más, continuamos usandolo
          return token;
      })
    )
];

function lessThanAWeek(payloadExp: number) {
  let expMS = payloadExp * 1000;
  let nowMS = new Date().getTime();
  let oneWeekMS = 7 * 24 * 3600;
  return (expMS - nowMS) < oneWeekMS;
}