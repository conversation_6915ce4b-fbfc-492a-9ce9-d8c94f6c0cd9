import { Request, Response, NextFunction } from 'express'
import { asRest, withMyAgentId } from "controllers/lib/ctrlUtils"
import { EcomPurchasesModel } from 'model/ecompurchasesModel';
import { AscOrDesc } from 'model/common/AscOrDesc';

export namespace EcomPurchasesCtrl {

  export function listAct(req: Request, res: Response, next: NextFunction) {
    asRest({ nullAs404: true })(res, next, () => withMyAgentId()(req, myAgent_id =>
      EcomPurchasesModel.list(parseFilter(req, myAgent_id))()
    ));
  }

}

// #region private

enum ParNames {
  // Agente que participa sí o sí como comprador o vendedor
  buyer_or_seller_agent_id = "buyer_or_seller_agent_id",
  // Agente que compró el producto
  buyer_agent_id = "buyer_agent_id",
  // Agente que ofrece el producto comprado
  seller_agent_id = "seller_agent_id",
  // Compras asociadas a una oferta
  service_offer_id = "service_offer_id",
  // Producto comprado
  product_id = "product_id",
  oby_date = "oby_date",
  offset = "offset",
  limit = "limit",
}


function parseFilter(req: Request, myAgent_id: string): EcomPurchasesModel.Filter {
  const q = req.query;
  return {
    buyer_or_seller_agent_id: myAgent_id,
    ...q[ParNames.buyer_agent_id] !== void 0 ? {
      buyer_agent_id: q[ParNames.buyer_agent_id] as string
    } : {},
    ...q[ParNames.seller_agent_id] !== void 0 ? {
      product_account_agent_id: q[ParNames.seller_agent_id] as string
    } : {},
    ...q[ParNames.service_offer_id] !== void 0 ? {
      service_offer_id: q[ParNames.service_offer_id] as string
    } : {},
    ...q[ParNames.product_id] !== void 0 ? {
      product_id: q[ParNames.product_id] as string
    } : {},
    include_product: true,
    include_service_offer: true,
    orderBy: {
      ...q[ParNames.oby_date] ? { date: q[ParNames.oby_date] === "desc" ? AscOrDesc.desc : AscOrDesc.asc } : {},
    },
    pagination: {
      ...q.offset ? { offset: parseInt(q.offset as string) } : {},
      ...q.limit ? { limit: parseInt(q.limit as string) } : {}
    }
  }

}
// #endregion