import { asRest, asValidRequest, withMyAgentId } from "controllers/lib/ctrlUtils";
import { NextFunction, Request, Response } from 'express';
import { withAppContext } from 'lib/AppContext';
import { PropertytypesModel } from 'model/propertysubtypesModel';

export namespace PropertytypesCtrl {

  export const getPropertytypesAct = (req: Request, res: Response, next: NextFunction) => void

    asRest({ nullAs404: true })(res, next, () => asValidRequest("/api/propertytypes/getPropertytypesRequest")(req, () => withMyAgentId()(req, _ =>

      PropertytypesModel.list(parseFilter(req))()

    )));

}

function parseFilter(req: Request): PropertytypesModel.Filter {
  return {
    ...req.query.include_subtypes ? {
      select: { include_subtypes: true }
    } : {}
  };
}