import { DemandBusiness } from 'business/demandBusiness'
import { asRest, asValidRequest, withMyAgentId } from "controllers/lib/ctrlUtils"
import { NextFunction, Request, Response } from 'express'
import { param } from "express-validator"
import { StringUtils } from 'agentor-lib'
import { DemandDTO } from 'model/common/dtos/DemandDTO'
import { DemandstatusCode } from 'model/common/dtos/DemandstatusDTO'
import { PropertytypeCode } from 'model/common/dtos/PropertytypeDTO'
import { DemandsModel } from "model/demandsModel"

export namespace DemandsCtrl {

  export const listAct = (req: Request, res: Response, next: NextFunction) =>
    asRest({})(res, next, () =>
      asValidRequest("/api/demands/getDemandsRequest")(req, () =>
        withMyAgentId()(req, myAgent_id =>
          DemandsModel.list({ ...parseFilter(req), agent_id: myAgent_id })()
        )
      )
    );

  export const readAct = [
    param("demand_id").isInt({ allow_leading_zeroes: false }),
    (req: Request, res: Response, next: NextFunction) => {
      asRest({ nullAs404: true })(res, next, () => withMyAgentId()(req, async myAgent_id => {
        let id: string = req.params["demand_id"];
        let [demand] = await DemandsModel.list({ agent_id: myAgent_id, id })();
        return demand;
      }));
    }
  ];

  export const createAct = (req: Request, res: Response, next: NextFunction) =>
    asRest({})(res, next, () =>
      asValidRequest("/api/demands/postDemandRequest")(req, () =>
        withMyAgentId()(req, myAgent_id =>
          DemandBusiness.create(myAgent_id, req.body as DemandDTO)()
        )
      )
    );


  export const updateAct = (req: Request, res: Response, next: NextFunction) =>
    asRest({})(res, next, () =>
      asValidRequest("/api/demands/putDemandRequest")(req, () =>
        withMyAgentId()(req, async myAgent_id => {
          const data = req.body as DemandDTO;
          const id = req.params.demand_id as string;

          const updated = await DemandBusiness.update(myAgent_id, id, data)();
          return updated;
        })
      )
    );


}



function parseFilter(req: Request): DemandsModel.Filter {
  const q = req.query;

  return <DemandsModel.Filter>{
    ...q.id !== void 0 ? { id: q.id as string } : {},
    ...q.customer_id !== void 0 ? { customer_id: q.customer_id as string } : {},
    ...q.agent_id !== void 0 ? { agent_id: q.agent_id as string } : {},
    ...q.status_codes ? { status_codes: `${q.status_codes}`.split(",").map(s => DemandstatusCode.parse(s.trim())) } : {},
    ...q.property_type_code ? { property_type_code: PropertytypeCode.parse(q.property_type_code as string) } : {},
    ...q.sale_allowed !== void 0 ? { sale_allowed: StringUtils.parseBool(q.sale_allowed as string) } : {},
    ...q.rent_allowed !== void 0 ? { rent_allowed: StringUtils.parseBool(q.rent_allowed as string) } : {},
    ...q.sale_amount_min !== void 0 ? { sale_amount_min: parseFloat(q.sale_amount_min as string) } : {},
    ...q.sale_amount_max !== void 0 ? { sale_amount_max: parseFloat(q.sale_amount_max as string) } : {},
    ...q.rent_amount_min !== void 0 ? { rent_amount_min: parseFloat(q.rent_amount_min as string) } : {},
    ...q.rent_amount_max !== void 0 ? { sale_amount_min: parseFloat(q.rent_amount_max as string) } : {},
    ...q.property_m2_min !== void 0 ? { property_m2_min: parseFloat(q.property_m2_min as string) } : {},
    ...q.property_m2_max !== void 0 ? { property_m2_max: parseFloat(q.property_m2_max as string) } : {},
    ...q.property_address_city_code !== void 0 ? { property_address_city_code: q.property_address_city_code as string } : {},

    pagination: {
      ...q.offset ? { offset: parseInt(q.offset as string) } : {},
      ...q.limit ? { limit: parseInt(q.limit as string) } : {}
    },

  }

}

/*
      if (filter.statuses != null) "status_codes": (filter.statuses ?? {}).map((e) => e.enumToString()).join(","),
      if (filter.demandId is! None) "id": filter.demandId.v,
      if (filter.customerId is! None) "customer_id": filter.customerId.v,
      if (filter.propertytypeCode is! None) "property_type_code": filter.propertytypeCode.v,
      if (filter.rentAllowed is! None) "rent_allowed": filter.rentAllowed.v,
      if (filter.rentAmountMin is! None) "rent_amount_min": filter.rentAmountMin.v,
      if (filter.rentAmountMax is! None) "rent_amount_max": filter.rentAmountMax.v,
      if (filter.saleAllowed is! None) "sale_allowed": filter.saleAllowed.v,
      if (filter.saleAmountMin is! None) "sale_amount_min": filter.saleAmountMin.v,
      if (filter.saleAmountMax is! None) "sale_amount_max": filter.saleAmountMax.v,
      if (filter.propertyM2Min is! None) "property_m2_min": filter.propertyM2Min.v,
      if (filter.propertyM2Max is! None) "property_m2_max": filter.propertyM2Max.v,
      if (filter.propertyAddrCityCode is! None) "property_address_city_code": filter.propertyAddrCityCode.v,
      "offset": offset,
      "limit": limit
      */