/// 
/// 
///   Métodos asociados al servicio de whatsapp
/// 
///

import { NextFunction, Request, Response } from 'express';
import { asRest, asValidRequest, withMyUserId } from "controllers/lib/ctrlUtils";
import { RestError, ErrUtils } from 'agentor-lib';
import { scrtyUserModel } from 'model/scrtyUserModel';
import { WhatasppProvidersModel } from 'model/whatasppProvidersModel';
import got from 'got/dist/source';
import { appContext } from 'lib/AppContext';
import { WaApiSessionDTO } from 'model/common/dtos/WaApiSessionDTO';


type createWaTokenResult = {
  access_token: string,
  token_type: "Bearer",
  expires_in: number
};

export namespace WaSrvCtrl {
  /**
   * Crear un toquen de acceso al servicio whatsapp.
   *   Las credenciales del servicio están disponibles en 
   * @param req 
   * @param res 
   * @param next 
   * @returns 
   */
  export const createToken = (req: Request, res: Response, next: NextFunction) =>
    asRest({})(res, next, () => asValidRequest("/api/wasrv/postTokenRequest")(req, () => withMyUserId()(req,
      async user_id => {
        const agent_id = await scrtyUserModel.getAgentId(user_id)() ?? ErrUtils.doThrow(RestError.forbidden());
        const provider = await WhatasppProvidersModel.read(agent_id)() ?? ErrUtils.doThrow(RestError.badRequest({ message: "User cant access whatsapp service" }));

        const baseUrl = provider.api?.url ?? ErrUtils.doThrow(RestError.internal({ message: "Missing api url" }));
        const response = await got.post(`${baseUrl}oauth/tokens`, {
          form: {
            grant_type: "client_credentials",
            scope: req.query.scope ?? ""
          },
          username: provider.api!.userId!,
          password: provider.api!.userSecret,
          responseType: "json",
        });
        if (response.statusCode === 200) {
          const result = response.body as createWaTokenResult;
          return {
            access_token: result.access_token,
            token_type: result.token_type,
            api_url: baseUrl
          } as  WaApiSessionDTO;
        } else {
          appContext().logger.error(`Problemas obteniendo credenciales de whatsapp ${JSON.stringify({ statusCode: response.statusCode, body: response.body })}`);
          throw new Error(`ERROR (${response.statusCode}) obteniendo credenciales de whatsapp`)
        }
      }
    )));
}
