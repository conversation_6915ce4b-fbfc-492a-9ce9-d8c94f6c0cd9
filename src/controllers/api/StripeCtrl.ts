import { asRest, asValidRequest, withMyAgentId } from "controllers/lib/ctrlUtils";
import { NextFunction, Request, Response } from 'express';
import { withAppContext } from 'lib/AppContext';
import { ErrUtils } from 'agentor-lib';
import { stripeCheckoutsessionsModel } from 'model/stripeCheckoutsessionsModel';

export namespace StripeCtrl {

  export const createCheckoutSessionAct = (req: Request, res: Response, next: NextFunction) => void

    asRest({})(res, next, () => asValidRequest("/api/stripe/postChechkoutsessionRequest")(req, () => withMyAgentId()(req, myAgent_id =>
      withAppContext(async ({ stripeWrapper, db }) => {
        const { priceCode, success_url, cancel_url } = req.body;
        const stripeSession = await stripeWrapper.createCheckout(priceCode, success_url, cancel_url);

        await stripeCheckoutsessionsModel.create({
          id: stripeSession.id,
          agent: { id: myAgent_id },
          data: stripeSession,
          total: (stripeSession.amount_total ?? ErrUtils.doThrowError("Unexpected error: stripeSession without amount_total")) / 100,
        })();

        return { id: stripeSession.id };
      })
    )));

  export const getStripeConfAct = (req: Request, res: Response, next: NextFunction) => void

    asRest({})(res, next, () => withAppContext(async ({ stripeWrapper }) => (
      {
        publicKey: stripeWrapper.getPublishableKey(),
      }
    )));

}