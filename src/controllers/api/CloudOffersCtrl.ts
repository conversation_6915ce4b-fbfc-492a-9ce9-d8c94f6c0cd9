import { Request, Response, NextFunction }
  from 'express'
import { withMyAgentId, asRest, asValidRequest }
  from "controllers/lib/ctrlUtils"
import { withAppContext }
  from "lib/AppContext";
import { StringUtils }
  from 'agentor-lib';
import { OfferstatusCode }
  from 'model/common/dtos/OfferstatusDTO';
import { OffersModel }
  from "model/offersModel";
import { AscOrDesc }
  from 'model/common/AscOrDesc';
import { PropertytypeCode }
  from 'model/common/dtos/PropertytypeDTO';

export namespace Offers_CloudVwCtrl {

  enum ParNames {
    status_codes = "status_codes",
    source_individual = "source_individual",
    containerzone_id = "containerzone_id",
    property_type_code = "property_type_code",
    created_at_min = "created_at_min",
    created_at_max = "created_at_max",
    sale_allowed = "sale_allowed",
    rent_allowed = "rent_allowed",
    sale_amount_min = "sale_amount_min",
    sale_amount_max = "sale_amount_max",
    rent_amount_min = "rent_amount_min",
    rent_amount_max = "rent_amount_max",
    property_m2_min = "property_m2_min",
    property_m2_max = "property_m2_max",
    property_address_city_code = "property_address_city_code",
    source_contact_phone_contains = "source_contact_phone_contains",
    include_favourites = "include_favourites",
    include_not_favourites = "include_not_favourites",
    oby_created_at = "oby_created_at",
    oby_amount = "oby_amount",
    offset = "offset",
    limit = "limit",
  }

  export function listAct(req: Request, res: Response, next: NextFunction) {
    asRest({})(res, next, () => asValidRequest("/api/offers/getCloudOffersRequest")(req, () => withMyAgentId()(req, myAgent_id =>
      OffersModel.Cloud.list(parseFilter(req, myAgent_id), {})()
    )));
  }


  function parseFilter(req: Request, myAgent_id: string): OffersModel.Cloud.Filter {
    const q = req.query;
    const filter: OffersModel.Cloud.Filter = {};

    if (q[ParNames.status_codes])
      filter.status_codes = `${q[ParNames.status_codes]}`.split(",").map(s => OfferstatusCode.parse(s.trim()));
    if (q[ParNames.source_individual] !== void 0)
      filter.source_announcedByAnIndividual = StringUtils.parseBool(q[ParNames.source_individual] as string)
    if (q[ParNames.source_contact_phone_contains] !== void 0)
      filter.source_contact_phone_contains = (q[ParNames.source_contact_phone_contains] as string).replace(/\s+/g, '');
    if (q[ParNames.containerzone_id])
      filter.containerzone_id = q[ParNames.containerzone_id] as string
    if (q[ParNames.property_type_code]) // Excluimos "" y undefined
      filter.property_type_code = PropertytypeCode.parse(q[ParNames.property_type_code] as string);
    if (q[ParNames.created_at_min])
      filter.created_at_min = StringUtils.parseDate(q[ParNames.created_at_min] as string);
    if (q[ParNames.created_at_max])
      filter.created_at_max = StringUtils.parseDate(q[ParNames.created_at_max] as string);
    if (q[ParNames.sale_allowed] !== void 0)
      filter.sale_allowed = StringUtils.parseBool(q[ParNames.sale_allowed] as string);
    if (q[ParNames.rent_allowed] !== void 0)
      filter.rent_allowed = StringUtils.parseBool(q[ParNames.rent_allowed] as string);
    if (q[ParNames.sale_amount_min] !== void 0)
      filter.sale_amount_min = parseFloat(q[ParNames.sale_amount_min] as string);
    if (q[ParNames.sale_amount_max] !== void 0)
      filter.sale_amount_max = parseFloat(q[ParNames.sale_amount_max] as string);
    if (q[ParNames.rent_amount_min] !== void 0)
      filter.rent_amount_min = parseFloat(q[ParNames.rent_amount_min] as string);
    if (q[ParNames.rent_amount_max] !== void 0)
      filter.sale_amount_min = parseFloat(q[ParNames.rent_amount_max] as string);
    if (q[ParNames.property_m2_min] !== void 0)
      filter.property_m2_min = parseFloat(q[ParNames.property_m2_min] as string);
    if (q[ParNames.property_m2_max] !== void 0)
      filter.property_m2_max = parseFloat(q[ParNames.property_m2_max] as string);
    if (q[ParNames.property_address_city_code] !== void 0)
      filter.property_address_city_code = q[ParNames.property_address_city_code] as string;
    filter.accessorCondition = {
      id: myAgent_id,
      ...q[ParNames.include_favourites] ? { includeFavourites: StringUtils.parseBool(q[ParNames.include_favourites] as string) } : {},
      ...q[ParNames.include_not_favourites] ? { includeNotFavourites: StringUtils.parseBool(q[ParNames.include_not_favourites] as string) } : {},
    };
    filter.pagination = {
      ...q[ParNames.offset] ? { offset: parseInt(q[ParNames.offset] as string) } : {},
      ...q[ParNames.limit] ? { limit: parseInt(q[ParNames.limit] as string) } : {}
    };
    filter.orderBy = {
      ...q[ParNames.oby_created_at] ? { created_at: q[ParNames.oby_created_at] === "desc" ? AscOrDesc.desc : AscOrDesc.asc } : {},
      ...q[ParNames.oby_amount] == void 0 ? {} :
        filter.sale_allowed && !filter.rent_allowed ? {
          sale_amount: q[ParNames.oby_amount] === "desc" ? AscOrDesc.desc : AscOrDesc.asc
        } : !filter.sale_allowed && filter.rent_allowed ? {
          rent_amount: q[ParNames.oby_amount] === "desc" ? AscOrDesc.desc : AscOrDesc.asc
        } : {
          // Si se adminte alquiler y venta a la vez... no sabemos ordenar :-/
        },
    }

    return filter;

  }
}