import { RestError } from "controllers/lib/RestError";
import { withApi<PERSON>eyUser } from "controllers/lib/ctrlApiKeyUtils";
import { Request } from "express";
import { ScrtyUserDTO } from "model/common/dtos/ScrtyUserDTO";
import { csiteSitesModel } from "model/csiteSiteModel";

export declare type FAgentIdAndContactId<T> = (agent_id: string, contact_id: string) => Promise<T>;
/**
 * API basada en ApiKey:
 * 
 * Extrae el agente y el contacto correspondiente al slug de la URL  (csite api).
 * El agente debe ser el del usuario que está usando la API (Access Token -> ApiKey -> UserId (e)
 * El agente debe ser el del usuario que accede usando el access token (basado en ApiKey)
 * Revisa el access token correspondiente a una API basada en ApiKey y deduce el usuario asociado.
 * Si la Apikey no existe se devuelve un error 503
 * @returns 
 */
export function withCsiteApiAgentAndContact<T>(): ((req: Request, fAgentAndContact: FAgentIdAndContactId<T>) => Promise<T>) {
  const MSG_UnkownContactSite = "Unknown site (Check site slug)";
  return (req:Request, fAgentAndContact: FAgentIdAndContactId<T>):Promise<T> =>
    withApiKeyUser<T>({ agent_is_required: true })(req, async (usr: ScrtyUserDTO) => {
      const site_slug = req.params.site_slug;
      
      const agent_id = usr.agent!.id!;
      if (typeof site_slug !== "string" || site_slug.trim().length === 0)
        throw new RestError({ status: 404, message: MSG_UnkownContactSite });
      
        const [site] = await csiteSitesModel.list({ contact_agent_id: agent_id, slug: site_slug, pagination: { limit: 1 } })();
      if (!(site?.contact?.id))
        throw new RestError({ status: 404, message: MSG_UnkownContactSite });

      return fAgentAndContact(agent_id, site.contact.id);
    });

}

