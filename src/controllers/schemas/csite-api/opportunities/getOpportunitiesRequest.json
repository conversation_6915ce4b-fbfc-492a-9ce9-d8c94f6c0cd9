{"type": "object", "properties": {"params": {"type": "object", "properties": {"site_slug": {"$ref": "/common/notempty_string"}}, "required": ["site_slug"]}, "query": {"type": "object", "properties": {"contact_site_slug": {"$ref": "/common/notempty_string"}, "contact_id": {"$ref": "/common/notempty_integer_string"}, "reaction_likedit": {"$ref": "/common/notempty_boolean_string"}, "created_ix_gt": {"$ref": "/common/notempty_integer_string"}, "details": {"type": "string", "pattern": "^$|^(offer|contact)(,(offer|contact)){0,1}$"}, "oby": {"enum": ["created_ix_asc", "created_ix_desc"]}, "pag_offset": {"$ref": "/common/notempty_integer_string"}, "pag_limit": {"$ref": "/common/notempty_integer_string"}}}}, "required": ["params"]}