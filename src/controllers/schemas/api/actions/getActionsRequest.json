{"type": "object", "properties": {"query": {"type": "object", "properties": {"done": {"$ref": "/common/notempty_boolean_string"}, "type_id": {"$ref": "/common/notempty_positive_number_string"}, "offer_id": {"$ref": "/common/notempty_positive_number_string"}, "contact_id": {"$ref": "/common/notempty_positive_number_string"}, "when_min": {"type": "string", "format": "date-time"}, "when_max": {"type": "string", "format": "date-time"}, "oby_when": {"enum": ["asc", "desc"]}, "offset": {"$ref": "/common/notempty_positive_number_string"}, "limit": {"$ref": "/common/notempty_positive_number_string"}, "details": {"type": "string", "pattern": "^$|^(offer|contact)(,(offer|contact)){0,1}$"}}, "required": [], "additionalProperties": false}}, "required": ["query"]}