{"type": "object", "properties": {"query": {"type": "object", "properties": {"id": {"$ref": "/common/notempty_string"}, "parent_id": {"$ref": "/common/notempty_string"}, "include_parent": {"$ref": "/common/notempty_boolean_string"}, "offset": {"$ref": "/common/notempty_positive_number_string"}, "limit": {"$ref": "/common/notempty_positive_number_string"}}, "required": [], "additionalProperties": false}}, "required": ["query"]}