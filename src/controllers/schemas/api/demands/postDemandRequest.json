{"type": "object", "properties": {"body": {"type": "object", "properties": {"status": {"$ref": "/common/object_codedkey"}, "customer": {"$ref": "/common/nullable_object_idedkey"}, "property": {"$ref": "/common/offers/propertyUp"}, "sale": {"type": "object", "properties": {"allowed": {"type": "boolean"}, "amount": {"oneOf": [{"$ref": "/common/positive_number"}, {"$ref": "/common/notempty_positive_number_string"}, {"type": "null"}]}}, "additionalProperties": false}, "rent": {"type": "object", "properties": {"allowed": {"type": "boolean"}, "amount": {"oneOf": [{"$ref": "/common/positive_number"}, {"$ref": "/common/notempty_positive_number_string"}, {"type": "null"}]}}, "additionalProperties": false}, "currency": {"$ref": "/common/nullable_object_codedkey"}, "notes": {"$ref": "/common/nullable_string"}}, "required": [], "additionalProperties": false}}, "required": ["params", "body"]}