{"type": "object", "properties": {"query": {"type": "object", "properties": {"search": {"type": "string"}, "mobile": {"type": "string"}, "isDemandCustomer": {"$ref": "/common/notempty_boolean_string"}, "isOfferCustomer": {"$ref": "/common/notempty_boolean_string"}, "isBankServicer": {"$ref": "/common/notempty_boolean_string"}, "hasSite": {"$ref": "/common/notempty_boolean_string"}, "offset": {"$ref": "/common/notempty_positive_number_string"}, "limit": {"$ref": "/common/notempty_positive_number_string"}}, "required": []}}, "required": ["params", "query"]}