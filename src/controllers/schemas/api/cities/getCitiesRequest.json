{"type": "object", "properties": {"query": {"type": "object", "properties": {"code": {"$ref": "/common/notempty_string"}, "province_code": {"$ref": "/common/notempty_string"}, "search": {"type": "string"}, "in_use": {"$ref": "/common/notempty_boolean_string"}, "used_by": {"enum": ["me", "me_and_coworkers"]}, "offset": {"$ref": "/common/notempty_positive_number_string"}, "limit": {"$ref": "/common/notempty_positive_number_string"}}, "required": []}}, "required": ["query"]}