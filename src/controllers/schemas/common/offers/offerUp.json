{"type": "object", "properties": {"id": {"$comment": "Not necessary, if specified, then it will be validated", "$ref": "/common/notempty_integer_string"}, "urgency": {"oneOf": [{"type": "number", "minimum": 0, "maximum": 3}, {"type": "null"}]}, "version": {"oneOf": [{"type": "object", "properties": {"disclaimer": {"type": "string"}}, "required": [], "additionalProperties": false}, {"type": "null"}]}, "customer": {"$ref": "/common/nullable_object_idedkey"}, "status": {"$ref": "/common/object_codedkey"}, "mandate": {"oneOf": [{"type": "null"}, {"$ref": "/common/offers/offer_mandate"}]}, "historic": {"oneOf": [{"type": "null"}, {"$ref": "/common/offers/offer_historic"}]}, "property": {"$ref": "/common/offers/propertyUp"}, "sale": {"$ref": "/common/offers/saleCrUp"}, "rent": {"$ref": "/common/offers/rentCrUp"}, "currency": {"$ref": "/common/object_codedkey"}, "notes": {"$ref": "/common/nullable_string"}, "description": {"$ref": "/common/nullable_string"}}, "required": [], "additionalProperties": false}