{"type": "object", "properties": {"allowed": {"type": "boolean"}, "amount": {"oneOf": [{"$ref": "/common/positive_number"}, {"$ref": "/common/notempty_positive_number_string"}, {"type": "null"}]}, "monthlyPayment": {"oneOf": [{"$ref": "/common/positive_number"}, {"$ref": "/common/notempty_positive_number_string"}, {"type": "null"}]}, "marketAmount": {"oneOf": [{"$ref": "/common/positive_number"}, {"$ref": "/common/notempty_positive_number_string"}, {"type": "null"}]}}, "additionalProperties": false}