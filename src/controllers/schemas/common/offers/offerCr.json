{"type": "object", "properties": {"source": {"$ref": "/common/offers/offer_source"}, "urgency": {"oneOf": [{"type": "number", "minimum": 0, "maximum": 3}, {"type": "null"}]}, "version": {"oneOf": [{"type": "null"}, {"type": "object", "properties": {"of": {"$ref": "/common/object_idedkey"}, "type": {"$ref": "/common/object_codedkey"}, "disclaimer": {"type": "string"}}, "required": ["of", "type"], "additionalProperties": false}]}, "customer": {"$ref": "/common/nullable_object_idedkey"}, "status": {"$ref": "/common/object_codedkey"}, "mandate": {"oneOf": [{"type": "null"}, {"$ref": "/common/offers/offer_mandate"}]}, "historic": {"oneOf": [{"type": "null"}, {"$ref": "/common/offers/offer_historic"}]}, "property": {"$ref": "/common/offers/propertyCr"}, "sale": {"$ref": "/common/offers/saleCrUp"}, "rent": {"$ref": "/common/offers/rentCrUp"}, "currency": {"$ref": "/common/object_codedkey"}, "notes": {"$ref": "/common/nullable_string"}, "description": {"$ref": "/common/nullable_string"}}, "required": ["property", "sale", "rent", "currency"], "additionalProperties": false}