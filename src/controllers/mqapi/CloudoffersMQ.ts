import { cloudExternalofferBusiness } from "business/CloudExternalofferBusiness";
import { appContext } from "lib/AppContext";

export namespace CloudoffersMQ {

  export async function subscribeToMessages() {
    const { crwidealista_api, crwindividuals_api, messagesbroker, logger } = appContext();

    try {
      await crwidealista_api.subscribeAdRefreshResponse(messagesbroker, async (response) => {
        logger.debug(`START: AdRefreshResponse key=${response.request_key} status=${response.status}!!!`);
        try {
          await cloudExternalofferBusiness.processCrawlerRefreshEnded(response.request_key, response.status);
          logger.debug(`OK: AdRefreshResponse key=${response.request_key} status=${response.status}!!!`);
        } catch(e:any){
          logger.error(`ERROR: AdRefreshResponse  key=${response.request_key} status=${response.status}!!!  (${e.message})`);
          // Don't re-throw here to prevent unhandled promise rejections
        }
      });

      logger.debug("CloudoffersMQ subscriptions set up successfully");
    } catch (error: any) {
      logger.error("Failed to set up CloudoffersMQ subscriptions:", error.message);
      throw error; // Re-throw to be handled by the caller
    }

    //await crwindividuals_api

  }
}