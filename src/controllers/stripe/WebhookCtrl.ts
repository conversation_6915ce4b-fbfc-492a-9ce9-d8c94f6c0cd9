import Stripe from 'stripe';
import { Request, Response, NextFunction } from 'express'
import { AppContext, withAppContext } from 'lib/AppContext';
import { stripeCheckoutsessionsModel } from 'model/stripeCheckoutsessionsModel';
import { asRest } from 'controllers/lib/ctrlUtils';
import { RestError } from 'controllers/lib/RestError';
import { ecomDepositBusiness } from 'business/ecomDepositBusiness';

export namespace stripe_webhooks {

	export async function postEventAct(req: Request, res: Response, next: NextFunction) {
		return asRest({})(res, next, () =>
			withAppContext( async ctx => {

				let event = null;
				try {
					event = ctx.stripeWrapper.extractWebhookEvent(req);
					// Successfully constructed event
					console.log('STRIPE webhook:', event.id, event.type, event.data);
				} catch (err: unknown) {
					const e = err as Error;
					// On error, log and return the error message
					console.log(`[ERROR] STRIPE webhook : ${e.message}`);
					throw new RestError({ status: 400, message: `Webhook Error: ${e.message}` });
				}
				// Hemos recibido y decodificado el evento.. independientemente del resultado interno de su proceso
				// debemos responder OK a stripe.
				try {
					switch (event.type) {
						/*case "checkout.session.async_payment_succeeded":
							break;
						case "checkout.session.async_payment_failed":
							await registerFailed(ctx, event);
							break;
							*/
						case "checkout.session.completed":
							await registerCheckoutCompleted(ctx, event);
							break;
						default:
							console.log('Unexpected Event', event.type);
							break;
						//throw new RestError({ status: 400, message: `Processing of "${event.type}" is not supported` });
					}
				} catch (err: unknown) {
					const e = err as Error;
					// Cualquier error en este punto no debe transmitirse a stripe!!!
					console.log(`[ERROR] STRIPE webhook (processing): ${e.message}`);
				}
				// Debemos devolver true siempre.
				return { received: true };

			})
		);
	}


	async function registerCheckoutCompleted(ctx: AppContext, event: Stripe.Event) {
		const session = event.data.object as Stripe.Checkout.Session;
		const stripeSessionId = session.id;

		return ctx.db.withTransaction(async dbTrx => {

			await stripeCheckoutsessionsModel.update( {
				id: stripeSessionId,
				completed: {
					at: new Date(),
					event: event
				}
			})(dbTrx);
			// Generamos el depósito.
			const deposit = await ecomDepositBusiness.createFromStripecheckout(dbTrx, stripeSessionId);
			//
			return true;

		})();
	}
}

