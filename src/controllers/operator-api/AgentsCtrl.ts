import { asRest } from "controllers/lib/ctrlUtils";
import { withA<PERSON><PERSON>eyUser } from "controllers/lib/ctrlApiKeyUtils";
import { NextFunction, Request, Response } from 'express';
import { AgentsModel } from 'model/agentsModel';
import { jsonMember, jsonObject } from 'typedjson';
import { CtrlParseUtils } from '../lib/CtrlParseUtils';

/// <reference path="OperatorApi.ts" />

export namespace OperatorApi_AgentsCtrl {

  @jsonObject
  class GetAgentsQuery {
    @jsonMember(String, { isRequired: true })
    email!: string
  }

  export function getAgentsAct(req: Request, res: Response, next: NextFunction) {
    asRest({ nullAs404: true })(res, next, () => withApiKeyUser()(req, (user) => {
      const params = CtrlParseUtils.parseQuery(GetAgentsQuery)(req);
      return AgentsModel.list({
        email: params.email
      })();
    }));
  }

}
