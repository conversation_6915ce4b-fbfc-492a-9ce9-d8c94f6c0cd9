import { ContactBusiness } from "business/contactBusiness";
import { asRest } from "controllers/lib/ctrlUtils";
import { withApi<PERSON>eyUser } from "controllers/lib/ctrlApiKeyUtils";
import { NextFunction, Request, Response } from 'express';
import { ContactDTO } from 'model/common/dtos/ContactDTO';
import { ContactsModel } from 'model/contactsModel';
import { jsonMember, jsonObject } from 'typedjson';
import { CtrlParseUtils } from '../lib/CtrlParseUtils';

/// <reference path="OperatorApi.ts" />

export namespace OperatorApi_ContactsCtrl {

  @jsonObject
  class GetContactsParams {
    /**
     * Parámetro de URL
     */
    @jsonMember(String, { isRequired: true })
    agent_id!: string
  }
  @jsonObject
  class GetContactsQuery {
    /** Query param */
    @jsonMember(String)
    email?: string
    @jsonMember(String)
    mobile?: string
    @jsonMember(Number, { deserializer: (v: any) => typeof v === "string" ? Number.parseInt(v) : v })
    pag_limit?: number
    @jsonMember(Number, { deserializer: (v: any) => typeof v === "string" ? Number.parseInt(v) : v })
    pag_offset?: number
  }


  /**
   * GET /srv-api/agents/:agent_id/contacts?email=<EMAIL>
   * @param req 
   * @param res 
   * @param next 
   */
  export function getContactsAct(req: Request, res: Response, next: NextFunction) {
    asRest({ nullAs404: true })(res, next, () => withApiKeyUser()(req, _ => {
      const params = CtrlParseUtils.parseParams(GetContactsParams)(req);
      const qry = CtrlParseUtils.parseQuery(GetContactsQuery)(req);

      return ContactsModel.list({
        agent_id: params.agent_id,
        mobile: qry.mobile,
        email: qry.email,
        pagination: {
          offset: qry.pag_offset ?? 0,
          limit: qry.pag_limit ?? 30
        }
      })();

    }));
  }
  @jsonObject
  class GetContactParams {
    @jsonMember(String, { isRequired: true })
    agent_id!: string
    @jsonMember(String, { isRequired: true })
    contact_id!: string
  }
  /**
   * GET srv-api/agents/:agent_id/contacts/:contact_id
   * @param req 
   * @param res 
   * @param next 
   */
  export function getContactAct(req: Request, res: Response, next: NextFunction) {
    asRest({ nullAs404: true })(res, next, () => withApiKeyUser()(req, async (user) => {
      const params = CtrlParseUtils.parseParams(GetContactParams)(req);
      const [contact] = await ContactsModel.list({
        agent_id: params.agent_id,
        id: params.contact_id,
      })();
      return contact;
    }));
  }

  @jsonObject
  class PostContactParams {
    @jsonMember(String, { isRequired: true })
    agent_id!: string
  }
  @jsonObject
  class PostContactBody implements ContactDTO {
    @jsonMember(String, { isRequired: true })
    firstName!: string
    @jsonMember(String)
    lastName?: string
    @jsonMember(String)
    email?: string
    @jsonMember(String)
    mobile?: string
    @jsonMember(String)
    notes?: string
  }
  /**
   * POST /srv-api/agents/:agent_id/contacts
   * Tener en cuenta que, para un agente, no se permiten email o mobile duplicados
   * @param req 
   * @param res 
   * @param next 
   */
  export function postContactAct(req: Request, res: Response, next: NextFunction) {
    asRest({ nullAs404: true })(res, next, () => withApiKeyUser()(req, (user) => {
      const params = CtrlParseUtils.parseParams(PostContactParams)(req);
      const body = CtrlParseUtils.parseBody(PostContactBody)(req);

      return ContactBusiness.create(params.agent_id, body)();
      
    }));
  }
}
