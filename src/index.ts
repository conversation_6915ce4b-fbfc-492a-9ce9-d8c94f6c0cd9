import express from 'express';
import { createAppContext, createAppRoutes } from "./app";
import compression from 'compression';
import cors from 'cors';
import { env } from "process";
import { setAppContext, withAppContext } from 'lib/AppContext';
import { CloudoffersMQ } from 'controllers/mqapi/CloudoffersMQ';
import { promisify } from 'util';

const config = require("../config/app.js");

const app = express();
const PORT = config.http.port;

// USADO en la generación de tokens (JWT).
// Debe provenir de la configuración
if (!env.ACCESS_TOKEN_SECRET) {
  env.ACCESS_TOKEN_SECRET = config.jwt.ACCESS_TOKEN_SECRET;
}

const appContext = createAppContext(config);
// Establecer contexto para toda la aplicación
setAppContext(appContext);

withAppContext(async ({ logger, messagesbroker }) => {

  try {
    logger.info("Setting up Message Broker subscriptions");
    try {
      // Preparamos todas las subscripciones antes de conectar con el servidor MQ
      await CloudoffersMQ.subscribeToMessages();
      // Activamos todas las subscripciones (y conectamos con el servidoer MQ)
      await messagesbroker.enableSubscriptions();
      logger.info("Message Broker subscriptions set up successfully");
    } catch (mqError: any) {
      logger.error("Failed to set up Message Broker subscriptions:", mqError.message);
      logger.warn("Continuing without Message Broker functionality");
    }

    logger.info("Setting up Express application");

    const server = app.
      enable("trust proxy").
      use(compression()).
      use(cors({
        "origin": "*",
        "methods": "GET,HEAD,PUT,PATCH,POST,DELETE",
        "preflightContinue": true,
        "optionsSuccessStatus": 204
      })).
      //use(bodyParser.json({ strict: true })).
      use(express.urlencoded({ extended: true })).
      use(express.static("public")).
      use(createAppRoutes(config)).
      listen(PORT, () => {
        logger.info(`⚡️[server]: Server is running at http://localhost:${PORT}`);
      });


    let shuttedDown = false;
    const onShutdown = async () => {
      if (!shuttedDown) {
        try {
          await promisify(server.close).bind(server)();
          await appContext.dispose();
        } finally {
          shuttedDown = true
        }
      }
    }
    process.on('SIGTERM', onShutdown);
    process.on('SIGINT', onShutdown);

  } catch (e: any) {
    logger.error("Unexpected error during application startup:", e.message);
    logger.error("Stack trace:", e.stack);
    process.exit(1);
  }
});


