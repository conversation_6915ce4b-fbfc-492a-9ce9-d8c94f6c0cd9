import { AscOrDesc } from "./common/AscOrDesc";
import { EcomPurchaseDTO } from "./common/dtos/EcomPurchaseDTO";
import { rowToEcompurchase } from "./common/rows2obj";
import { ecomPurchasesTable } from "./common/schema/ecomPurchasesTable";

import { PoolClient } from 'pg';
import { DbCrudUtils, SqlHelpers, SqlWhereHelpers } from "agentor-lib";
import { dbSchema } from './common/schema/dbSchema';
import { withDbCli, withDbTrx } from "lib/AppContextUtils";
const
  { addEq, addGtEq, addLt, addLtEq, addIsNotNull } = SqlWhereHelpers,
  { insertRecord, updateRecord } = DbCrudUtils,
  { buildSqlFields, buildSqlFrom } = SqlHelpers;

const c_root_alias = "purchase";

export namespace EcomPurchasesModel {

  export declare type Filter = {
    id?: string,
    /** Sin fecha de fin de servicio */
    service_active?: boolean,
    service_offer_id?: string,

    buyer_id?: string,
    buyer_agent_id?: string,
    product_id?: string,
    product_account_id?: string,
    product_account_agent_id?: string,

    buyer_or_seller_agent_id?: string,

    include_product?: boolean,
    include_service_offer?: boolean,

    /** Incluir los campos calculados con totales (de momento, totals.payed) */
    include_totals?: boolean,

    orderBy?: {
      date?: AscOrDesc,

    },
    pagination?: {
      offset?: number,
      limit?: number
    }
  };


  export const
    create = (dto: EcomPurchaseDTO) => withDbTrx(create_imp(dto)),
    list = (filter: Filter) => withDbCli(list_imp(filter)),
    update = (id: string, dto: EcomPurchaseDTO) => withDbTrx(update_imp(id, dto));

  function create_imp(dto: EcomPurchaseDTO): (dbTran: PoolClient) => Promise<EcomPurchaseDTO> {
    return async (dbTran) => {
      let { id } = await insertRecord(ecomPurchasesTable, dto)(dbTran);
      let [newPurchase] = await list_imp({ id })(dbTran);
      return newPurchase;
    };
  }

  function update_imp(id: string, dto: EcomPurchaseDTO): (dbTran: PoolClient) => Promise<number> {
    return updateRecord(ecomPurchasesTable, { ...dto, id });
  }

  function list_imp(filter: Filter): (dbClient: PoolClient) => Promise<EcomPurchaseDTO[]> {
    const { terms, params, orderBy, offset, limit } = buildCondition(filter);

    const qryOptions = {
      table: ecomPurchasesTable,
      tableAlias: c_root_alias,
      // Foreign keys
      allowedPaths: [
        c_root_alias,
        `${c_root_alias}_service`,
        ...filter.include_product ? [`${c_root_alias}_product`] : [],
        ...filter.include_service_offer ? [
          `${c_root_alias}_service_offer`,
          `${c_root_alias}_service_offer_property`,
          `${c_root_alias}_service_offer_property_type`,
          `${c_root_alias}_service_offer_property_address_city`,
        ] : [],
        ...filter.include_totals ? [`${c_root_alias}_totals`] : [],
      ]
    };
    const fromOptions = {
      ...qryOptions,
      allowedPaths: [
        ...qryOptions.allowedPaths,
        `${c_root_alias}_product`,
        ...filter.include_service_offer ? [
          `${c_root_alias}_service_offer`,
          `${c_root_alias}_service_offer_property`,
          `${c_root_alias}_service_offer_property_address_city`,
        ] : [],
        `${c_root_alias}_buyer`,
        `${c_root_alias}_buyer_agent`,
        `${c_root_alias}_product`,
        `${c_root_alias}_product_account`,
        `${c_root_alias}_product_account_agent`,
      ]
    };

    const
      qryFields = buildSqlFields(dbSchema, qryOptions).join(", "),
      qryFrom = buildSqlFrom(dbSchema, fromOptions);

    const qry = `
			select ${qryFields}  
			from ${qryFrom}
			${terms.length !== 0 ? `where ${terms.join(" and ")}` : ""}
			${orderBy.length !== 0 ? `order by ${orderBy.join(", ")}` : ""}
			offset ${offset} 
			limit ${limit}
		`;

    return dbClient =>
      dbClient.
        query(qry, params).
        then(({ rows }) => rows.map(rw =>
          rowToEcompurchase(rw, c_root_alias)
        ));
  }

  function buildCondition(filter: Filter) {
    const { id, buyer_id, buyer_or_seller_agent_id, buyer_agent_id, product_id, product_account_id, product_account_agent_id, service_active, service_offer_id, pagination } = filter;
    const offset = pagination?.offset ?? 0;
    const limit = pagination?.limit ?? 30;

    let terms: string[] = [];
    let params: any[] = [];
    let orderBy: string[] = [];

    addEq(terms, params, `${c_root_alias}.id`, id);
    addEq(terms, params, `${c_root_alias}.buyer_id`, buyer_id);

    addEq(terms, params, `${c_root_alias}.product_id`, product_id);
    addEq(terms, params, `${c_root_alias}_product.account_id`, product_account_id);

    addEq(terms, params, `${c_root_alias}_buyer.agent_id`, buyer_agent_id);
    addEq(terms, params, `${c_root_alias}_product_account.agent_id`, product_account_agent_id);
    if (buyer_or_seller_agent_id !== void 0) {
      // Debemos obligar a que uno de los 2 agentes (comprador o vendedor) sean el indicado
      if (buyer_agent_id === void 0 && product_account_agent_id === void 0) {
        params.push(buyer_or_seller_agent_id);
        terms.push(`(${c_root_alias}_buyer.agent_id = $${params.length} or ${c_root_alias}_product_account.agent_id = $${params.length} )`);
      } else if (buyer_agent_id !== void 0 && buyer_agent_id !== buyer_or_seller_agent_id) {
        addEq(terms, params, `${c_root_alias}_product_account.agent_id`, product_account_agent_id);
      } else if (product_account_agent_id !== void 0 && product_account_agent_id !== buyer_or_seller_agent_id) {
        addEq(terms, params, `${c_root_alias}_buyer.agent_id`, buyer_or_seller_agent_id);
      }
    }
    addIsNotNull(terms, `${c_root_alias}.service_enddate`, service_active);
    addEq(terms, params, `${c_root_alias}.service_offer_id`, service_offer_id);

    if (filter.orderBy?.date) {
      orderBy = [...orderBy, `${c_root_alias}.date ${filter.orderBy.date ?? "asc"} `]
    } else {
      orderBy = [...orderBy, `${c_root_alias}.date asc`]
    }
    return { terms, params, offset, limit, orderBy };
  }
}