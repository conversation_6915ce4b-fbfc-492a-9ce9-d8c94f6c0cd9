import { withDbCli } from 'lib/AppContextUtils';


import { PropertytypeDTO } from "./common/dtos/PropertytypeDTO";
import { rowToPropertySubtype, rowToPropertyType } from './common/rows2obj';
import { propertysubtypesTable } from "./common/schema/propertysubtypesTable";
import { propertytypesTable } from "./common/schema/propertytypesTable";


import { PoolClient } from 'pg';
import { DbCrudUtils, SqlHelpers, SqlWhereHelpers } from "agentor-lib";
import { dbSchema } from './common/schema/dbSchema';
const
  { addEq, addGt, addGtEq, addLt, addLtEq, addNotIn, addIsNotNull, addIn, addNotExists, addIsFkOf, addIsNotFkOf } = SqlWhereHelpers,
  { insertRecord, removeRecord, updateRecord } = DbCrudUtils,
  { buildSqlFields, buildSqlFrom } = SqlHelpers;


const c_root_alias = "propertysubtype";
export namespace PropertytypesModel {
  export declare type Filter = {
    code?: string,
    select?: {
      include_subtypes?: boolean
    }
  }

  export const list = (filter: Filter) => withDbCli(listPropertytypes_imp(filter));


  function listPropertytypes_imp(filter: Filter): (dbClient: PoolClient) => Promise<PropertytypeDTO[]> {
    const { terms, params } = buildCondition(filter);
    const allowedPaths = [
      "propertytype"
    ];
    const
      qryFields = buildSqlFields(dbSchema, { table: propertytypesTable, tableAlias: "propertytype", allowedPaths }).join(', '),
      qryFrom = buildSqlFrom(dbSchema, { table: propertytypesTable, tableAlias: "propertytype", allowedPaths });

    const qry = `
      select 
        ${qryFields}  
      from 
        ${qryFrom}
      ${terms.length !== 0 ? `
      where 
        ${terms.join(" and ")}` : ""
      }
      order by propertytype.code asc
    `;

    return dbClient => dbClient.query(qry, params).
      then(result => result.rows.map(row => rowToPropertyType(row, "propertytype"))).
      then(propertytypes =>
        filter.select?.include_subtypes
          ? completeWithSubtypes(propertytypes)(dbClient)
          : propertytypes
      );


  }

  /**
   * Obtiene todos los "subtipos" de la BBDD y los añade a la colección de subtipos de cada tipo indicado en @propertytypes
   * @param dbClient 
   * @param propertytypes 
   * @returns
   */
  function completeWithSubtypes(propertytypes: PropertytypeDTO[]): (dbClient: PoolClient) => Promise<PropertytypeDTO[]> {

    const qry = `
      select 
        ${buildSqlFields(dbSchema, { table: propertysubtypesTable, tableAlias: "propertysubtype" })}
      from
        ${buildSqlFrom(dbSchema, { table: propertysubtypesTable, tableAlias: "propertysubtype" })}
      order by 
        propertysubtype.type_code, 
        propertysubtype.code
    `;

    return async dbClient => {
      const subtypes = await dbClient.query(qry, []).then(result => result.rows.map(row =>
        rowToPropertySubtype(row, "propertysubtype")
      ));

      return propertytypes.map(
        propertytype => ({
          ...propertytype,
          subtypes: subtypes.filter(subtype => subtype.type?.code === propertytype.code),
        })
      );
    }

  }

  function buildCondition(filter: Filter) {
    const { code } = filter;
    let terms: string[] = [];
    let params: any[] = [];

    addEq(terms, params, "propertytype.code", code);

    return { terms, params };
  }

}
