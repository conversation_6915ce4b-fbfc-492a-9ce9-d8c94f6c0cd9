import { withDbCli } from "lib/AppContextUtils";
import { PoolClient } from "pg";
import { CloudProviderDTO } from "./common/dtos/CloudProviderDTO";
import { cloudProvidersFields, cloudProvidersTable } from "./common/schema/cloudProvidersTable";
import { dbSchema } from "./common/schema/dbSchema";
import { SqlHelpers } from "agentor-lib";

import { rowToCloudProvider } from "./common/rows2obj";
import { OffermandatetypeCode } from "./common/dtos/OffermandatetypeDTO";
import { OfferstatusCode } from "./common/dtos/OfferstatusDTO";
import { TypedJsonUtils } from "lib/TypedJsonUtils";
const
  { buildSqlFields, buildSqlFrom } = SqlHelpers;

const c_root_alias = "provider";

export namespace CloudProvidersModel {
  export enum providersIds {
    crwidealista = "9223372036854775807",
    crwhabitaclia = "9223372036854775806",
    crwhaya = "9223372036854775805",
    crwservihabitat = "9223372036854775804",
    crwsolvia = "9223372036854775803",
    crwunicaja = "9223372036854775802",
    /** Crawler de particulares */
    crwindividuals = "9223372036854775801",
    crwportalnow = "9223372036854775800",
    crwaliseda = "9223372036854775799",
    crwuci = "9223372036854775798"
  }
  export enum contactsIds {
    haya = "9223372036854775807",
    servihabitat = "9223372036854775806",
    solvia = "9223372036854775805",
    unicaja = "9223372036854775804",
    portalnow = "9223372036854775803",
    aliseda = "9223372036854775802",
    uci = "9223372036854775801"
  }

  export type Filter = {
    id?: string,
    agent_id?: string,
  };

  // De momento ponemos esta info hardcoded aquí... pero debería estar en la BBDD en un campo especial
  const providersImportAdConfs = buildProvidersImportAdConfsExt();

  export function readN(ids: string[]): (dbCli?: PoolClient) => Promise<CloudProviderDTO[]> {

    if (ids.length === 0) {
      return async () => [];
    } else {
      const qryOptions = { table: cloudProvidersTable, tableAlias: c_root_alias };
      const qry = `
      select 
        ${buildSqlFields(dbSchema, qryOptions).join(', ')} 
      from 
        ${buildSqlFrom(dbSchema, qryOptions)} 
      where 
        ${c_root_alias}.${cloudProvidersFields.id}=any($1)
      `;
      const qryParams = [ids];

      return withDbCli(async dbClient =>
        dbClient.query(qry, qryParams).then(({ rows }) =>
          rows.map(row => rowToCloudProvider(row, c_root_alias))
        )
      );

    }
  }

  export function readByAgentId(agentId: string): (dbClient: PoolClient) => Promise<CloudProviderDTO[]> {

    const
      qryFields = buildSqlFields(dbSchema, { table: cloudProvidersTable, tableAlias: c_root_alias }).join(', '),
      qryFrom = buildSqlFrom(dbSchema, { table: cloudProvidersTable, tableAlias: c_root_alias });

    const qry = `select ${qryFields} from ${qryFrom} where ${c_root_alias}.${cloudProvidersFields.agent_id}=any($1)`;
    const qryParams = [agentId];

    return withDbCli(async dbClient =>
      dbClient.query(qry, qryParams).then(({ rows }) =>
        rows.map(row => rowToCloudProvider(row, c_root_alias))
      )
    );

  }

  export function getProviderImportAdConf(id: string): (dbCli?: PoolClient) => Promise<CloudProviderDTO.ImportAdConfigurationExt | undefined> {
    return withDbCli(async _ => providersImportAdConfs.get(id));
  }


  function buildProvidersImportAdConfsExt() {
    const providersImportAdConfs = new Map<string, CloudProviderDTO.ImportAdConfigurationExt>();

    const bankservicerconf: CloudProviderDTO.ImportAdConfigurationExt = {
      bindIfPossible: false,
      activeAdCr: {
        status_code: OfferstatusCode.news,
        mandate_type_code: OffermandatetypeCode.other,
        importInternals: true,
        importProperty: true,
        importModality: true,
        importDescrition: true,
      },
      deletedAdCr: {
        status_code: OfferstatusCode.historic,
        mandate_type_code: null,
        importInternals: true,
        importProperty: true,
        importModality: true,
        importDescrition: true,
      },
      activeAdUp_news: {
        importModality: true,
        importInternals: false,
        importProperty: false,
        importDescrition: false,
      },
      activeAdUp_draft: {
        importModality: true,
        importInternals: false,
        importProperty: false,
        importDescrition: false,
      },
      activeAdUp_commercialization: {
        importModality: true,
        importInternals: false,
        importProperty: false,
        importDescrition: false,
      },
      activeAdUp_historic: {
        status_code: OfferstatusCode.historic,
        importModality: true,
        importInternals: false,
        importProperty: false,
        importDescrition: false,
      },
      deletedAdUp_news: {
        status_code: OfferstatusCode.historic,
        importModality: true,
        importInternals: false,
        importProperty: false,
        importDescrition: false,
      },
      deletedAdUp_draft: {
        status_code: OfferstatusCode.historic,
        importModality: true,
        importInternals: false,
        importProperty: false,
        importDescrition: false,
      },
      deletedAdUp_commercialization: {
        status_code: OfferstatusCode.historic,
        importModality: true,
        importInternals: false,
        importProperty: false,
        importDescrition: false,
      },
      deletedAdUp_historic: {
        status_code: OfferstatusCode.historic,
        importModality: true,
        importInternals: false,
        importProperty: false,
        importDescrition: false,
      }
    };

    const hayaconf: CloudProviderDTO.ImportAdConfigurationExt = {
      ...bankservicerconf,
      activeAdCr: { ...bankservicerconf.activeAdCr, customer_id: contactsIds.haya },
      deletedAdCr: { ...bankservicerconf.deletedAdCr, customer_id: contactsIds.haya },
      activeAdUp_news: { ...bankservicerconf.activeAdUp_news, customer_id: contactsIds.haya },
      activeAdUp_draft: { ...bankservicerconf.activeAdUp_draft, customer_id: contactsIds.haya },
      activeAdUp_commercialization: { ...bankservicerconf.activeAdUp_commercialization, customer_id: contactsIds.haya },
      activeAdUp_historic: { ...bankservicerconf.activeAdUp_historic, customer_id: contactsIds.haya },
      deletedAdUp_news: { ...bankservicerconf.deletedAdUp_news, customer_id: contactsIds.haya },
      deletedAdUp_draft: { ...bankservicerconf.deletedAdUp_draft, customer_id: contactsIds.haya },
      deletedAdUp_commercialization: { ...bankservicerconf.deletedAdUp_commercialization, customer_id: contactsIds.haya },
      deletedAdUp_historic: { ...bankservicerconf.deletedAdUp_historic, customer_id: contactsIds.haya },
    }
    providersImportAdConfs.set(providersIds.crwhaya, TypedJsonUtils.parse(hayaconf,
      CloudProviderDTO.ImportAdConfigurationExt
    ));

    const serviavitatConf: CloudProviderDTO.ImportAdConfigurationExt = {
      ...bankservicerconf,
      activeAdCr: { ...bankservicerconf.activeAdCr, customer_id: contactsIds.servihabitat },
      deletedAdCr: { ...bankservicerconf.deletedAdCr, customer_id: contactsIds.servihabitat },
      activeAdUp_news: { ...bankservicerconf.activeAdUp_news, customer_id: contactsIds.servihabitat },
      activeAdUp_draft: { ...bankservicerconf.activeAdUp_draft, customer_id: contactsIds.servihabitat },
      activeAdUp_commercialization: { ...bankservicerconf.activeAdUp_commercialization, customer_id: contactsIds.servihabitat },
      activeAdUp_historic: { ...bankservicerconf.activeAdUp_historic, customer_id: contactsIds.servihabitat },
      deletedAdUp_news: { ...bankservicerconf.deletedAdUp_news, customer_id: contactsIds.servihabitat },
      deletedAdUp_draft: { ...bankservicerconf.deletedAdUp_draft, customer_id: contactsIds.servihabitat },
      deletedAdUp_commercialization: { ...bankservicerconf.deletedAdUp_commercialization, customer_id: contactsIds.servihabitat },
      deletedAdUp_historic: { ...bankservicerconf.deletedAdUp_historic, customer_id: contactsIds.servihabitat },
    }
    providersImportAdConfs.set(providersIds.crwservihabitat, TypedJsonUtils.parse(serviavitatConf,
      CloudProviderDTO.ImportAdConfigurationExt
    ));

    const solviaConf: CloudProviderDTO.ImportAdConfigurationExt = {
      ...bankservicerconf,
      activeAdCr: { ...bankservicerconf.activeAdCr, customer_id: contactsIds.solvia },
      deletedAdCr: { ...bankservicerconf.deletedAdCr, customer_id: contactsIds.solvia },
      activeAdUp_news: { ...bankservicerconf.activeAdUp_news, customer_id: contactsIds.solvia },
      activeAdUp_draft: { ...bankservicerconf.activeAdUp_draft, customer_id: contactsIds.solvia },
      activeAdUp_commercialization: { ...bankservicerconf.activeAdUp_commercialization, customer_id: contactsIds.solvia },
      activeAdUp_historic: { ...bankservicerconf.activeAdUp_historic, customer_id: contactsIds.solvia },
      deletedAdUp_news: { ...bankservicerconf.deletedAdUp_news, customer_id: contactsIds.solvia },
      deletedAdUp_draft: { ...bankservicerconf.deletedAdUp_draft, customer_id: contactsIds.solvia },
      deletedAdUp_commercialization: { ...bankservicerconf.deletedAdUp_commercialization, customer_id: contactsIds.solvia },
      deletedAdUp_historic: { ...bankservicerconf.deletedAdUp_historic, customer_id: contactsIds.solvia },
    }
    providersImportAdConfs.set(providersIds.crwsolvia, TypedJsonUtils.parse(solviaConf,
      CloudProviderDTO.ImportAdConfigurationExt
    ));

    const unicajaConf: CloudProviderDTO.ImportAdConfigurationExt = {
      ...bankservicerconf,
      activeAdCr: { ...bankservicerconf.activeAdCr, customer_id: contactsIds.unicaja },
      deletedAdCr: { ...bankservicerconf.deletedAdCr, customer_id: contactsIds.unicaja },
      activeAdUp_news: { ...bankservicerconf.activeAdUp_news, customer_id: contactsIds.unicaja },
      activeAdUp_draft: { ...bankservicerconf.activeAdUp_draft, customer_id: contactsIds.unicaja },
      activeAdUp_commercialization: { ...bankservicerconf.activeAdUp_commercialization, customer_id: contactsIds.unicaja },
      activeAdUp_historic: { ...bankservicerconf.activeAdUp_historic, customer_id: contactsIds.unicaja },
      deletedAdUp_news: { ...bankservicerconf.deletedAdUp_news, customer_id: contactsIds.unicaja },
      deletedAdUp_draft: { ...bankservicerconf.deletedAdUp_draft, customer_id: contactsIds.unicaja },
      deletedAdUp_commercialization: { ...bankservicerconf.deletedAdUp_commercialization, customer_id: contactsIds.unicaja },
      deletedAdUp_historic: { ...bankservicerconf.deletedAdUp_historic, customer_id: contactsIds.unicaja },
    }
    providersImportAdConfs.set(providersIds.crwunicaja, TypedJsonUtils.parse(unicajaConf,
      CloudProviderDTO.ImportAdConfigurationExt
    ));

    const portalnowConf: CloudProviderDTO.ImportAdConfigurationExt = {
      ...bankservicerconf,
      activeAdCr: { ...bankservicerconf.activeAdCr, customer_id: contactsIds.portalnow },
      deletedAdCr: { ...bankservicerconf.deletedAdCr, customer_id: contactsIds.portalnow },
      activeAdUp_news: { ...bankservicerconf.activeAdUp_news, customer_id: contactsIds.portalnow },
      activeAdUp_draft: { ...bankservicerconf.activeAdUp_draft, customer_id: contactsIds.portalnow },
      activeAdUp_commercialization: { ...bankservicerconf.activeAdUp_commercialization, customer_id: contactsIds.portalnow },
      activeAdUp_historic: { ...bankservicerconf.activeAdUp_historic, customer_id: contactsIds.portalnow },
      deletedAdUp_news: { ...bankservicerconf.deletedAdUp_news, customer_id: contactsIds.portalnow },
      deletedAdUp_draft: { ...bankservicerconf.deletedAdUp_draft, customer_id: contactsIds.portalnow },
      deletedAdUp_commercialization: { ...bankservicerconf.deletedAdUp_commercialization, customer_id: contactsIds.portalnow },
      deletedAdUp_historic: { ...bankservicerconf.deletedAdUp_historic, customer_id: contactsIds.portalnow },
    }
    providersImportAdConfs.set(providersIds.crwportalnow, TypedJsonUtils.parse(portalnowConf,
      CloudProviderDTO.ImportAdConfigurationExt
    ));

    const alisedaConf: CloudProviderDTO.ImportAdConfigurationExt = {
      ...bankservicerconf,
      activeAdCr: { ...bankservicerconf.activeAdCr, customer_id: contactsIds.aliseda },
      deletedAdCr: { ...bankservicerconf.deletedAdCr, customer_id: contactsIds.aliseda },
      activeAdUp_news: { ...bankservicerconf.activeAdUp_news, customer_id: contactsIds.aliseda },
      activeAdUp_draft: { ...bankservicerconf.activeAdUp_draft, customer_id: contactsIds.aliseda },
      activeAdUp_commercialization: { ...bankservicerconf.activeAdUp_commercialization, customer_id: contactsIds.aliseda },
      activeAdUp_historic: { ...bankservicerconf.activeAdUp_historic, customer_id: contactsIds.aliseda },
      deletedAdUp_news: { ...bankservicerconf.deletedAdUp_news, customer_id: contactsIds.aliseda },
      deletedAdUp_draft: { ...bankservicerconf.deletedAdUp_draft, customer_id: contactsIds.aliseda },
      deletedAdUp_commercialization: { ...bankservicerconf.deletedAdUp_commercialization, customer_id: contactsIds.aliseda },
      deletedAdUp_historic: { ...bankservicerconf.deletedAdUp_historic, customer_id: contactsIds.aliseda },
    }
    providersImportAdConfs.set(providersIds.crwaliseda, TypedJsonUtils.parse(alisedaConf,
      CloudProviderDTO.ImportAdConfigurationExt
    ));

    const uciConf: CloudProviderDTO.ImportAdConfigurationExt = {
      ...bankservicerconf,
      activeAdCr: { ...bankservicerconf.activeAdCr, customer_id: contactsIds.uci },
      deletedAdCr: { ...bankservicerconf.deletedAdCr, customer_id: contactsIds.uci },
      activeAdUp_news: { ...bankservicerconf.activeAdUp_news, customer_id: contactsIds.uci },
      activeAdUp_draft: { ...bankservicerconf.activeAdUp_draft, customer_id: contactsIds.uci },
      activeAdUp_commercialization: { ...bankservicerconf.activeAdUp_commercialization, customer_id: contactsIds.uci },
      activeAdUp_historic: { ...bankservicerconf.activeAdUp_historic, customer_id: contactsIds.uci },
      deletedAdUp_news: { ...bankservicerconf.deletedAdUp_news, customer_id: contactsIds.uci },
      deletedAdUp_draft: { ...bankservicerconf.deletedAdUp_draft, customer_id: contactsIds.uci },
      deletedAdUp_commercialization: { ...bankservicerconf.deletedAdUp_commercialization, customer_id: contactsIds.uci },
      deletedAdUp_historic: { ...bankservicerconf.deletedAdUp_historic, customer_id: contactsIds.uci },
    }
    providersImportAdConfs.set(providersIds.crwuci, TypedJsonUtils.parse(uciConf,
      CloudProviderDTO.ImportAdConfigurationExt
    ));


    const idealistaconf: CloudProviderDTO.ImportAdConfigurationExt = {
      activeAdCr: {
        status_code: OfferstatusCode.news,
        importInternals: true,
        importProperty: true,
        importModality: true,
        importDescrition: true,
      },
      deletedAdCr: {
        status_code: OfferstatusCode.historic,
        importInternals: true,
        importProperty: true,
        importModality: true,
        importDescrition: true,
      },
      activeAdUp_news: {
        status_code: OfferstatusCode.news,
        importModality: true,
        importInternals: true,
        importProperty: true,
        importDescrition: true,
      },
      activeAdUp_draft: {
        status_code: OfferstatusCode.news,
        importModality: true,
        importInternals: true,
        importProperty: true,
        importDescrition: true,
      },
      activeAdUp_commercialization: {
        status_code: OfferstatusCode.news,
        importModality: true,
        importInternals: true,
        importProperty: true,
        importDescrition: true,
      },
      activeAdUp_historic: {
        status_code: OfferstatusCode.news,
        importModality: true,
        importInternals: true,
        importProperty: true,
        importDescrition: true,
      },
      deletedAdUp_news: {
        status_code: OfferstatusCode.historic,
        importModality: true,
        importInternals: false,
        importProperty: false,
        importDescrition: true,
      },
      deletedAdUp_draft: {
        status_code: OfferstatusCode.historic,
        importModality: true,
        importInternals: false,
        importProperty: false,
        importDescrition: true,
      },
      deletedAdUp_commercialization: {
        status_code: OfferstatusCode.historic,
        importModality: true,
        importInternals: false,
        importProperty: false,
        importDescrition: true,
      },
      deletedAdUp_historic: {
        status_code: OfferstatusCode.historic,
        importModality: true,
        importInternals: false,
        importProperty: false,
        importDescrition: true,
      },
    };

    providersImportAdConfs.set(providersIds.crwidealista, TypedJsonUtils.parse(idealistaconf,
      CloudProviderDTO.ImportAdConfigurationExt
    ));

    const individualsConf: CloudProviderDTO.ImportAdConfigurationExt = {
      bindIfPossible: true,
      activeAdCr: {
        allowAdRefresh: true,
        status_code: OfferstatusCode.news,
        mandate_type_code: OffermandatetypeCode.other,
        customer_id: null,
        importInternals: true,
        importProperty: true,
        importModality: true,
        importDescrition: true,
      },
      deletedAdCr: {
        status_code: OfferstatusCode.historic,
        mandate_type_code: null,
        importInternals: false,
        importProperty: true,
        importModality: true,
        importDescrition: true,
      },
      activeAdUp_news: {
        allowAdRefresh: true,
        importModality: true,
        importInternals: false,
        importProperty: true,
        // Si el usuario cambia algo en topbrokers, lo machacamos con lo que viene de idealista
        importDescrition: true,
      },
      activeAdUp_draft: {
        allowAdRefresh: true,
        importModality: true,
        importInternals: false,
        importProperty: true,
        // Si el usuario cambia algo en topbrokers, lo machacamos con lo que viene de idealista
        importDescrition: false,
      },
      activeAdUp_commercialization: {
        allowAdRefresh: true,
        importModality: true,
        importInternals: false,
        importProperty: false,
        // Si el usuario cambia algo en topbrokers, lo machacamos con lo que viene de idealista
        importDescrition: false,
      },
      activeAdUp_historic: {
        // No "revivimos" la oferta: la dejamos en su estado actual "historic"
        importModality: true,
        importInternals: false,
        importProperty: false,
        importDescrition: false,
      },
      deletedAdUp_news: {
        status_code: OfferstatusCode.historic,
        importModality: true,
        importInternals: false,
        importProperty: false,
        importDescrition: false,
      },
      deletedAdUp_draft: {
        importModality: true,
        importInternals: false,
        importProperty: false,
        importDescrition: false,
      },
      deletedAdUp_commercialization: {
        importModality: true,
        importInternals: false,
        importProperty: false,
        importDescrition: false,
      },
      deletedAdUp_historic: {
        importModality: true,
        importInternals: false,
        importProperty: false,
        importDescrition: false,
      },
    };

    providersImportAdConfs.set(providersIds.crwindividuals, TypedJsonUtils.parse(individualsConf,
      CloudProviderDTO.ImportAdConfigurationExt
    ));

    return providersImportAdConfs;
  }
  function buildProvidersImportAdConfs() {
    const providersImportAdConfs = new Map<string, CloudProviderDTO.ImportAdConfiguration>();
    providersImportAdConfs.set(providersIds.crwhaya, TypedJsonUtils.parse(
      {
        activeAdCr: {
          status_code: OfferstatusCode.news,
          mandate_type_code: OffermandatetypeCode.other,
          customer_id: contactsIds.haya,
          importInternals: true,
          importProperty: true,
          importModality: true,
          importDescrition: true,
        },
        deletedAdCr: {
          status_code: OfferstatusCode.historic,
          mandate_type_code: null,
          customer_id: contactsIds.haya,
          importInternals: true,
          importProperty: true,
          importModality: true,
          importDescrition: true,
        },
        activeAdUp: {
          customer_id: contactsIds.haya,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        },
        deletedAdUp: {
          status_code: OfferstatusCode.historic,
          customer_id: contactsIds.haya,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        },
        revivedAdUp: {
          /** Seguimos manteniendo la oferta local en histórico... Debemos tratar este tema */
          status_code: OfferstatusCode.historic,
          customer_id: contactsIds.haya,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        }
      } as CloudProviderDTO.ImportAdConfiguration,
      CloudProviderDTO.ImportAdConfiguration
    ));

    providersImportAdConfs.set(providersIds.crwservihabitat, TypedJsonUtils.parse(
      {
        activeAdCr: {
          status_code: OfferstatusCode.news,
          mandate_type_code: OffermandatetypeCode.other,
          customer_id: contactsIds.servihabitat,
          importInternals: true,
          importProperty: true,
          importModality: true,
          importDescrition: true,
        },
        deletedAdCr: {
          status_code: OfferstatusCode.historic,
          mandate_type_code: null,
          customer_id: contactsIds.servihabitat,
          importInternals: true,
          importProperty: true,
          importModality: true,
          importDescrition: true,
        },
        activeAdUp: {
          customer_id: contactsIds.servihabitat,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        },
        deletedAdUp: {
          status_code: OfferstatusCode.historic,
          customer_id: contactsIds.servihabitat,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        },
        revivedAdUp: {
          /** Seguimos manteniendo la oferta local en histórico... Debemos tratar este tema */
          status_code: OfferstatusCode.historic,
          customer_id: contactsIds.servihabitat,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        }
      } as CloudProviderDTO.ImportAdConfiguration,
      CloudProviderDTO.ImportAdConfiguration
    ));

    providersImportAdConfs.set(providersIds.crwsolvia, TypedJsonUtils.parse(
      {
        activeAdCr: {
          status_code: OfferstatusCode.news,
          mandate_type_code: OffermandatetypeCode.other,
          customer_id: contactsIds.solvia,
          importInternals: true,
          importProperty: true,
          importModality: true,
          importDescrition: true,
        },
        deletedAdCr: {
          status_code: OfferstatusCode.historic,
          mandate_type_code: null,
          customer_id: contactsIds.solvia,
          importInternals: true,
          importProperty: true,
          importModality: true,
          importDescrition: true,
        },
        activeAdUp: {
          customer_id: contactsIds.solvia,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        },
        deletedAdUp: {
          status_code: OfferstatusCode.historic,
          customer_id: contactsIds.solvia,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        },
        revivedAdUp: {
          /** Seguimos manteniendo la oferta local en histórico... Debemos tratar este tema */
          status_code: OfferstatusCode.historic,
          customer_id: contactsIds.solvia,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        }
      } as CloudProviderDTO.ImportAdConfiguration,
      CloudProviderDTO.ImportAdConfiguration
    ));

    providersImportAdConfs.set(providersIds.crwunicaja, TypedJsonUtils.parse(
      {
        activeAdCr: {
          status_code: OfferstatusCode.news,
          mandate_type_code: OffermandatetypeCode.other,
          customer_id: contactsIds.unicaja,
          importInternals: true,
          importProperty: true,
          importModality: true,
          importDescrition: true,
        },
        deletedAdCr: {
          status_code: OfferstatusCode.historic,
          mandate_type_code: null,
          customer_id: contactsIds.unicaja,
          importInternals: true,
          importProperty: true,
          importModality: true,
          importDescrition: true,
        },
        activeAdUp: {
          customer_id: contactsIds.unicaja,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        },
        deletedAdUp: {
          status_code: OfferstatusCode.historic,
          customer_id: contactsIds.unicaja,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        },
        revivedAdUp: {
          /** Seguimos manteniendo la oferta local en histórico... Debemos tratar este tema */
          status_code: OfferstatusCode.historic,
          customer_id: contactsIds.unicaja,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        }
      } as CloudProviderDTO.ImportAdConfiguration,
      CloudProviderDTO.ImportAdConfiguration
    ));

    providersImportAdConfs.set(providersIds.crwportalnow, TypedJsonUtils.parse(
      {
        activeAdCr: {
          status_code: OfferstatusCode.news,
          mandate_type_code: OffermandatetypeCode.other,
          customer_id: contactsIds.portalnow,
          importInternals: true,
          importProperty: true,
          importModality: true,
          importDescrition: true,
        },
        deletedAdCr: {
          status_code: OfferstatusCode.historic,
          mandate_type_code: null,
          customer_id: contactsIds.portalnow,
          importInternals: true,
          importProperty: true,
          importModality: true,
          importDescrition: true,
        },
        activeAdUp: {
          customer_id: contactsIds.portalnow,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        },
        deletedAdUp: {
          status_code: OfferstatusCode.historic,
          customer_id: contactsIds.portalnow,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        },
        revivedAdUp: {
          /** Seguimos manteniendo la oferta local en histórico... Debemos tratar este tema */
          status_code: OfferstatusCode.historic,
          customer_id: contactsIds.portalnow,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        }
      } as CloudProviderDTO.ImportAdConfiguration,
      CloudProviderDTO.ImportAdConfiguration
    ));

    providersImportAdConfs.set(providersIds.crwaliseda, TypedJsonUtils.parse(
      {
        activeAdCr: {
          status_code: OfferstatusCode.news,
          mandate_type_code: OffermandatetypeCode.other,
          customer_id: contactsIds.aliseda,
          importInternals: true,
          importProperty: true,
          importModality: true,
          importDescrition: true,
        },
        deletedAdCr: {
          status_code: OfferstatusCode.historic,
          mandate_type_code: null,
          customer_id: contactsIds.aliseda,
          importInternals: true,
          importProperty: true,
          importModality: true,
          importDescrition: true,
        },
        activeAdUp: {
          customer_id: contactsIds.aliseda,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        },
        deletedAdUp: {
          status_code: OfferstatusCode.historic,
          customer_id: contactsIds.aliseda,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        },
        revivedAdUp: {
          /** Seguimos manteniendo la oferta local en histórico... Debemos tratar este tema */
          status_code: OfferstatusCode.historic,
          customer_id: contactsIds.aliseda,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        }
      } as CloudProviderDTO.ImportAdConfiguration,
      CloudProviderDTO.ImportAdConfiguration
    ));

    providersImportAdConfs.set(providersIds.crwuci, TypedJsonUtils.parse(
      {
        activeAdCr: {
          status_code: OfferstatusCode.news,
          mandate_type_code: OffermandatetypeCode.other,
          customer_id: contactsIds.uci,
          importInternals: true,
          importProperty: true,
          importModality: true,
          importDescrition: true,
        },
        deletedAdCr: {
          status_code: OfferstatusCode.historic,
          mandate_type_code: null,
          customer_id: contactsIds.uci,
          importInternals: true,
          importProperty: true,
          importModality: true,
          importDescrition: true,
        },
        activeAdUp: {
          customer_id: contactsIds.uci,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        },
        deletedAdUp: {
          status_code: OfferstatusCode.historic,
          customer_id: contactsIds.uci,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        },
        revivedAdUp: {
          /** Seguimos manteniendo la oferta local en histórico... Debemos tratar este tema */
          status_code: OfferstatusCode.historic,
          customer_id: contactsIds.uci,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        }
      } as CloudProviderDTO.ImportAdConfiguration,
      CloudProviderDTO.ImportAdConfiguration
    ));

    providersImportAdConfs.set(providersIds.crwidealista, TypedJsonUtils.parse(
      {
        activeAdCr: {
          status_code: OfferstatusCode.news,
          importInternals: true,
          importProperty: true,
          importModality: true,
          importDescrition: true,
        },
        deletedAdCr: {
          status_code: OfferstatusCode.historic,
          importInternals: true,
          importProperty: true,
          importModality: true,
          importDescrition: true,
        },
        activeAdUp: {
          status_code: OfferstatusCode.news,
          importModality: true,
          importInternals: true,
          importProperty: true,
          importDescrition: true,
        },
        deletedAdUp: {
          status_code: OfferstatusCode.historic,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: true,
        },
        revivedAdUp: {
          status_code: OfferstatusCode.news,
          importInternals: true,
          importProperty: true,
          importModality: true,
          importDescrition: true,
        }
      } as CloudProviderDTO.ImportAdConfiguration,
      CloudProviderDTO.ImportAdConfiguration
    ));

    /** 
     * TEMAS PENDIENTES!!!
     * TODO:  Deberíamos hacer depender los cambios locales en ActiveAdUp del estado actual de la oferta local
     *    Si news->Aceptar cabmios de todo tipo
     *    Si draft/commercialization->Solo cambios en precios, imágenes y atributos de inmueble
     *    Si histórico->No tocamos nada
     * TODO: Deberíamos hacer dependerr los cambios locales en DeletedAdUp del estado actual de la oferta
     *    Si el estado es news->Pasamos a histórico
     *    En cualquier otro caso, no aceptar cambios
     *  
     * 
     * En resumen... tendremos 
     *   cr_adActive
     *   cr_adDeleted
     *   up_adActive_localNews
     *   up_adDeleted_localNews
     *   up_adActive_localDraft
     *   up_adDeleted_localDraft
     *   up_adActive_localCommerzialization
     *   up_adDeleted_localCommerzialization
     *   up_adActive_localHistoric
     *   up_adDeleted_localHistoric
     */
    providersImportAdConfs.set(providersIds.crwindividuals, TypedJsonUtils.parse(
      {
        activeAdCr: {
          status_code: OfferstatusCode.news,
          mandate_type_code: OffermandatetypeCode.other,
          customer_id: null,
          bindIfPossible: true,
          importInternals: true,
          importProperty: true,
          importModality: true,
          importDescrition: true,
        },
        deletedAdCr: {
          status_code: OfferstatusCode.historic,
          mandate_type_code: null,
          customer_id: undefined,
          importInternals: false,
          importProperty: true,
          importModality: true,
          importDescrition: true,
        },
        activeAdUp: {
          customer_id: undefined,
          importModality: true,
          importInternals: false,
          importProperty: true,
          // Si el usuario cambia algo en topbrokers, lo machacamos con lo que viene de idealista
          importDescrition: true,
        },
        deletedAdUp: {
          status_code: OfferstatusCode.historic,
          customer_id: undefined,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        },
        revivedAdUp: {
          /** No tocamos el estado local */
          status_code: undefined, // OfferstatusCode.historic,
          customer_id: undefined,
          importModality: true,
          importInternals: false,
          importProperty: false,
          importDescrition: false,
        }
      } as CloudProviderDTO.ImportAdConfiguration,
      CloudProviderDTO.ImportAdConfiguration
    ));

    return providersImportAdConfs;
  }
}


