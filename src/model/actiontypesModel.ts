import { Sql<PERSON><PERSON><PERSON>, SqlWhereHelpers } from "agentor-lib";
import { withDbCli } from "lib/AppContextUtils";
import { PoolClient } from 'pg';
import { rowToActiontype } from './common/rows2obj';
import { actiontypesFields, actiontypesTable } from "./common/schema/actiontypesTable";
import { dbSchema } from "./common/schema/dbSchema";
const { addEq, addIsNotNull } = SqlWhereHelpers;
const { buildSqlFields, buildSqlFrom } = SqlHelpers;
const c_root_alias = "actiontype";

export namespace ActiontypesModel {
  export declare type Filter = {
    id?: string,
    withService?: boolean,
    agentCanCreate?: boolean,
  }

  export const list = (dbClient?: PoolClient) => (filter: Filter) =>
    withDbCli(dbClient =>
      listActiontypes_imp(dbClient, filter)
    )(dbClient);


  async function listActiontypes_imp(dbClient: PoolClient, filter: Filter) {
    const { terms, params } = buildCondition(filter);
    const
      qryFields = buildSqlFields(dbSchema, {
        table: actiontypesTable, tableAlias: c_root_alias, allowedPaths: [
          c_root_alias,
          `${c_root_alias}_service`,
          `${c_root_alias}_service_disclaimer`,
        ]
      }).join(', '),
      qryFrom = buildSqlFrom(dbSchema,{
        table: actiontypesTable, tableAlias: c_root_alias,
        allowedPaths: [
          c_root_alias,
          `${c_root_alias}_service`,
          `${c_root_alias}_service_disclaimer`,
        ]
      });

    const qry = `
    select 
      ${qryFields}  
    from 
      ${qryFrom}
    ${terms.length !== 0 ? `
    where 
      ${terms.join(" and ")}` : ""}
    order by 
      ${c_root_alias}.${actiontypesFields.logicorder} asc
    `;

    return dbClient.query(qry, params).
      then(result =>
        result.rows.map(row => rowToActiontype(row, c_root_alias))
      );
  }


  function buildCondition(filter: Filter) {
    const { id, withService, agentCanCreate } = filter;
    let terms: string[] = [];
    let params: any[] = [];

    addEq(terms, params, `${c_root_alias}.id`, id);
    addIsNotNull(terms, `${c_root_alias}.${actiontypesFields.service_code}`, withService);
    addEq(terms, params, `${c_root_alias}."${actiontypesFields.agentCanCreate}"`, agentCanCreate);

    return { terms, params }
  }
}