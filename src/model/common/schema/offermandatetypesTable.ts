import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export const offermandatetypesTable = new TableSchema(
  "offermandatetypes",
  [
    new TableFieldSchema("code", { r, c, pk, nn }),
    new TableFieldSchema("label", { c, r, nn }),
    new TableFieldSchema("logicorder", { c, r, nn }),
  ]
);