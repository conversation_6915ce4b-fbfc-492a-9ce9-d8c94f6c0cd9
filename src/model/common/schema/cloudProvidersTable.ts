import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum cloudProvidersFields {
  id = "id",
  agent_id = "agent_id",
  name = "name",
  //type_code = "type_code",
}


export const cloudProvidersTable = new TableSchema(
  "cloud_providers",
  [
    new TableFieldSchema(cloudProvidersFields.id, { r, pk, nn }),
    new TableFieldSchema(cloudProvidersFields.name, { c, r, u, nn }),
    new TableFieldSchema(cloudProvidersFields.agent_id, { c, r, nn }),
    //new TableFieldSchema(cloudProvidersFields.type_code, { c, r, nn }),
  ],
  [
    new TableFKSchema("agent", "agents"),
  ]
);

