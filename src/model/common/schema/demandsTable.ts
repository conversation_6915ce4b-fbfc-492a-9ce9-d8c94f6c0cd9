import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum demandsFields {
  id = "id",
  agent_id = "agent_id",
  customer_id = "customer_id",
  status_code = "status_code",
  sale_allowed = "sale_allowed",
  sale_amount = "sale_amount",
  rent_allowed = "rent_allowed",
  rent_amount = "rent_amount",
  currency_code = "currency_code",
  property_id = "property_id",
  notes = "notes",
  matchingsinfo_has = "matchingsinfo_has",
  matchingsinfo_notReviewedCount = "matchingsinfo_notReviewedCount",
  created_at = "created_at",
  updated_at = "updated_at",
}

export const demandsTable = new TableSchema(
  "demands",
  [
    new TableFieldSchema(demandsFields.id, { r, pk, nn }),
    new TableFieldSchema(demandsFields.agent_id, { c, r, nn }),
    new TableFieldSchema(demandsFields.customer_id, { c, r, u }), // *+*+* nn
    //
    new TableFieldSchema(demandsFields.status_code, { c, r, u }),
    // Venta y/o Alquiler
    new TableFieldSchema(demandsFields.sale_allowed, { c, r, u, nn }),
    new TableFieldSchema(demandsFields.sale_amount, { c, r, u }),
    new TableFieldSchema(demandsFields.rent_allowed, { c, r, u, nn }),
    new TableFieldSchema(demandsFields.rent_amount, { c, r, u }),
    new TableFieldSchema(demandsFields.currency_code, { c, r, u, nn }),
    // Inmueble
    new TableFieldSchema(demandsFields.property_id, { c, r, nn }),
    // Notas privadas
    new TableFieldSchema(demandsFields.notes, { c, r, u }),
    //
    new TableFieldSchema(demandsFields.matchingsinfo_has, { r, computed: (demandAlias) => `exists (select true from matchings where demand_id=${demandAlias}.id and not reviewed)` }),
    new TableFieldSchema(demandsFields.matchingsinfo_notReviewedCount, { r, computed: (demandAlias) => `select count(*) from matchings where demand_id=${demandAlias}.id and not reviewed` }),

    new TableFieldSchema(demandsFields.created_at, { r }),
    new TableFieldSchema(demandsFields.updated_at, { r }),
  ],
  [
    new TableFKSchema("status", "demandstatuses"),
    new TableFKSchema("agent", "agents"),
    new TableFKSchema("customer", "contacts"),
    new TableFKSchema("property", "properties"),
    new TableFKSchema("currency", "currencies")
  ]
);