import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum sessionsFields {
  token = "token",
  user_id = "user_id",
  expires_at = "expires_at",
  created_at = "created_at",
  updated_at = "updated_at",
}
export const sessionsTable = new TableSchema(
  "sessions",
  [
    new TableFieldSchema(sessionsFields.token, { c, r, pk, nn }),
    new TableFieldSchema(sessionsFields.user_id, { c, r, nn }),
    new TableFieldSchema(sessionsFields.expires_at, { c, r, u, nn }),
    new TableFieldSchema(sessionsFields.created_at, { r, nn }),
    new TableFieldSchema(sessionsFields.updated_at, { r, nn }),
  ],
  [
    new TableFKSchema("user", "scrty_users"),
  ]
);
