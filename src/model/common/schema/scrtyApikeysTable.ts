import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum scrtyApikeysFields {
  token = "token",
  user_id = "user_id",
  created_at = "created_at",
  deleted_at = "deleted_at",
}
export const scrtyApikeysTable = new TableSchema(
  "scrty_apikeys",
  [
    new TableFieldSchema(scrtyApikeysFields.token, { c, r, pk, nn }),
    new TableFieldSchema(scrtyApikeysFields.user_id, { c, r, nn }),
    new TableFieldSchema(scrtyApikeysFields.created_at, { r, nn }),
    new TableFieldSchema(scrtyApikeysFields.deleted_at, { c, r, u }),
  ],
  [
    new TableFKSchema("user", "scrty_users"),
  ]
);