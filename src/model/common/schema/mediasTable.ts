import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum mediasFields {
  key = "key",
  folder = "folder",
  original_mediatype = "original_mediatype",
  original_url = "original_url",
  thumbnail_mediatype = "thumbnail_mediatype",
  thumbnail_url = "thumbnail_url",
  publishing_mediatype = "publishing_mediatype",
  publishing_url = "publishing_url",
  publishingNoWm_mediatype = "publishingNoWm_mediatype",
  publishingNoWm_url = "publishingNoWm_url",

  source_url = "source_url",
  created_at = "created_at",
  updated_at = "updated_at",
}
export const mediasTable = new TableSchema(
  "medias",
  [
    new TableFieldSchema(mediasFields.key, { c, r, pk, nn }),
    new TableFieldSchema(mediasFields.folder, { c, r, nn }),
    new TableFieldSchema(mediasFields.original_mediatype, { c, r, nn }),
    new TableFieldSchema(mediasFields.original_url, { c, r, nn }),
    new TableFieldSchema(mediasFields.thumbnail_mediatype, { c, r, u }),
    new TableFieldSchema(mediasFields.thumbnail_url, { c, r, u }),
    new TableFieldSchema(mediasFields.publishing_mediatype, { c, r, u }),
    new TableFieldSchema(mediasFields.publishing_url, { c, r, u }),
    new TableFieldSchema(mediasFields.publishingNoWm_mediatype, { c, r, u }),
    new TableFieldSchema(mediasFields.publishingNoWm_url, { c, r, u }),
    new TableFieldSchema(mediasFields.source_url, { c, r }),
    new TableFieldSchema(mediasFields.created_at, { r }),
    new TableFieldSchema(mediasFields.updated_at, { r })
  ],
  [

  ]
);

