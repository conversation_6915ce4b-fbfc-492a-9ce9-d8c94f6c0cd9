import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum propertytypesFields  {
  code = "code",
  label = "label",
  fieldsdef = "fieldsdef",
}
export const propertytypesTable = new TableSchema(
  "propertytypes",
  [
    new TableFieldSchema("code", { r, c, pk, nn }),
    new TableFieldSchema("label", { c, r, nn }),
    new TableFieldSchema("fieldsdef", { c, r, u, nn })
  ]
);