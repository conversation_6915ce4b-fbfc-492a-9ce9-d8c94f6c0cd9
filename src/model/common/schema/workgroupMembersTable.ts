import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];export enum workgroupMembersFields {
  workgroup_id = "workgroup_id",
  agent_id = "agent_id",
  can_read = "can_read",
  can_publish = "can_publish",
};

export const workgroupMembersTable = new TableSchema(
  "workgroup_members",
  [
    new TableFieldSchema(workgroupMembersFields.workgroup_id, { c, r, pk, nn }),
    new TableFieldSchema(workgroupMembersFields.agent_id, { c, r, pk, nn }),
    new TableFieldSchema(workgroupMembersFields.can_read, { c, r, u, nn }),
    new TableFieldSchema(workgroupMembersFields.can_publish, { c, r, u, nn }),
  ],
  [
    new TableFKSchema("agent", "agents"),
    new TableFKSchema("workgroup", "workgroups"),
  ]
);
