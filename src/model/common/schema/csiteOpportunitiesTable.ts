import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum csiteOpportunitiesFields {
  /** Identificador alfanumérico de 32 caracteres (base64url) generado por la BBDD */
  id="id",
  contact_id="contact_id",
  offer_id = "offer_id",
  reaction_likedit = "reaction_likedit",
  reaction_date = "reaction_date",
  created_at = "created_at",
  /** Posición relativa de creación de la oportunidad (asegura unicidad frente a lo que pasaría con created_at) */
  created_ix = "created_ix",
}
export const csiteOpportunitiesTable = new TableSchema(
  "csite_opportunities",
  [
    new TableFieldSchema(csiteOpportunitiesFields.id, { r, pk, nn }),
    new TableFieldSchema(csiteOpportunitiesFields.contact_id, { c, r, nn }),
    new TableFieldSchema(csiteOpportunitiesFields.offer_id, { c, r, nn }),
    new TableFieldSchema(csiteOpportunitiesFields.reaction_likedit, { c, r, u }),
    new TableFieldSchema(csiteOpportunitiesFields.reaction_date, { c, r, u }),
    new TableFieldSchema(csiteOpportunitiesFields.created_at, { r, nn }),
    new TableFieldSchema(csiteOpportunitiesFields.created_ix, { r, nn }),
  ],
  [
    new TableFKSchema("contact", "contacts"),
    new TableFKSchema("offer", "offers"),
  ]
);