import { DBSchema } from "agentor-lib";
import { csiteOpportunitiesFields, csiteOpportunitiesTable } from "./csiteOpportunitiesTable";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum csiteSitesFields {
  /** Identificador alfanumérico de 32 caracteres (base64url) generado por la BBDD */
  slug="slug",
  /** Contacto del site */
  contact_id="contact_id",
  /** Last Opportunity */
  lastopportunity_id="lastopportunity_id",
  
}
export const csiteSitesTable = new TableSchema(
  "csite_sites",
  [
    new TableFieldSchema(csiteSitesFields.slug, { r, pk, nn }),
    new TableFieldSchema(csiteSitesFields.contact_id, { c, r, nn }),
    new TableFieldSchema(csiteSitesFields.lastopportunity_id, { r, computed: (siteAlias) => `select so.id from ${csiteOpportunitiesTable.name} so where so.${csiteOpportunitiesFields.contact_id}=${siteAlias}.${csiteSitesFields.contact_id} order by so.${csiteOpportunitiesFields.created_ix} desc limit 1` }),
  ],
  [
    new TableFKSchema("contact", "contacts"),
    new TableFKSchema("lastopportunity", "csite_opportunities")
  ]
);