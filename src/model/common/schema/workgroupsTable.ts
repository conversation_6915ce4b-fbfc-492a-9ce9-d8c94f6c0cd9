import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];export enum workgroupsFields {
  id = "id",
  owner_id = "owner_id",
  name = "name",
  publicationecomproduct_id = "publicationecomproduct_id",
}
export const workgroupsTable = new TableSchema(
  "workgroups",
  [
    new TableFieldSchema(workgroupsFields.id, { c, r, pk, nn }),
    new TableFieldSchema(workgroupsFields.owner_id, { c, r, nn }),
    new TableFieldSchema(workgroupsFields.name, { c, r, u, nn }),
    new TableFieldSchema(workgroupsFields.publicationecomproduct_id, { c, r, u }),
  ],
  [
    new TableFKSchema("owner", "agents"),
    new TableFKSchema("publicationecomproduct", "ecom_products")
  ]
);
