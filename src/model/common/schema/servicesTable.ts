import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum servicesFields {
  code = "code",
  label = "label",
  disclaimer_has = "disclaimer_has",
  disclaimer_title = "disclaimer_title",
  disclaimer_detailHtml = "disclaimer_detailHtml",
  disclaimer_requiresAcceptance = "disclaimer_requiresAcceptance",
}

export const servicesTable = new TableSchema(
  "services",
  [
    new TableFieldSchema(servicesFields.code, { c, r, u, pk, nn }),
    new TableFieldSchema(servicesFields.label, { c, r, u, nn }),
    new TableFieldSchema(servicesFields.disclaimer_has, { c, r, u, nn }),
    new TableFieldSchema(servicesFields.disclaimer_title, { c, r, u, nn }),
    new TableFieldSchema(servicesFields.disclaimer_detailHtml, { c, r, u, nn }),
    new TableFieldSchema(servicesFields.disclaimer_requiresAcceptance, { c, r, u, nn }),
  ],
  []
);
