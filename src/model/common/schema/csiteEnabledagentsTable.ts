import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, pk, nn] = [true, true, true, true];

export enum csiteEnabledagentsFields {
  agent_id = "agent_id",
}
export const csiteEnabledagentsTable = new TableSchema(
  "csite_enabledagents",
  [
    new TableFieldSchema(csiteEnabledagentsFields.agent_id, { c, r,  pk, nn }),
  ],
  [
    new TableFKSchema("agent", "agents")
  ]
);