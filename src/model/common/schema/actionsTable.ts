import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

export enum actionsFields {
  id = "id",
  type_id = "type_id",
  description = "description",
  agent_id = "agent_id",
  source_id = "source_id",
  offer_id = "offer_id",
  contact_id = "contact_id",
  extradata = "extradata",
  when = "when",
  done = "done",
  created_at = "created_at",
}

const [c, r, u, pk, nn] = [true, true, true, true, true];
export const actionsTable = new TableSchema(
  "actions",
  [
    new TableFieldSchema(actionsFields.id, { r, pk, nn }),
    new TableFieldSchema(actionsFields.type_id, { c, r, u, nn }),
    new TableFieldSchema(actionsFields.description, { c, r, u }),
    new TableFieldSchema(actionsFields.agent_id, { c, r, nn }),
    new TableFieldSchema(actionsFields.source_id, { c, r }),
    new TableFieldSchema(actionsFields.offer_id, { c, r, u }),
    new TableFieldSchema(actionsFields.contact_id, { c, r, u }),
    new TableFieldSchema(actionsFields.extradata, { c, r, u }),
    new TableFieldSchema(actionsFields.when, { c, r, u, nn }),
    new TableFieldSchema(actionsFields.done, { c, r, u }),
    new TableFieldSchema(actionsFields.created_at, { r })
  ],
  [
    new TableFKSchema("type", "actiontypes"),
    new TableFKSchema("agent", "agents"),
    new TableFKSchema("source", "agents"),
    new TableFKSchema("offer", "offers"),
    new TableFKSchema("contact", "contacts")
  ]
);

