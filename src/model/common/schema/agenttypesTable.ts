
import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum agenttypesFields {
  code = "code",
  name = "name",
}

export const agenttypesTable = new TableSchema(
  "agenttypes",
  [
    new TableFieldSchema(agenttypesFields.code, { r, pk, nn }),
    new TableFieldSchema(agenttypesFields.name, { c, r, u }),
  ],
  [
  ]
);
