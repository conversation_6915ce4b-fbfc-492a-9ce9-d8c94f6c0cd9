import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export const ecomAccountsTable = new TableSchema(
  "ecom_accounts",
  [
    new TableFieldSchema("id", { r, pk, nn }),
    new TableFieldSchema("agent_id", { c, r, nn }),
    new TableFieldSchema("balance", { c, r, u, nn }),
  ],
  [
    new TableFKSchema("agent", "agents"),
  ]
);

