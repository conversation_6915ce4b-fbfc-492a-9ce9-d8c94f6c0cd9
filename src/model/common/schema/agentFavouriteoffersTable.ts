
import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum agentFavouriteoffersFields {
  agent_id = "agent_id",
  offer_id = "offer_id",
}

const fldN = agentFavouriteoffersFields;

export const agentFavouriteoffersTable = new TableSchema(
  "agent_favouriteoffers",
  [
    new TableFieldSchema(fldN.agent_id, { c, r, pk, nn }),
    new TableFieldSchema(fldN.offer_id, { c, r, pk, nn }),
  ],
  [
    new TableFKSchema("agent", "agents"),
    new TableFKSchema("offer", "offers"),
  ]
);