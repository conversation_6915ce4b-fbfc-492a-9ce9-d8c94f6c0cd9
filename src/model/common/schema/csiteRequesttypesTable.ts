import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum csiteRequesttypesFields {
  /** Identificador alfanumérico de 32 caracteres (base64url) generado por la BBDD */
  code="code",
  label="label",
}
export const csiteRequesttypesTable = new TableSchema(
  "csite_requesttypes",
  [
    new TableFieldSchema(csiteRequesttypesFields.code, { c, r, pk, nn }),
    new TableFieldSchema(csiteRequesttypesFields.label, { c, r, nn }),
  ],
  [
  ]
);