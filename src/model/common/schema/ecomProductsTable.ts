import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum ecomProductsFields {
	id = "id",
	account_id = "account_id",
	name = "name",
	description = "description",
	forInternalUseOnly = "forInternalUseOnly",
	firstpayment_amount = "firstpayment_amount",
	firstpayment_days = "firstpayment_days",
	dailyamount = "dailyamount",
}

export const ecomProductsTable = new TableSchema(
	"ecom_products",
	[
		new TableFieldSchema(ecomProductsFields.id, { c, r, pk, nn }),
		new TableFieldSchema(ecomProductsFields.account_id, { c, r, nn }),
		new TableFieldSchema(ecomProductsFields.name, { c, r, u, nn }),
		new TableFieldSchema(ecomProductsFields.description, { c, r, u, nn }),
		new TableFieldSchema(ecomProductsFields.forInternalUseOnly, { c, r, nn }),
		new TableFieldSchema(ecomProductsFields.firstpayment_amount, { c, r, u, nn }),
		new TableFieldSchema(ecomProductsFields.firstpayment_days, { c, r, u, nn }),
		new TableFieldSchema(ecomProductsFields.dailyamount, { c, r, u, nn }),
	],
	[
		new TableFKSchema("account", "ecom_accounts"),
	]
);

