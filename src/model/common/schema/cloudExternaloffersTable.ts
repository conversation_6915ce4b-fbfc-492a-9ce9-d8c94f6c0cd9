import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum cloudExternaloffersFields {
  provider_id = "provider_id",
  id = "id",
  revision = "revision",
  localoffer_id = "localoffer_id",
}

export const cloudExternaloffersTable = new TableSchema(
  "cloud_externaloffers",
  [
    new TableFieldSchema(cloudExternaloffersFields.provider_id, { c, r, pk, nn }),
    new TableFieldSchema(cloudExternaloffersFields.id, { c, r, pk, nn }),
    new TableFieldSchema(cloudExternaloffersFields.revision, { c, r, u, nn }),
    new TableFieldSchema(cloudExternaloffersFields.localoffer_id, { c, r, nn })
  ],
  [
    new TableFKSchema("provider", "cloud_providers"),
    new TableFKSchema("localoffer", "offers"),
  ]
);

