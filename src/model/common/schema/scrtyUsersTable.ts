import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum scrtyUsersFields {
  id = "id",
  name = "name",
  agent_id="agent_id"
}
export const scrtyUsersTable = new TableSchema(
  "scrty_users",
  [
    new TableFieldSchema(scrtyUsersFields.id, { c, r, pk, nn }),
    new TableFieldSchema(scrtyUsersFields.name, { c, r, nn }),
    new TableFieldSchema(scrtyUsersFields.agent_id, { c, r }),
  ],
  [
    new TableFKSchema("agent", "agents")
  ]
);
