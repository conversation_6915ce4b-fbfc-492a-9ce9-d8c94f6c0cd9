import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum propertymediasFields {
  property_id = "property_id",
  media_key = "media_key",
  isFavourite = "isFavourite",
}

export const propertymediasTable = new TableSchema(
  "propertymedias",
  [
    new TableFieldSchema(propertymediasFields.property_id, { c, r, pk, nn }),
    new TableFieldSchema(propertymediasFields.media_key, { c, r, pk, nn }),
    new TableFieldSchema(propertymediasFields.isFavourite, { c, r, u, nn })
  ],
  [
    new TableFKSchema("property", "properties"),
    new TableFKSchema("media", "medias"),
  ]
);
