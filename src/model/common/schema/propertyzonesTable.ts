import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export const propertyzonesTable = new TableSchema(
  "propertyzones",
  [
    new TableFieldSchema("id", { c, r, pk, nn }),
    new TableFieldSchema("parent_id", { c, r }),
    new TableFieldSchema("name", { c, r, u, nn }),
    new TableFieldSchema("hasChildren", { r, computed: (propertyzoneAlias) => `exists (select true from propertyzones where parent_id=${propertyzoneAlias}.id)` }),
  ],
  [
    new TableFKSchema("parent", "propertyzones"),
  ]
);
