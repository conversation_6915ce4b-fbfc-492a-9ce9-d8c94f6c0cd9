import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum csiteOfferChangesFields {
  id="id",
  offer_id = "offer_id",
  op_code="op_code",
  created_at="created_at",
}
export const csiteOfferChangesTable = new TableSchema(
  "csite_offer_changes",
  [
    new TableFieldSchema(csiteOfferChangesFields.id, { r, pk, nn }),
    new TableFieldSchema(csiteOfferChangesFields.offer_id, { r, nn }),
    new TableFieldSchema(csiteOfferChangesFields.op_code, { r, nn }),
    new TableFieldSchema(csiteOfferChangesFields.created_at, { r, nn }),
  ],
  [
    new TableFKSchema("offer", "offers")
  ]
);