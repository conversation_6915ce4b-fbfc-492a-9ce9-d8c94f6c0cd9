import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export const propertysubtypesTable = new TableSchema(
  "propertysubtypes",
  [
    new TableFieldSchema("code", { r, c, pk, nn }),
    new TableFieldSchema("type_code", {r, c, nn}),
    new TableFieldSchema("label", { c, r, nn }),   
  ],
  [
    new TableFKSchema("type", "propertytypes"),
  ]
);