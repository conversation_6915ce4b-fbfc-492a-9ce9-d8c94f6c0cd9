import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum cloudRefreshcallsFields {
  key = "key",
  externaloffer_provider_id = "externaloffer_provider_id",
  externaloffer_id = "externaloffer_id",
  request_when = "request_when",
  response_when = "response_when",
  response_status = "response_status",
}

const flds = cloudRefreshcallsFields;

export const cloudRefreshcallsTable = new TableSchema(
  "cloud_refreshcalls",
  [
    new TableFieldSchema(flds.key, { c, r, pk, nn }),
    new TableFieldSchema(flds.externaloffer_provider_id, { c, r, nn }),
    new TableFieldSchema(flds.externaloffer_id, { c, r, nn }),
    new TableFieldSchema(flds.request_when, { c, r, nn }),
    new TableFieldSchema(flds.response_when, { c, u, r }),
    new TableFieldSchema(flds.response_status, { c, u, r }),
  ],
  [
    new TableFKSchema("externaloffer", "cloud_externaloffers"),
  ]
);

