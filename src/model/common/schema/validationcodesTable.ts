
import { DBSchema } from "agentor-lib";
const { TableSchema, TableFieldSchema } = DBSchema;

const [c, r, pk, nn] = [true, true, true, true];

export const validationcodesTable = new TableSchema(
  "validationcodes",
  [
    new TableFieldSchema("code", { c, r, pk, nn }),
    // Field associated to foreign key "agent" (this is a convention): <ForeigkKeyName>_<ReferencedTabkePKField>
    new TableFieldSchema("item_value", { c, r, pk, nn }),
    new TableFieldSchema("item_type", { c, r, pk, nn }),
    new TableFieldSchema("expires_at", { c, r, nn }),
    new TableFieldSchema("created_at", { r, nn }),
  ],
  [
  ]
);