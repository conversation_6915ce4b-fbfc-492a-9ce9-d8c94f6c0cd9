import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export const provincesTable = new TableSchema(
  "provinces",
  [
    new TableFieldSchema("code", { c, r, pk, nn }),
    new TableFieldSchema("country_code", { c, r, nn }),
    new TableFieldSchema("label", { c, r, u })
  ],
  [
    new TableFKSchema("country", "countries"),
  ]
);
