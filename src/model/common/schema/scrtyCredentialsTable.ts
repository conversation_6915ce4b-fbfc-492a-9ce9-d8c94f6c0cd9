import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum scrtyCredentialsFields {
  username = "username",
  password = "password",
  user_id = "user_id"
}
export const scrtyCredentialsTable = new TableSchema(
  "scrty_credentials",
  [
    new TableFieldSchema(scrtyCredentialsFields.username, { c, r, pk, nn }),
    new TableFieldSchema(scrtyCredentialsFields.password, { c, u, nn }), // Password can be created/updated, but NOT readed (can't be on a "select")
    new TableFieldSchema(scrtyCredentialsFields.user_id, { c, r, nn }),
  ],
  [
    new TableFKSchema("user", "scrty_users")
  ]
);
