import { DBSchema } from "agentor-lib";

const { TableSchema, TableFieldSchema, TableFKSchema } = DBSchema;

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum contactsFields {
  id = "id",
  agent_id = "agent_id",
  firstName = "firstName",
  lastName = "lastName",
  mobile = "mobile",
  email = "email",
  isBankServicer = "isBankServicer",
  notes = "notes",
  updated_at = "updated_at",
  created_at = "created_at",
  /** Campo calculado */
  siteSlug = "siteSlug",
  /** Campo interno (solo para condiciones where) calculado por trigger */
  computedSearchString = "computedSearchString",
}

const fldN = contactsFields;

export const contactsTable = new TableSchema(
  "contacts",
  [
    new TableFieldSchema(fldN.id, { r, pk, nn }),
    // Field associated to foreign key "agent" (this is a convention): <ForeigkKeyName>_<ReferencedTabkePKField>
    new TableFieldSchema(fldN.agent_id, { c, r, nn }),
    new TableFieldSchema(fldN.firstName, { c, r, u, nn }),
    new TableFieldSchema(fldN.lastName, { c, r, u }),
    new TableFieldSchema(fldN.mobile, { c, r, u }),
    new TableFieldSchema(fldN.email, { c, r, u }),
    new TableFieldSchema(fldN.isBankServicer, { c, r, u, nn }),
    new TableFieldSchema(fldN.siteSlug, {
			r, computed: (contactAlias) => `
				select 
          site.slug
				from 
          csite_sites site
        where 
          site.contact_id=${contactAlias}.id
				`
		}),
    new TableFieldSchema(fldN.computedSearchString, {}),
    new TableFieldSchema(fldN.notes, { c, r, u }),
    new TableFieldSchema(fldN.updated_at, { r, nn }),
    new TableFieldSchema(fldN.created_at, { r, nn })
  ],
  [
    new TableFKSchema("agent", "agents"),
  ]
);