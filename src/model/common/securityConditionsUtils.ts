import * as pg from 'pg';
import { SqlWhereHelpers } from "agentor-lib";

import { agentFavouriteoffersFields, agentFavouriteoffersTable } from "./schema/agentFavouriteoffersTable";
import { workgroupOffersFields, workgroupOffersTable } from "./schema/workgroupOffersTable";

const { addEq, addNotIn } = SqlWhereHelpers;
export namespace SecurityCoditionsUtils {
  export declare type AccessorCondition = {
    /**
      * The agent that performs the query.
      * An agent can see:
      * - it's own offers 
      * - offers shared in workgroups where he is member (and can read)
      */
    id: string,
    /**
     * Incluir ofertas que sean mías.
     * Si se omite, se asume true
     */
    includeMine?: boolean,
    /**
     * Incluir ofertas que no sean mías.
     * Si se omite, se asume false
     */
    includeNotMine?: boolean,

    /**
     * Incluir ofertas que no sean mías pero que han sido marcadas como favoritas.
     * Si se omite, se asume false
     */
    includeNotMineFavourites?: boolean,

    /**
     * @summary
     * Conjunto de grupos de trabajo que no deben tenerse en cuenta si se indica @see includeNotMine=true o @see includeNotMineIfFavourite=true
     * Por defecto, se asume []
     * nota: una oferta puede ser compartida en varios grupos de trabajo... que se excluya uno de ellos no implica que todas sus ofertas sean excluídas también (pueden leerse a través de otro grupo)
     */
    omitWorkgroupIds?: string[],
  }


  /**
   * 
   * Añade los términos y parámetros que permite ver a un agente las ofertas que realmente puede ver (sean suyas o estén compartidas en un grupo del que es miembro)
   * @param qryTerms Terminos SQL (and terms) de la consulta
   * @param sqlParams Valores de los parámetros de la consulta
   * @param options Opciones para establecer el filtro
   */
  export function addOfferAccessorTerms(/*inout*/qryTerms: string[], /*inout*/sqlParams: any[], options: {
    agentIdAlias: string,
    offerIdAlias: string,
    accessorCondition?: AccessorCondition
  }): (dbCli: pg.PoolClient) => Promise<void> {
    return async dbCli => {
      const { agentIdAlias, offerIdAlias, accessorCondition } = options;
      if (accessorCondition !== void 0) {
        let {
          id: me_id,
          includeMine = true,
          includeNotMine = false,
          includeNotMineFavourites = false,
          omitWorkgroupIds = [],
        } = accessorCondition;

        if (includeMine && !includeNotMine && !includeNotMineFavourites) {
          sqlParams.push(me_id);
          qryTerms.push(`${agentIdAlias}=$${sqlParams.length}`);
        } else {
          let unionTerms = [];
          if (includeNotMineFavourites) {
            sqlParams.push(me_id);
            unionTerms.push(`
              select 
                fo.${agentFavouriteoffersFields.offer_id} 
              from 
                ${agentFavouriteoffersTable.name} fo 
              where 
                fo.${agentFavouriteoffersFields.agent_id}=$${sqlParams.length} 
            `);
          }
          if (includeNotMine && !includeNotMineFavourites) {
            // NOTA: Es más eficiente obtener primero todos los workgroups de los que puedes leer el agente y añadirlos a la condición sobre "workgroup_offers", 
            //       que añadir una join en la subselect contra workgroup_members
            const wgIds = await listWorkgroupsIds({ myAgentId: me_id, myAgentCanRead: true, omitWorkgroupIds })(dbCli);
            if (wgIds.length !== 0) {
              sqlParams.push(wgIds);
              unionTerms.push(`
                select 
                  wgo.${workgroupOffersFields.offer_id} 
                from 
                  ${workgroupOffersTable.name} wgo 
                where 
                  wgo.${workgroupOffersFields.member_workgroup_id} = ANY($${sqlParams.length}) 
              `);
            }
          }

          if (includeMine) {
            // Debería añadirse siempre... pero es terrible en cuanto a eficiencia si lo mezclamos con Favourites
            sqlParams.push(me_id);
            unionTerms.push(`
              select 
                id 
              from 
                offers 
              where 
                agent_id=$${sqlParams.length}
            `);
          }

          if (unionTerms.length !== 0)
            qryTerms.push(`${offerIdAlias} in ( ${unionTerms.join(" union ")} )`);
          else
            qryTerms.push("false");
        }
      }
    }

  }

  /**
   * Obtener identificadores de todos los workgroups de los que el agente puede leer ofertas.
   */
  function listWorkgroupsIds(filter: { myAgentId: string, myAgentCanRead: boolean, omitWorkgroupIds: string[] }): (dbCli: pg.PoolClient) => Promise<string[]> {
    // 1.- Obtener todos los grupos de los que el agente es miembro.
    var params: string[] = [];
    var terms: string[] = ["true"];
    addNotIn(terms, params, "wgm.workgroup_id", filter.omitWorkgroupIds);
    addEq(terms, params, "wgm.agent_id", filter.myAgentId);
    addEq(terms, params, "wgm.can_read", filter.myAgentCanRead);
    //addEq(terms, params, "wgm.can_publish", filter.myAgentCanPublish);
    const qry = `select distinct wgm.workgroup_id from workgroup_members wgm where ${terms.join(" and ")}`;
    return dbClient => dbClient.query(qry, params).then(result => result.rows.map(row => row["workgroup_id"] as string));
  }




  /**
   * Añade los términos y parámetros que permite ver a un agente las demandas que realmente puede ver (sean suyas o estén compartidas en un grupo del que es miembro)
   * NOTA: Las demandas no pueden ser, actualmente, compartidas en un grupo de trabajo por lo que solo se usa un "subset" del accessorCondition (id e includeMin)
   * @param terms 
   * @param params 
   * @param options 
   */
  export function addDemandAccessorCondition(terms: string[], params: any[], options: {
    agentIdAlias: string,
    accessorCondition?: { id: string, includeMine?: boolean }
  }) {
    const { agentIdAlias, accessorCondition } = options;
    if (accessorCondition !== void 0) {
      const {
        id: me_id,
        includeMine = true,
      } = accessorCondition;
      let orTerms = [];
      if (includeMine) {
        params.push(me_id);
        orTerms.push(`${agentIdAlias}=$${params.length}`);
      } else {
        orTerms.push("false");
      }
      if (orTerms.length !== 0) {
        terms.push(`(${orTerms.join(" or ")})`);
      }
    }
  }

}