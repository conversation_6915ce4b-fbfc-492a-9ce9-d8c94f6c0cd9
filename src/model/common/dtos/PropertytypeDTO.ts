import { MultilingualStrDTO } from "./MultilingualStrDTO";
import { PropertysubtypeDTO } from "./PropertysubtypeDTO";

export enum PropertytypeCode  {
  house = "house",
  flat = "flat"
};
export namespace PropertytypeCode {
  export function toString(code: PropertytypeCode): string {
    return PropertytypeCode[code];
  }

  export function parse(code: string): PropertytypeCode {
    const enumValue = (<any>PropertytypeCode)[code];
    if (enumValue === undefined) {
      throw new Error(`"${code}" can't be parsed to PropertytypeCode`);
    } else {
      return enumValue as PropertytypeCode;
    }
  }
}
//export declare type PropertytypeCode = PropertytypeCodeEnum.house | PropertytypeCodeEnum.flat
export declare type PropertytypeDTO = {
  code?: PropertytypeCode,
  label?: MultilingualStrDTO,
  subtypes?: PropertysubtypeDTO[]
}

