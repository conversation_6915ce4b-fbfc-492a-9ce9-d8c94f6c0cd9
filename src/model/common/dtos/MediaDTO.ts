export declare type MediaDTO = {
	key?: string,
	folder?: string,
	original?: MediaFileDTO,
	thumbnail?: MediaFileDTO | null,
	publishing?: MediaFileDTO | null,
  publishingNoWm?: MediaFileDTO | null,
  source?: ExternalMediaFileDTO | null
}

export declare type MediaFileDTO = {
	url?: string,
	mediatype?: string,
}
export declare type ExternalMediaFileDTO = {
  url?: string,
	//mediatype?: string|null,
}

