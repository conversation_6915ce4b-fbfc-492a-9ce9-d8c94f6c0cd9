import { jsonMember, jsonObject } from "typedjson";
import { AgentDTO } from "./AgentDTO";
import { CloudProvidertypeDTO } from "./CloudProvidertypeDTO";
import { OffermandatetypeCode } from "./OffermandatetypeDTO";
import { OfferstatusCode } from "./OfferstatusDTO";

import { ErrUtils } from "agentor-lib";
const { doThrow } = ErrUtils;

export declare type CloudProviderDTO = {
  id: string,
  name?: string,
  /**
   * Tipo de proveedor
   */
  type?: CloudProvidertypeDTO,

  /**
   * Agente al que afecta el proveedor (Será el agente de las ofertas importadas)
   */
  agent?: AgentDTO,

};


export namespace CloudProviderDTO {

  @jsonObject
  export class ImportAdOperation {
    /**
     * Si la importanción lo cree necesario, puede solicitar al servicio de crawling que refresque el anuncio para obtener más detalles.
     * Pese a todo, el anuncio se importará en su estado actual confiando en que el detalle obtenido llegará en el futuro.
     */
    @jsonMember(Boolean)
    allowAdRefresh?: boolean
    /**
     * Cuando un nuevo anuncio es detectado y está activo, intentar "enlazarlo" con uno local si es posible.
     * El enlace se basará en la URL del anuncio.
     * No se enlazan anuncios ya enlazados
     */
    @jsonMember(Boolean)
    bindIfPossible?: boolean
    /** A qué cliente debe vincularse la oferta.   undefined para indicar "no tocar", null para indicar "sin cliente", "by_phonumber" para indicar que el cliente debe buscarse por teléfono*/
    @jsonMember(String)
    customer_id?: string | null | undefined
    /**  
     * En qué estado dejar la oferta 
     * Nota sobre la deserialización:  Los valores del enumerado son strings, así que lo asignamos directamente previa verificación de que es un valor válido
     */
    @jsonMember(String, { deserializer: (v: any) => typeof v === "string" ? OfferstatusCode.parse(v) : v === void 0 ? void 0 : doThrow(new TypeError(`${v}:${typeof v} not allowed`)) })
    status_code?: OfferstatusCode | undefined
    /** Qué tipo de mandato emplear.  NULL para que no haya mandato */
    @jsonMember(String, { deserializer: (v: any) => typeof v === "string" ? OffermandatetypeCode.parse(v) : v === void 0 || v === null ? v : doThrow(new TypeError(`${typeof v} not allowed`)) })
    mandate_type_code?: OffermandatetypeCode | null | undefined
    /** Importar información sobre venta/alquiler*/
    @jsonMember(Boolean, { isRequired: true })
    importModality!: boolean
    /** Importar toda la información del inmueble */
    @jsonMember(Boolean, { isRequired: true })
    importProperty!: boolean
    @jsonMember(Boolean, { isRequired: true })
    importDescrition!: boolean
    /** Importar datos internos: Notas, Source */
    @jsonMember(Boolean, { isRequired: true })
    importInternals!: boolean
  }
  @jsonObject
  export class ImportAdConfiguration {

    /** Cuando un nuevo anuncio es detectado y está activo*/
    @jsonMember(ImportAdOperation)
    activeAdCr!: ImportAdOperation
    /** Cuando un nuevo anuncio es detectado pero ya ha sido retirado */
    @jsonMember(ImportAdOperation)
    deletedAdCr!: ImportAdOperation
    /** Cuando un anuncio que conocemos ha cambiado y está activo */
    @jsonMember(ImportAdOperation)
    activeAdUp!: ImportAdOperation
    /** Cuando un anuncio que conocemos ha cambiado pero ha sido retirado */
    @jsonMember(ImportAdOperation)
    deletedAdUp!: ImportAdOperation
    /** Cuando un anuncio que conocemos pasa de retirado a activo */
    @jsonMember(ImportAdOperation)
    revivedAdUp!: ImportAdOperation
  }

  @jsonObject
  export class ImportAdConfigurationExt {
    /**
     * Cuando un nuevo anuncio es detectado, antes de crearlo localmente, intenta buscar un candidato local y lo enlaza.  Tras ello, se aplicarán las reglas de Up adecuadas
     * El enlace se basará en la URL del anuncio.
     * No se enlazan anuncios ya enlazados
     */
    @jsonMember(Boolean)
    bindIfPossible?: boolean
    /** Cuando un nuevo anuncio es detectado y está activo*/
    @jsonMember(ImportAdOperation)
    activeAdCr!: ImportAdOperation
    /** Cuando un anuncio es detectado, pero no está activo (estado deleted en crawler) */
    @jsonMember(ImportAdOperation)
    deletedAdCr!: ImportAdOperation
    /** Cuando un anuncio que conocemos ha cambiado y está activo */
    @jsonMember(ImportAdOperation)
    activeAdUp_news!: ImportAdOperation
    @jsonMember(ImportAdOperation)
    activeAdUp_draft!: ImportAdOperation
    @jsonMember(ImportAdOperation)
    activeAdUp_commercialization!: ImportAdOperation
    @jsonMember(ImportAdOperation)
    activeAdUp_historic!: ImportAdOperation
    /** Cuando un anuncio que conocemos se ha retirado en el origen */
    // El anuncio local está en estado news
    @jsonMember(ImportAdOperation)
    deletedAdUp_news!: ImportAdOperation
    // El anuncio local está en estado draft
    @jsonMember(ImportAdOperation)
    deletedAdUp_draft!: ImportAdOperation
    // El anuncio local está en estado comercialización
    @jsonMember(ImportAdOperation)
    deletedAdUp_commercialization!: ImportAdOperation
    // El anuncio local está en estado histórico
    @jsonMember(ImportAdOperation)
    deletedAdUp_historic!: ImportAdOperation
    
  }

}