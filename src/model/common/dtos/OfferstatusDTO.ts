import { MultilingualStrDTO } from "./MultilingualStrDTO";

export enum OfferstatusCode {
  // Noticia inmobiliaria
  news = "news",
  // Oferta en preparación (Borrador)
  draft = "draft",
  // Comercializando la oferta
  commercialization = "commercialization",
  // En histórico
  historic = "historic"
}

export namespace OfferstatusCode {
  export function toString(code: OfferstatusCode): string {
    return OfferstatusCode[code];
  }
  

  export const activeCodes:OfferstatusCode[] = [
    OfferstatusCode.news,
    // Cualquier trabajo previo a la comercialización
    //   Localizando inmueble/propietario/...
    //   Captando la operación (intentando obtener un mandato)
    // ...
    OfferstatusCode.draft,
    // Comercializando la oferta
    OfferstatusCode.commercialization,
  ];

  export function parse(status: string): OfferstatusCode {
    const enumValue = (<any>OfferstatusCode)[status];
    if (enumValue === undefined) {
      throw new Error(`"${status}" can't be parsed to OfferStatusCode`);
    } else {
      return enumValue as OfferstatusCode;
    }

  }
}

export declare type OfferstatusDTO = {
  code?: OfferstatusCode,
  label?: MultilingualStrDTO
}