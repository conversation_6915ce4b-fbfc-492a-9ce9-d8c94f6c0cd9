import { asNullOptString, asOptBoolean, asOptString } from "agentor-lib";
import { AgentDTO, asAgentDTO } from "./AgentDTO";

export declare type ContactDTO = {
  id?: string,
  agent?: AgentDTO,
  firstName?: string,
  lastName?: string | null,
  email?: string | null,
  mobile?: string | null,
  isBankServicer?: boolean,
  notes?: string | null,
  // Campo calculado
  siteSlug?: string | null,
};

export declare type ContactPKDTO = {
  id: string
}
export function asContactDTO(jsonObj: any | undefined): ContactDTO | undefined {
  return jsonObj ?? {
    id: asOptString(jsonObj.id),
    agent: asAgentDTO(jsonObj.agent),
    firstName: asOptString(jsonObj.firstname),
    lastName: asNullOptString(jsonObj.lastName),
    email: asNullOptString(jsonObj.email),
    mobile: asNullOptString(jsonObj.mobile),
    isBankServicer: asOptBoolean(jsonObj.isBankServicer),
    notes: asNullOptString(jsonObj.notes),
    siteSlug: asNullOptString(jsonObj.siteSlug),
  };
}