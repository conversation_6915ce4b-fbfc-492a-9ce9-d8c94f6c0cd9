import { GeolocD<PERSON> } from "./GeolocDTO";
import { MediaDTO } from "./MediaDTO";
import { PropertyAddressDTO } from "./PropertyAddressDTO";
import { PropertyAttributesDTO } from "./PropertyAttributesDTO";
import { PropertymediaDTO } from "./PropertymediaDTO";
import { PropertysubtypeDTO } from "./PropertysubtypeDTO";
import { PropertytypeDTO } from "./PropertytypeDTO";
import { PropertyzoneDTO } from "./PropertyzoneDTO";

export declare type PropertyDTO = {
  id?: string,
  type?: PropertytypeDTO,
  subtype?: PropertysubtypeDTO | null,
  cadastralReference?: string | null,
  favouritePicture?: MediaDTO | null,
  zone?: PropertyzoneDTO | null,
  address?: PropertyAddressDTO,
  location?: GeolocDTO | null,
  attributes?: PropertyAttributesDTO,
  /**
   * Relación 1 -> N.  
   */
  propertymedias?: PropertymediaDTO[],
}