import { ErrUtils } from "agentor-lib";
const { doThrowError } = ErrUtils;

export enum PropertyStatusCode {
  new = "new",
  second_hand = "second_hand"
}
export namespace PropertyStatusCode {
  export const toString = (code: PropertyStatusCode): string => PropertyStatusCode[code];
  export const parse = (code: string) => ((<any>PropertyStatusCode)[code] ?? doThrowError(`"${code}" can't be parsed to PropertyStatusCode`)) as PropertyStatusCode;
}
export enum ConservationStatusCode {
  mint = "mint",
  good = "good",
  reformed = "reformed",
  to_reform = "to_reform"
}
export namespace ConservationStatusCode {
  export const toString = (code: ConservationStatusCode): string => ConservationStatusCode[code];
  export const parse = (code: string) => ((<any>ConservationStatusCode)[code] ?? doThrowError(`"${code}" can't be parsed to ConservationStatusCode`)) as ConservationStatusCode;
}
export enum ExternalJoineryCode {
  aluminium = "aluminium",
  wood = "wood",
  pvc = "pvc",
  other = "other"
}
export namespace ExternalJoineryCode {
  export const toString = (code: ExternalJoineryCode): string => ExternalJoineryCode[code];
  export const parse = (code: string) => ((<any>ExternalJoineryCode)[code] ?? doThrowError(`"${code}" can't be parsed to ExternalJoineryCode`)) as ExternalJoineryCode;
}
export enum GroundCode {
  gres = "gres",
  marble = "marble",
  carpet = "carpet",
  parquet = "parquet",
  /** Tarima flotante */
  laminatedFlooring = "laminatedFlooring",
  /** Tarima maciza */
  solidFlooring = "solidFlooring",
  terrazzo = "terrazzo"
}
export namespace GroundCode {
  export const toString = (code: GroundCode): string => GroundCode[code];
  export const parse = (code: string) => ((<any>GroundCode)[code] ?? doThrowError(`"${code}" can't be parsed to GroundCode`)) as GroundCode;
}
export enum AirConditioningCode {
  none = "none",
  cold = "cold",
  coldAndHeat = "coldAndHeat"
}
export namespace AirConditioningCode {
  export const tryParse = (code: string) => (<any>AirConditioningCode)[code] as AirConditioningCode | undefined;
}
export enum HeatingCode {
  none = "none",
  central = "central",
  electric = "electric",
  naturalGas = "naturalGas",
  gasoil = "gasoil"
}
export namespace HeatingCode {
  export const toString = (code: HeatingCode): string => HeatingCode[code];
  export const tryParse = (code: string) => (<any>HeatingCode)[code] as HeatingCode | undefined
  export const parse = (code: string) => tryParse(code) ?? doThrowError(`"${code}" can't be parsed to HeatingCode`);
}
export enum GardenCode {
  none = "none", own = "own", community = "community"
}

export namespace GardenCode {
  export const toString = (code: GardenCode): string => GardenCode[code];
  export const parse = (code: string) => ((<any>GardenCode)[code] ?? doThrowError(`"${code}" can't be parsed to GardenCode`)) as GardenCode;
}
export enum NoneOwnCommunity {
  none = "none", own = "own", community = "community"
}
export namespace NoneOwnCommunity {
  export const toString = (code: NoneOwnCommunity): string => NoneOwnCommunity[code];
  export const tryParse = (code: string) => ((<any>NoneOwnCommunity)[code]) as NoneOwnCommunity | undefined;
  export const parse = (code: string) => tryParse(code) ?? doThrowError(`"${code}" can't be parsed to NoneOwnCommunity`);
}
export enum FacadeCode {
  interior = "interior", exterior = "exterior"
}
export namespace FacadeCode {
  export const toString = (code: FacadeCode): string => FacadeCode[code];
  export const tryParse = (code: string) => (<any>FacadeCode)[code] as FacadeCode | undefined;
  export const parse = (code: string) => tryParse(code) ?? doThrowError(`"${code}" can't be parsed to FacadeCode`);
}
export enum OrientationCode {
  north = "north", northeast = "northeast", east = "east", southeast = "southeast", south = "south", southwest = "southwest", west = "west", northwest = "northwest"
}
export namespace OrientationCode {
  export const toString = (code: OrientationCode): string => OrientationCode[code];
  export const tryParse = (code: string) => (<any>OrientationCode)[code] as OrientationCode | undefined;
  export const parse = (code: string) => OrientationCode.tryParse(code) ?? doThrowError(`"${code}" can't be parsed to OrientationCode`) as OrientationCode;
}
export enum FloorCode {
  /** Sótano */
  basement = "basement",
  /** Semisótano */
  semibasement = "semibasement",
  /** Planta baja */
  ground = "ground",
  /** Entresuelo */
  mezzanine = "mezzanine",
  /** Principal */
  main = "main",
  /** 1ª */
  f1 = "1",
  /** 2ª */
  f2 = "2",
  f3 = "3",
  f4 = "4",
  f5 = "5",
  f6 = "6",
  f7 = "7", f8 = "8", f9 = "9", f10 = "10",
  f11 = "11", f12 = "12", f13 = "13", f14 = "14", f15 = "15",
  f16 = "16", f17 = "17", f18 = "18", f19 = "19", f20 = "20",
  /** Ático */
  penthouse = "penthouse"
}
export namespace FloorCode {
  export const tryParse = (code: string): FloorCode | undefined =>
    (Object.values(FloorCode).filter(value => value === code)[0] ?? undefined) as FloorCode | undefined;
}
export enum EnergyCertificateCode {
  available = "available", inProcess = "inProcess", exempt = "exempt"
}
export namespace EnergyCertificateCode {
  export const tryParse = (code: string): EnergyCertificateCode | undefined => (<any>EnergyCertificateCode)[code];
}
export enum ConsumptionLevelCode {
  A = "A", B = "B", C = "C", D = "D", E = "E", F = "F", G = "G"
}
export namespace ConsumptionLevelCode {
  export const tryParse = (code: string) => (<any>ConsumptionLevelCode)[code] as ConsumptionLevelCode | undefined;
}
export enum EmissionLevelCode {
  A = "A", B = "B", C = "C", D = "D", E = "E", F = "F", G = "G"
}
export namespace EmissionLevelCode {
  export const tryParse = (code: string) => (<any>EmissionLevelCode)[code] as EmissionLevelCode | undefined;
}
export declare type PropertyAttributesDTO = {
  totalSurfaceM2?: number,
  usefulSurfaceM2?: number | null,
  solarSurfaceM2?: number | null,
  constructionYear?: number | null,
  statusCode?: PropertyStatusCode | null,
  conservationStatusCode?: ConservationStatusCode | null,
  individualBedroomsCount?: number | null,
  doubleBedroomsCount?: number | null,
  suiteBedroomsCount?: number | null,
  totalBedroomsCount?: number | null,// Internally, this is a computed value
  bathroomsCount?: number | null,
  bathroomsNotes?: string | null,
  toiletsCount?: number | null,
  toiletsNotes?: string | null,
  /** ¿Lavadero? */
  buddleHas?: boolean | null,
  /** ¿Cocina? */
  kitchenHas?: boolean | null,
  /** ¿Comedor? */
  dinningRoomHas?: boolean | null,
  dinningRoomNotes?: string | null,
  /** ¿Trastero? */
  storageRoomHas?: boolean | null,
  balconyHas?: boolean | null,
  balconyNotes?: string | null,
  terraceHas?: boolean | null,
  buildInCabinetsCount?: number | null,
  doubleGlassesHas?: boolean | null,
  externalJoineryCode?: ExternalJoineryCode | null,
  externalJoineryNotes?: string | null,
  groundCodes?: GroundCode[] | null,
  waterSupplyHas?: boolean | null,
  waterSupplyNotes?: string | null,
  powerSupplyHas?: boolean | null,
  powerSupplyNotes?: string | null,
  gasSupplyHas?: boolean | null,
  airConditioningCode?: AirConditioningCode | null,
  heatingCode?: HeatingCode | null,
  fireplaceHas?: boolean | null,
  intercomHas?: boolean | null,
  intercomNotes?: string | null,
  reinforcedDoorHas?: boolean | null,
  reinforcedDoorNotes?: string | null,
  alarmSystemHas?: boolean | null,
  /** ¿ Tiene ascensor ? */
  elevatorHas?: boolean | null,
  handicappedAccessibleIs?: boolean | null,
  furnishedIs?: boolean | null,
  gardenCode?: NoneOwnCommunity | null,
  outsideAreaCode?: NoneOwnCommunity | null,
  outsideAreaNotes?: string | null,
  swimmingPoolCode?: NoneOwnCommunity | null,
  /** # Plazas de parking */
  parkingPlacesCount?: number | null,
  optionalParkingIs?: boolean | null,
  facadeCodes?: FacadeCode[] | null,
  orientationCodes?: OrientationCode[] | null,
  sunnyIs?: boolean | null,
  sunnyNotes?: string | null,
  communityFeesAmount?: number | null,
  neighborsPerFloorCount?: number | null,
  buildingFloorsCount?: number | null,
  floorCode?: FloorCode | null,
  energyCertificateCode?: EnergyCertificateCode | null,
  consumptionLevelCode?: ConsumptionLevelCode | null,
  emissionLevelCode?: EmissionLevelCode | null
}


