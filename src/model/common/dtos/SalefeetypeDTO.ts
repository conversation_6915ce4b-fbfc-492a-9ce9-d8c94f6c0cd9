import { MultilingualStrDTO } from "./MultilingualStrDTO";
export enum SalefeetypeCode {
  // Noticia inmobiliaria
  percent = "percent",
  // Localizando inmueble/propietario/...
  fixed = "fixed",
}

export namespace SalefeetypesCode {
  export function toString(code: SalefeetypeCode): string {
    return SalefeetypeCode[code];
  }


  export function parse(code: string): SalefeetypeCode {
    const enumValue = (<any>SalefeetypesCode)[code];
    if (enumValue === undefined) {
      throw new Error(`"${code}" can't be parsed to SalefeetypesCode`);
    } else {
      return enumValue as SalefeetypeCode;
    }

  }
}

export declare type SalefeetypeDTO = {
  code?: SalefeetypeCode,
  label?: MultilingualStrDTO
}