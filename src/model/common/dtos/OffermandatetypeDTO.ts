import { MultilingualStrDTO } from "./MultilingualStrDTO";
export enum OffermandatetypeCode {
  // Noticia inmobiliaria
  exclusive = "exclusive",
  // Localizando inmueble/propietario/...
  open = "open",
  // Captando la operación (intentando obtener un mandato)
  verbal = "verbal",
  // Comercializando la oferta
  other = "other",
  // En histórico
  historic = "historic"

}

export namespace OffermandatetypeCode {
  export function toString(code: OffermandatetypeCode): string {
    return OffermandatetypeCode[code];
  }


  export function parse(code: string): OffermandatetypeCode {
    const enumValue = (<any>OffermandatetypeCode)[code];
    if (enumValue === undefined) {
      throw new Error(`"${code}" can't be parsed to OffermandatetypeCode`);
    } else {
      return enumValue as OffermandatetypeCode;
    }
  }
}

export declare type OffermandatetypeDTO = {
  code?: OffermandatetypeCode,
  label?: MultilingualStrDTO
}