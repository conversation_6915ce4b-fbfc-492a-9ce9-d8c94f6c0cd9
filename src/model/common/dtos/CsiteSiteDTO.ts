import { ContactDTO, ContactPKDTO } from "./ContactDTO";
import { CsiteOpportunityDTO } from "./CsiteOpportunityDTO";

export declare type CsiteSiteDTO = {
  /** Identificador único del site de oportunidades.  Codificado como string base64uri de 32 caracteres */
  id?: string,
  /** Contacto al que se refiere el site */
  contact?: ContactDTO,
  lastopportunity?: CsiteOpportunityDTO|null
};

export declare type CsiteSitePkDto = {
  slug:string
}

