import { ContactDTO } from "./ContactDTO";
import { OfferDTO } from "./OfferDTO";

export declare type CsiteOpportunityReactionDTO = {
  /** true=Le gusta, false=No le gusta */
  likedit: boolean,
  /** Instante en el que ha reaccionado */
  date?: Date
}

export declare type CsiteOpportunityDTO = {
  id?: string,
  contact?: ContactDTO,
  offer?: OfferDTO,
  /** ¿El contacto ha reaccionado votando positiva o negativamente a la oportunidad */
  reaction?: null | CsiteOpportunityReactionDTO,
  created?: { at: Date, ix: string },
};

export declare type CsiteOpportunityPkDto = {
  id: string
}

