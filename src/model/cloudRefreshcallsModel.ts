import { Db<PERSON>rudUtils, SqlHelpers, SqlWhereHelpers } from 'agentor-lib';
import { bool } from 'aws-sdk/clients/signer';
import { withDbCli, withDbTrx } from 'lib/AppContextUtils';
import { PoolClient } from 'pg';
import { AscOrDesc } from './common/AscOrDesc';
import { CloudRefreshcallDTO, CloudRefreshcallStatus } from './common/dtos/CloudRefreshcallDTO';
import { rowToCloudRefreshcall } from './common/rows2obj';
import { cloudRefreshcallsFields, cloudRefreshcallsTable } from './common/schema/cloudRefreshcallsTable';
import { dbSchema } from './common/schema/dbSchema';

const
  { addEq, addGtEq, addIsNotNull, addLtEq } = SqlWhereHelpers,
  { insertRecord, updateRecord } = DbCrudUtils,
  { buildSqlFields, buildSqlFrom } = SqlHelpers;

const c_root_alias = "refreshcalls";

export namespace cloudRefreshcallsModel {
  export declare type Filter = {
    key?: string,
    externaloffer_provider_id?: string,
    externaloffer_id?: string,
    //localoffer_id?: string,
    request_when_min?: Date,
    request_when_max?: Date,
    /** La llamada ya tiene respuesta? true -> exigir que sí, false -> exigir que no, undefined -> no importa */
    hasResponse?: bool,
    response_when_min?: Date,
    response_when_max?: Date,
    response_status?: CloudRefreshcallStatus,
    pagination?: {
      offset?: number,
      limit?: number,
    },
    orderBy?: {
      request_when?: AscOrDesc,
    }
  };
  export const
    list = (filter: Filter) =>
      withDbCli(dbCli =>
        list_imp(dbCli, filter)
      ),
    create = (dto: CloudRefreshcallDTO) =>
      withDbTrx(dbTran =>
        create_imp(dbTran, dto)
      ),
    update = (dto: CloudRefreshcallDTO) =>
      withDbTrx(dbTran =>
        update_imp(dbTran, dto)
      );

  async function create_imp(dbTran: PoolClient, dto: CloudRefreshcallDTO) {
    const { key } = await insertRecord(cloudRefreshcallsTable, dto)(dbTran);
    const [newRefreshcall] = await list_imp(dbTran, { key });
    return newRefreshcall;
  }
  async function update_imp(dbTran: PoolClient, dto: CloudRefreshcallDTO) {
    return updateRecord(cloudRefreshcallsTable, dto)(dbTran);
  }
  async function list_imp(dbClient: PoolClient, filter: Filter) {
    const { terms, params, offset, limit, orderBy } = buildCondition(filter);
    const allowedPaths = [
      `${c_root_alias}`,
      `${c_root_alias}_externaloffer`,
    ];
    const
      qryFields = buildSqlFields(dbSchema, { table: cloudRefreshcallsTable, tableAlias: c_root_alias, allowedPaths }).join(', '),
      qryFrom = buildSqlFrom(dbSchema, { table: cloudRefreshcallsTable, tableAlias: c_root_alias, allowedPaths, forceLeftJoin: false });
    const qry = `
      select ${qryFields}  
      from ${qryFrom}
      ${terms.length !== 0 ? `where ${terms.join(" and ")}` : ""}
      order by ${orderBy.join(", ")}
      offset ${offset} limit ${limit}
    `;
    return dbClient.query(qry, params).then(result =>
      result.rows.map(row => rowToCloudRefreshcall(row, c_root_alias))
    );
  }

  function buildCondition(filter: Filter) {
    const fldN = cloudRefreshcallsFields;

    const offset = filter.pagination?.offset ?? 0;
    const limit = filter.pagination?.limit ?? 30;
    let terms: string[] = [];
    let params: any[] = [];
    let orderBy: string[] = [];

    addEq(terms, params, `${c_root_alias}.${fldN.key}`, filter.key);
    addEq(terms, params, `${c_root_alias}.${fldN.externaloffer_provider_id}`, filter.externaloffer_provider_id);
    addEq(terms, params, `${c_root_alias}.${fldN.externaloffer_id}`, filter.externaloffer_id);
    addGtEq(terms, params, `${c_root_alias}.${fldN.request_when}`, filter.request_when_min);
    addLtEq(terms, params, `${c_root_alias}.${fldN.request_when}`, filter.request_when_max);
    addIsNotNull(terms, `${c_root_alias}.${fldN.response_when}`, filter.hasResponse);
    addGtEq(terms, params, `${c_root_alias}.${fldN.response_when}`, filter.response_when_min);
    addLtEq(terms, params, `${c_root_alias}.${fldN.response_when}`, filter.response_when_max);
    addEq(terms, params, `${c_root_alias}.${fldN.response_status}`, filter.response_status);

    if (filter.orderBy === void 0 || filter.orderBy?.request_when === AscOrDesc.desc) {
      orderBy.push(`${c_root_alias}.${fldN.request_when} desc`);
      orderBy.push(`${c_root_alias}.${fldN.key} desc`);
    } else if (filter.orderBy?.request_when === AscOrDesc.asc) {
      orderBy.push(`${c_root_alias}.${fldN.request_when} asc`);
      orderBy.push(`${c_root_alias}.${fldN.key} asc`);
    }
    return {
      terms,
      params,
      offset,
      limit,
      orderBy
    };

  }
}