import { withDb<PERSON>li, withDbTrx } from "lib/AppContextUtils";
import { rowToCsiteOpportunity, rowToCsiteOpportunityPK } from "./common/rows2obj";

import { DbCrudUtils, ErrUtils, SqlHelpers, SqlWhereHelpers, throwUndefinedError } from "agentor-lib";
import { PoolClient } from 'pg';
import { dbSchema } from './common/schema/dbSchema';
import { AscOrDesc } from "./common/AscOrDesc";
import { csiteOfferVectorsFields, csiteOfferVectorsTable } from "./common/schema/csiteOfferVectorsTable";
import { offersFields, offersTable } from "./common/schema/offersTable";
import { csiteOpportunitiesFields, csiteOpportunitiesTable } from "./common/schema/csiteOpportunitiesTable";
import { CsiteOpportunityDTO as CsiteOpportunityDTO, CsiteOpportunityPkDto } from "./common/dtos/CsiteOpportunityDTO";
import { propertiesFields, propertiesTable } from "./common/schema/propertiesTable";
import { csiteSitesFields, csiteSitesTable } from "./common/schema/csiteSitesTable";
import { DetailLevel } from "./common/DetailLevel";
import assert from "assert";
import { propertymediasQueries } from "./common/propertymediasQueries";
import { PropertymediaDTO } from "./common/dtos/PropertymediaDTO";
const
  { addEq, addIsFkOf, addGt, addLt } = SqlWhereHelpers,
  { buildSqlFields, buildSqlFrom } = SqlHelpers,
  { insertRecord, updateRecord } = DbCrudUtils,
  { doThrowError } = ErrUtils;
const c_root_alias = "opportunity";
const c_offer_alias = `${c_root_alias}_offer`;

export namespace csiteOpportunitiesModel {
  export declare type ListOptions = {
    id?: string,
    /** Orden de creación mayor que */
    created_ix_gt?: string,
    /** Orden de creación menor que */
    created_ix_lt?: string,
    /** Contacto al que pertenece la oportunidad */
    contact_id?: string,
    /** Oportunidades cuyo contacto tenga un site con este slug  */
    contact_site_slug?: string,
    /** Oferta a la que se refiere la oportunidad */
    offer_id?: string,
    offer_agent_id?: string,
    /** El contacto reaccionó con un voto positivo o negativo */
    reaction_likedit?: boolean,
    reaction_date_gt?: Date,
    reaction_date_lt?: Date,
    includePropertymedias?: boolean,
    matches_vector?: {
      /** Vectores cuya zona esté contenida en ... */
      containerzone_id?: string,
      /** Vectores cuya zona contenga a ... */
      containedzone_id?: string,
      m2: number,
      beedroms: number,
      price: number,
      /**
       * Distancia máxima permitida expresada como logaritmo neperiano.  En su ausencia se asume 0.94
       */
      maxdistanceLn?: number,
    }
    details?: {
      contact?: DetailLevel,
      offer?: DetailLevel
    },
    orderby?: {
      reaction_date?: AscOrDesc,
      created_ix?: AscOrDesc,
      created_at?: AscOrDesc
    },

    pagination?: {
      /**
       * Número máximo de tokens a obtener.  Por defecto 100
       */
      limit?: number,
      /**
       * Registro inicial en el resultado (contando desde 0).  Por defecto 0
       */
      offset?: number,
    }
  }

  export const
    list = (options: ListOptions) => withDbCli(list_imp(options)),
    exists = (options: ListOptions) => withDbCli(exists_imp(options)),
    create = (dto: CsiteOpportunityDTO) => withDbTrx(create_imp(dto)),
    createIfNotExists = (dto: CsiteOpportunityDTO) => withDbTrx(createIfNotExists_imp(dto)),
    update = (dto: CsiteOpportunityDTO) => withDbTrx(update_imp(dto));

  // #region Implementation

  function create_imp(opportunity: CsiteOpportunityDTO): (trx: PoolClient) => Promise<CsiteOpportunityPkDto> {
    return async (trx: PoolClient) => {
      return insertRecord(csiteOpportunitiesTable, opportunity)(trx).then(row => rowToCsiteOpportunityPK(row, ""));
    }
  }

  function createIfNotExists_imp(opportunity: CsiteOpportunityDTO): (trx: PoolClient) => Promise<CsiteOpportunityPkDto> {
    return async (trx: PoolClient) => {
      const
        contact_id = opportunity.contact?.id ?? throwUndefinedError("opportunity.contact.id is required"),
        offer_id = opportunity.offer?.id ?? throwUndefinedError("opportunity.offer.id is required");
      const [existing] = await list_imp({ contact_id, offer_id })(trx);
      if (!existing)
        return insertRecord(csiteOpportunitiesTable, opportunity)(trx).then(row =>
          rowToCsiteOpportunityPK(row, "")
        );
      else
        return rowToCsiteOpportunityPK({ id: existing.id! }, "");

    }
  }

  function update_imp(opportunity: CsiteOpportunityDTO): (trx: PoolClient) => Promise<number> {
    return async (trx: PoolClient) => updateRecord(csiteOpportunitiesTable, opportunity)(trx);
  }

  function exists_imp(options: ListOptions): (dbClient: PoolClient) => Promise<CsiteOpportunityDTO[]> {
    const
      { terms, params } = buildCondition(options);


    const
      sqlOptions = {
        table: csiteOpportunitiesTable,
        tableAlias: c_root_alias,

      };
    const
      qry = `
        select exists (select * 
          from ${buildSqlFrom(dbSchema, { ...sqlOptions, allowedPaths: [c_root_alias, c_offer_alias] })}
          where ${terms.join(" and ")} 
        ) as exists`;

    return dbClient => dbClient.query(qry, params).
      then(result => result.rows[0]["exists"]);
  }

  function list_imp(options: ListOptions): (dbClient: PoolClient) => Promise<CsiteOpportunityDTO[]> {
    const
      { terms, params, limit, offset, orderby } = buildCondition(options);
    const selectPaths = [
      `${c_root_alias}`,
      ...options.details?.contact ?? false ? [
        `${c_root_alias}_contact`
      ] : [],
      ...options.details?.offer !== void 0 ? [
        `${c_offer_alias}`,
        `${c_offer_alias}_customer`,
        `${c_offer_alias}_currency`,
        `${c_offer_alias}_property`,
        `${c_offer_alias}_property_type`,
        `${c_offer_alias}_property_subtype`,
        `${c_offer_alias}_property_zone`,
        `${c_offer_alias}_property_zone_parent`,
        `${c_offer_alias}_property_address`,
        `${c_offer_alias}_property_address_city`,
        `${c_offer_alias}_property_address_city_province`,
        `${c_offer_alias}_property_address_city_province_country`,
        `${c_offer_alias}_property_favouritePicture`,
      ] : [],
    ],
      fromPaths = [
        ...selectPaths,
        // Tenemos condiciones sobre oferta.  Dado que es "left join", si no se incluyen las condiciones no debería
        // haber diferencia con no hacer la join
        `${c_offer_alias}`
      ];
    const
      qryFields = buildSqlFields(dbSchema, { table: csiteOpportunitiesTable, tableAlias: c_root_alias, allowedPaths: selectPaths }).join(", "),
      qryFrom = buildSqlFrom(dbSchema, { table: csiteOpportunitiesTable, tableAlias: c_root_alias, allowedPaths: fromPaths, forceLeftJoin: true });
    const
      qry = `
        select ${qryFields} 
        from ${qryFrom}
        where ${terms.join(" and ")} 
        ${orderby.length === 0 ? "" : `order by ${orderby.join(", ")}`}
        offset ${offset} limit ${limit}
      `;

    // Si nos piden datos públicos de la oferta... tenemos que engañar a rowToOffer indicando que somos un id de agente inexistente.
    // Esto debería cambiarse:  por cada acción/oferta/oportunidad debería indicarse el nivel de acceso: "private", "public"
    const nooneAgentId = options.details?.offer === DetailLevel.public ? "NOONEID" : undefined;

    return async dbClient => {
      const opportunities = await dbClient.query(qry, params).then(({ rows }) =>
        rows.map(r => rowToCsiteOpportunity(r, c_root_alias, nooneAgentId))
      );
      if (options.includePropertymedias)
        return completeWithPropertymedias(dbClient, opportunities);
      else
        return opportunities
    }
  }

  function buildCondition(options: ListOptions) {

    const { pagination, id, offer_id, offer_agent_id, contact_id, contact_site_slug: csiteSite_slug, reaction_likedit, reaction_date_gt, reaction_date_lt, matches_vector, created_ix_gt, created_ix_lt } = options;
    const orderby = [];
    const offset = pagination?.offset ?? 0;
    const limit = pagination?.limit ?? 30;

    let terms: [string, ...string[]] = ["true"];
    let params: any[] = [];


    addEq(terms, params, `${c_offer_alias}.${offersFields.agent_id}`, offer_agent_id)
    addEq(terms, params, `${c_root_alias}.${csiteOpportunitiesFields.id}`, id)
    addEq(terms, params, `${c_root_alias}.${csiteOpportunitiesFields.offer_id}`, offer_id);
    addEq(terms, params, `${c_root_alias}.${csiteOpportunitiesFields.contact_id}`, contact_id);
    addEq(terms, params, `${c_root_alias}.${csiteOpportunitiesFields.reaction_likedit}`, reaction_likedit);
    addGt(terms, params, `${c_root_alias}.${csiteOpportunitiesFields.created_ix}`, created_ix_gt);
    addLt(terms, params, `${c_root_alias}.${csiteOpportunitiesFields.created_ix}`, created_ix_lt);
    addGt(terms, params, `${c_root_alias}.${csiteOpportunitiesFields.reaction_date}`, reaction_date_gt);
    addLt(terms, params, `${c_root_alias}.${csiteOpportunitiesFields.reaction_date}`, reaction_date_lt);

    if (csiteSite_slug !== void 0) {
      params.push(csiteSite_slug)
      terms.push(`${c_root_alias}.${csiteOpportunitiesFields.contact_id} in (select cs.${csiteSitesFields.contact_id} from ${csiteSitesTable.name} mcs where mcs.${csiteSitesFields.slug}=$${params.length})`);
    }
    if (matches_vector) {
      const c_vector_alias = "vector";
      const c_v_offer_alias = "vector_offer";
      const c_v_o_property_alias = "vector_offer_property";

      let subterms: string[] = [];

      // Vector asociado a la oferta de la oportunidad
      subterms.push(`${c_vector_alias}.${csiteOfferVectorsFields.offer_id}=${c_root_alias}.${csiteOpportunitiesFields.offer_id}`)

      addIsFkOf(subterms, params, `${c_v_o_property_alias}.zone_id`, "propertyzones_containers", "propertyzones_containers.contained_id", "propertyzones_containers.container_id", matches_vector.containerzone_id);
      addIsFkOf(subterms, params, `${c_v_o_property_alias}.zone_id`, "propertyzones_containers", "propertyzones_containers.container_id", "propertyzones_containers.contained_id", matches_vector.containedzone_id);
      //addEq(terms, params, `${c_root_alias}.${matcherOfferVectorsFields.zone_id}`, liked.containerzone_id);
      params.push(Math.log(Math.max(matches_vector.beedroms ?? 1, 1)));
      const bedroomsNPar = params.length;
      params.push(Math.log(Math.max(matches_vector.m2 ?? 1, 1)));
      const m2NPar = params.length;
      params.push(Math.log(Math.max(matches_vector.price ?? 1, 1)));
      const priceNPar = params.length;
      //params.push(Math.pow(Math.max(0, matches_vector.maxdistanceLn ?? 0.94), 2))
      //const maxdistanceNPar = params.length;

      //subterms.push(`
      //  ( pow("m2Ln"-$${m2NPar} ,2) + pow($${bedroomsNPar}-"bedroomsLn",2) + pow(2*($${priceNPar}-"priceLn"),2) <= $${maxdistanceNPar} )
      //`);
      subterms.push(`
        ( "m2Ln"-$${m2NPar} between -0.32 and 0.28 ) and ("bedroomsLn"-$${bedroomsNPar}<0.28) and ($${priceNPar}-"priceLn" between -0.32 and 0.18 )
      `);

      terms.push(`exists ( select * 
        from 
          ${csiteOfferVectorsTable.name} ${c_vector_alias}
          inner join ${offersTable.name} ${c_v_offer_alias} on ${c_vector_alias}.${csiteOfferVectorsFields.offer_id}=${c_v_offer_alias}.${offersFields.id}
          inner join ${propertiesTable.name} ${c_v_o_property_alias} on ${c_v_offer_alias}.${offersFields.property_id}=${c_v_o_property_alias}.${propertiesFields.id}
        where
          ${subterms.join(" and ")}
        )
      `);
    }

    const orderBy = options.orderby ?? { created_ix: AscOrDesc.desc };

    if (orderBy.reaction_date) {
      orderby.push(`${c_root_alias}.${csiteOpportunitiesFields.reaction_date} ${orderBy.reaction_date === AscOrDesc.desc ? "desc" : "asc"} `);
      orderby.push(`${c_root_alias}.${csiteOpportunitiesFields.created_at} ${orderBy.reaction_date === AscOrDesc.desc ? "desc" : "asc"} `);
    } else if (orderBy.created_at) {
      orderby.push(`${c_root_alias}.${csiteOpportunitiesFields.created_at} ${orderBy.created_at === AscOrDesc.desc ? "desc" : "asc"} `);
      // Usamos dato adicional para asegurar un orden
    } else if (orderBy.created_ix) {
      orderby.push(`${c_root_alias}.${csiteOpportunitiesFields.created_ix} ${orderBy.created_ix === AscOrDesc.desc ? "desc" : "asc"} `);
      //
    }


    return {
      terms,
      params,
      offset,
      limit,
      orderby
    };

  }
  // #endregion

  /**
   * Completa los datos del inmueble de la oferta de cada oportunidad con sus propertymedias.
   * @param dbClient Conexión BBDD a usar para obtener los propertymedias
   * @param offers Ofertas que se desean completar
   * @returns La MISMA lista de ofertas modificada
   */

  async function completeWithPropertymedias(dbClient: PoolClient, opportunities: CsiteOpportunityDTO[]) {
    assert(-1 === opportunities.findIndex(o => o?.offer?.id === void 0), "Identificador de las ofertas es requerido");
    assert(-1 === opportunities.findIndex(o => o?.offer?.property?.id === void 0), "Identificador de los inmuebles es requerido");


    const propertymediasByPropertyId = groupByPropertyId(
      await propertymediasQueries.list({
        offer_ids: opportunities.map(o => o.offer?.id ?? doThrowError("Unexpected: Offer without id"))
      })(dbClient)
    );
    for (let o of opportunities) if (o.offer?.property?.id)
      o.offer.property.propertymedias = propertymediasByPropertyId.get(o.offer.property.id);
    return opportunities;

    function groupByPropertyId(propertymedias: PropertymediaDTO[]): Map<string, PropertymediaDTO[]> {
      assert(-1 === propertymedias.findIndex(pm => pm.property?.id === void 0), "Falta id del inmueble");

      return propertymedias.reduce(
        (byPropertyId, propertymedia) => {
          let propertyId = propertymedia.property!.id!;
          if (!byPropertyId.has(propertyId))
            byPropertyId.set(propertyId, []);
          byPropertyId.get(propertyId)!.push(propertymedia);
          return byPropertyId;
        },
        new Map<string, PropertymediaDTO[]>()
      );
    }
  }
}
