import { withDbCli, withDbTrx } from "lib/AppContextUtils";
import { rowToCsiteEnabledagent } from "./common/rows2obj";

import { DbCrudUtils, SqlHelpers, SqlWhereHelpers } from "agentor-lib";
import { PoolClient } from 'pg';
import { dbSchema } from './common/schema/dbSchema';
import { AscOrDesc } from "./common/AscOrDesc";
import { csiteOfferChangesTable } from "./common/schema/csiteOfferChangesTable";
import { csiteEnabledagentsFields, csiteEnabledagentsTable } from "./common/schema/csiteEnabledagentsTable";
import { CsiteEnabledagentDTO } from "./common/dtos/CsiteEnabledagentDTO";
const
  { addGt } = SqlWhereHelpers,
  { buildSqlFields, buildSqlFrom } = SqlHelpers,
  { removeRecord } = DbCrudUtils;

const c_root_alias = "enabledagents";

export namespace csiteEnabledagentsModel {
  export declare type ListOptions = {
    /** Oferta del stream */
    agent_id?: string,

    orderby?: {
      agent_id?: AscOrDesc
    },

    pagination?: {
      /**
       * Número máximo de tokens a obtener.  Por defecto 100
       */
      limit: number,
      /**
       * Registro inicial en el resultado (contando desde 0).  Por defecto 0
       */
      offset: number,
    }
  }

  export const list = (options: ListOptions) => withDbCli(
    list_imp(options)
  );

  export const exists = (options: ListOptions) => withDbCli(
    exists_imp(options)
  );

  export const remove = (id: string) => withDbTrx(
    removeRecord(csiteOfferChangesTable, { id })
  );

  // #region Implementation

  function list_imp(options: ListOptions): (dbClient: PoolClient) => Promise<CsiteEnabledagentDTO[]> {
    const
      { terms, params, orderby, limit, offset } = buildCondition(options);
    const
      sqlOptions = {
        table: csiteEnabledagentsTable,
        tableAlias: c_root_alias
      };
    const
      qry = `
        select ${buildSqlFields(dbSchema, sqlOptions).join(', ')} 
        from ${buildSqlFrom(dbSchema, { ...sqlOptions, allowedPaths: [c_root_alias] })}
        where ${terms.join(" and ")} 
        ${orderby.length === 0 ? "" :
          `order by ${orderby.join(", ")}`
        }
        offset ${offset} limit ${limit}
      `;

    return dbClient => dbClient.
      query(qry, params).
      then(({ rows }) =>
        rows.map(r =>
          rowToCsiteEnabledagent(r, c_root_alias)
        )
      );
  }


  function exists_imp(options: ListOptions): (dbClient: PoolClient) => Promise<boolean> {
    const
      { terms, params } = buildCondition(options);
    const
      sqlOptions = {
        table: csiteEnabledagentsTable,
        tableAlias: c_root_alias
      };
    const
      qry = `
        select exists (
          select * 
          from ${buildSqlFrom(dbSchema, { ...sqlOptions, allowedPaths: [c_root_alias] })}
          where ${terms.join(" and ")}
        ) as exists         
      `;

    return dbClient => dbClient.
      query(qry, params).
      then(({ rows }) => rows[0]["exists"]);
  }

  function buildCondition(options: ListOptions) {

    const { pagination, agent_id, orderby } = options;
    const orderBy = [];
    const offset = pagination?.offset ?? 0;
    const limit = pagination?.limit ?? 30;

    let terms: [string, ...string[]] = ["true"];
    let params: any[] = [];
    addGt(terms, params, `${c_root_alias}.${csiteEnabledagentsFields.agent_id}`, agent_id);


    if (orderby?.agent_id !== void 0)
      orderBy.push(`${c_root_alias}.${csiteEnabledagentsFields.agent_id} ${orderby.agent_id === AscOrDesc.desc ? "desc" : "asc"}`);
    else
      orderBy.push(`${c_root_alias}.${csiteEnabledagentsFields.agent_id} asc`);


    return {
      terms,
      params,
      offset,
      limit,
      orderby: orderBy
    };

  }
  // #endregion
}