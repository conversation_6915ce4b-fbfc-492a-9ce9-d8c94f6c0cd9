import { withDbCli, withDbTrx } from "lib/AppContextUtils";
import { ScrtyUserDTO } from "./common/dtos/ScrtyUserDTO";
import { rowToScrtyUser } from "./common/rows2obj";
import { scrtyUsersFields, scrtyUsersTable } from "./common/schema/scrtyUsersTable";

import { DbCrudUtils, SqlHelpers, SqlWhereHelpers } from "agentor-lib";
import { dbSchema } from './common/schema/dbSchema';
import { scrtyCredentialsFields, scrtyCredentialsTable } from "./common/schema/scrtyCredentialsTable";
import { PoolClient } from "pg";
import { userInfo } from "os";
const
  { addEq } = SqlWhereHelpers,
  { insertRecord } = DbCrudUtils,
  { buildSqlFields, buildSqlFrom } = SqlHelpers;

const c_root_alias = 'usr';
export namespace scrtyUserModel {
  export declare type scrtyUserId = string;
  export declare type ListOptions = {
    id?: scrtyUserId,
    agent_id?: string,
    /** Filtrar usuarios según sus credenciales.  Usado para validar en el proceso de login */
    credentials?: {
      username: string,
      password?: string
    },
    pagination?: {
      /**
       * Número máximo de tokens a obtener.  Por defecto 100
       */
      limit: number,
      /**
       * Registro inicial en el resultado (contando desde 0).  Por defecto 0
       */
      offset: number,
    }
  }

  export const
    list = (options: ListOptions) => withDbCli(dbCli => list_imp(dbCli, options)),
    create = (data: ScrtyUserDTO) => withDbTrx(dbTran => create_imp(dbTran, data)),
    read = (id: string) => withDbCli(dbCli => read_imp(dbCli, id)),
    /**
     *  Obtener el identificador del agente asociado al usuario
     */
    getAgentId = (id:string)=> withDbCli(dbCli => 
      list_imp(dbCli,{id}).then( usrs=>usrs.map(usr=>usr.agent!.id!)[0] as string|undefined)
    )
    ;

  // #region Implementation
  function create_imp(dbTran: PoolClient, dto: ScrtyUserDTO) {
    return insertRecord(scrtyUsersTable, dto)(dbTran).then(({ id }) => id as string);
  }
  function read_imp(dbCli: PoolClient, id: string): Promise<ScrtyUserDTO | undefined> {
    return list_imp(dbCli, { id }).then(([user]) => user);
  }
  function list_imp(dbCli: PoolClient, options: ListOptions) {
    const
      { terms, params, limit, offset } = buildCondition(options);
    const
      sqlOptions = {
        table: scrtyUsersTable,
        tableAlias: c_root_alias,
      };
    const
      qry = `
        select ${buildSqlFields(dbSchema, sqlOptions).join(', ')} 
        from ${buildSqlFrom(dbSchema, sqlOptions)}
        where ${terms.join(" and ")}
        offset ${offset} limit ${limit}
      `;
    return dbCli.
      query(qry, params).
      then(
        result => result.rows.map(r => rowToScrtyUser(r, c_root_alias))
      );

  }
  function buildCondition(options: ListOptions) {
    const { id, agent_id, pagination, credentials } = options;
    const offset = pagination?.offset ?? 0;
    const limit = pagination?.limit ?? 30;

    let terms: [string, ...string[]] = ["true"];
    let params: any[] = [];

    addEq(terms, params, `${c_root_alias}.id`, id);
    addEq(terms, params, `${c_root_alias}.${scrtyUsersFields.agent_id}`, agent_id);

    if (credentials !== void 0) {
      let subTerms: string[] = [];
      addEq(subTerms, params, `"${scrtyCredentialsFields.username}"`, credentials.username);
      addEq(subTerms, params, `"${scrtyCredentialsFields.password}"`, credentials.password);
      let subSql = `select "${scrtyCredentialsFields.user_id}" from "${scrtyCredentialsTable.name}" where ${subTerms.join(" and ")}`;
      terms.push(`${c_root_alias}."${scrtyUsersFields.id}" in (${subSql})`);
    }

    return {
      terms,
      params,
      offset,
      limit
    };

  }
  // #endregion
}