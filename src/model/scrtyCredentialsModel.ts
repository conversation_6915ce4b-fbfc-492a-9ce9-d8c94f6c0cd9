import { withDbCli, withDbTrx } from "lib/AppContextUtils";
import { ScrtyUserDTO } from "./common/dtos/ScrtyUserDTO";
import { rowToScrtyCredentials, rowToScrtyUser } from "./common/rows2obj";
import { scrtyUsersFields, scrtyUsersTable } from "./common/schema/scrtyUsersTable";

import { DbCrudUtils, SqlHelpers, SqlWhereHelpers } from "agentor-lib";
import { dbSchema } from './common/schema/dbSchema';
import { scrtyCredentialsFields, scrtyCredentialsTable } from "./common/schema/scrtyCredentialsTable";
import { ScrtyCredentialsDTO } from "./common/dtos/ScrtyCredentialsDTO";
const
  { addEq } = SqlWhereHelpers,
  { insertRecord, updateRecord } = DbCrudUtils,
  { buildSqlFields, buildSqlFrom } = SqlHelpers;

const c_alias = 'creds';
export namespace scrtyCredentialsModel {

  export declare type ListOptions = {
    user_id?: string,
    username?: string,
    password?: string,
    pagination?: {
      /**
       * Número máximo de tokens a obtener.  Por defecto 100
       */
      limit: number,
      /**
       * Registro inicial en el resultado (contando desde 0).  Por defecto 0
       */
      offset: number,
    }
  }

  export const
    list = (options: ListOptions) => list_imp(options),
    create = (data: ScrtyCredentialsDTO) => create_imp(data),
    update = (data: ScrtyCredentialsDTO) => update_imp(data)
    ;

  // #region Implementation
  function create_imp(dto: ScrtyCredentialsDTO) {
    return withDbTrx(dbTrx => insertRecord(scrtyCredentialsTable, dto)(dbTrx).then(({ username }) => username))
  }
  function update_imp(dto: ScrtyCredentialsDTO) {
    return withDbTrx(dbTrx => updateRecord(scrtyCredentialsTable, dto)(dbTrx).then((count) => count))
  }
  function list_imp(options: ListOptions) {
    const
      { terms, params, limit, offset } = buildCondition(options);
    const
      sqlOptions = {
        table: scrtyCredentialsTable,
        tableAlias: c_alias,
        allowedPaths: [
          c_alias,
          `${c_alias}_user`,
          `${c_alias}_user_agent`,
        ]
      };
    const
      qry = `
        select ${buildSqlFields(dbSchema, sqlOptions).join(', ')} 
        from ${buildSqlFrom(dbSchema, sqlOptions)}
        where ${terms.join(" and ")}
        offset ${offset} limit ${limit}
      `;
    return withDbCli(dbClient => dbClient.
      query(qry, params).
      then(
        result => result.rows.map(r => rowToScrtyCredentials(r, c_alias))
      )
    );
  }
  function buildCondition(options: ListOptions) {
    const { pagination, user_id, username, password } = options;
    const offset = pagination?.offset ?? 0;
    const limit = pagination?.limit ?? 30;

    let terms: [string, ...string[]] = ["true"];
    let params: any[] = [];

    addEq(terms, params, `${c_alias}.${scrtyCredentialsFields.user_id}`, user_id);
    addEq(terms, params, `${c_alias}.${scrtyCredentialsFields.username}`, username);
    addEq(terms, params, `${c_alias}.${scrtyCredentialsFields.password}`, password);

    return {
      terms,
      params,
      offset,
      limit
    };

  }
  // #endregion
}