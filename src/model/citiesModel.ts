import { StringUtils } from 'agentor-lib';
import { withDbCli } from "lib/AppContextUtils";
import * as pg from 'pg';

import { CityDTO } from "./common/dtos/CityDTO";
import { rowToCity } from './common/rows2obj';
import { citiesFields, citiesTable } from "./common/schema/citiesTable";
import { offersFields } from "./common/schema/offersTable";
import { SecurityCoditionsUtils } from "./common/securityConditionsUtils";
import { SqlHelpers, SqlWhereHelpers, DbCrudUtils } from "agentor-lib";
import { dbSchema } from './common/schema/dbSchema';

const { buildSqlFields, buildSqlFrom } = SqlHelpers;
const { addContainsLike, addEq } = SqlWhereHelpers;
const { removeDiacritics } = StringUtils;


const c_root_alias = "city";

export namespace CitiesModel {

  export declare type Filter = {
    accessorConditions?: {
      /** Identificador del agente que accede... "me" */
      id: string,
      /** Ciudades usadas por el agente en alguna oferta o demanda */
      includeUsedByMe?: boolean,
      /** Ciudades usadas por otros agentes en alguna oferta o demanda que YO (me) puedo leer.  Debe usarse con mucho cuidado */
      includeUsedByOthers?: boolean,
      /** En caso de indicar usedByOthers, identificadores de los workgroups que quedan excluidos (Ej: id del cloud) para determinar ofertas/demandas de otros agentes */
      omitWorkgroupIds?: string[]
    }
    search?: string,
    code?: string,
    province_code?: string,
    /**
     * @deprecated Usar accessorConditions
     * El agente tiene oportunidades (ofertas o demandas) en la población.
     */
    used_by_agent_id?: string,
    pagination?: {
      offset?: number,
      limit?: number
    }
  }

  export const
    list = (filter: Filter) =>
      withDbCli(listCities_imp(filter)),
    count = (filter: Filter) =>
      withDbCli(countCities_imp(filter));

  function listCities_imp(filter: Filter): (dbClient: pg.PoolClient) => Promise<CityDTO[]> {
    return async (dbClient: pg.PoolClient) => {
      const { terms, params, offset, limit } = await buildCondition(filter)(dbClient);

      const qryOptions = {
        table: citiesTable,
        tableAlias: c_root_alias,
        // Foreign keys
        allowedPaths: [
          c_root_alias,
          `${c_root_alias}_province`,
          `${c_root_alias}_province_country`
        ],
      };
      const
        qryFields = buildSqlFields(dbSchema, qryOptions).join(', '),
        qryFrom = buildSqlFrom(dbSchema, { ...qryOptions, forceLeftJoin: false });

      const qry = `
      select 
        ${qryFields}
      from 
        ${qryFrom}
      where 
        ${terms.join(" and ")}
      order by 
        ${c_root_alias}."computedSortValue" asc
      offset 
        ${offset} 
      limit 
        ${limit}
    `;

      return dbClient.query(qry, params).
        then(result => result.rows.map(rw => rowToCity(rw)));
    };
  }
  function countCities_imp(filter: Filter): (dbClient: pg.PoolClient) => Promise<number> {
    return async (dbClient: pg.PoolClient) => {
      const { terms, params } = await buildCondition(filter)(dbClient);

      const qryOptions = {
        table: citiesTable,
        tableAlias: c_root_alias,
        // Foreign keys
        allowedPaths: [
          c_root_alias,
          `${c_root_alias}_province`,
          `${c_root_alias}_province_spain`
        ],
      };
      const
        qryFrom = buildSqlFrom(dbSchema, { ...qryOptions, forceLeftJoin: false });

      const qry = `
      select
        count(*) as "howMany"
      from
        ${qryFrom}
      where
        ${terms.join(" and ")}
    `
      return dbClient.query(qry, params).
        then(result => parseInt(result.rows[0]["howMany"]));
    };
  }
  function buildCondition(filter: Filter) {
    return async (dbCli: pg.PoolClient) => {
      const { accessorConditions, search, code, province_code, pagination } = filter;
      const offset = pagination?.offset ?? 0;
      const limit = pagination?.limit ?? 30;

      let terms: [string, ...string[]] = ["true"];
      let params: any[] = [];

      addEq(terms, params, `${c_root_alias}.${citiesFields.code}`, code);
      addEq(terms, params, `${c_root_alias}.${citiesFields.province_code} `, province_code);

      if (accessorConditions && accessorConditions.id.length > 0) {
        //
        // REMARKS: Para detectar poblaciones en uso nos basamos en la tabla de ofertas
        //

        let { id, includeUsedByMe, includeUsedByOthers, omitWorkgroupIds } = accessorConditions;
        let accessorTerms: string[] = [
          //`offer_property.address_city_code = ${ c_root_alias }.code`
        ];
        await SecurityCoditionsUtils.addOfferAccessorTerms(accessorTerms, params, {
          agentIdAlias: `offer.${offersFields.agent_id} `,
          offerIdAlias: `offer.${offersFields.id} `,
          accessorCondition: {
            id: id,
            ...includeUsedByMe !== void 0 ? { includeMine: includeUsedByMe } : {},
            ...includeUsedByOthers !== void 0 ? { includeNotMine: includeUsedByOthers } : {},
            ...omitWorkgroupIds !== void 0 ? { omitWorkgroupIds: omitWorkgroupIds } : {}
          }
        })(dbCli);

        let accessorSql = `
        ${c_root_alias}.code in (
          select 
            offer_property.address_city_code 
          from 
            offers offer
            inner join properties offer_property on offer.property_id = offer_property.id
          where 
            ${accessorTerms.join(" and ")}
        )
      `;
        terms.push(accessorSql);
      }
      // computedSearchString is expressend in lower case... we don't need "ilike"
      addContainsLike(terms, params, `${c_root_alias}."computedSearchString"`, search ? removeDiacritics(search.toLowerCase()) : search, false);

      return {
        terms,
        params,
        offset,
        limit
      }
    };
  }

}