
import { DbCrudUtils } from "agentor-lib";
import { withDbCli, withDbTrx } from "lib/AppContextUtils";
import { PoolClient } from "pg";
import { CloudExternalofferDTO, CloudExternalofferRefDTO } from "./common/dtos/CloudExternalofferDTO";
import { cloudExternaloffersFields, cloudExternaloffersTable } from "./common/schema/cloudExternaloffersTable";
const { insertRecord } = DbCrudUtils;
export namespace CloudExternaloffersModel {

  export const
    listPage = (cloudProviderId: string, lastReaded?: CloudExternalofferDTO, size: number = 1000) =>
      withDbCli(listExternaloffersPage_imp(cloudProviderId, lastReaded, size)),
    createExternaloffer = (cloudOffer: CloudExternalofferDTO) =>
      withDbTrx(createCloudOfferRef_imp(cloudOffer)),
    updateRevision = (cloudRef: CloudExternalofferRefDTO) =>
      withDbTrx(updateRevision_imp(cloudRef)),
    idToLocalofferId = (provider_id: string, id: string) =>
      withDbCli(idToLocalofferId_imp(provider_id, id)),
    getByLocalofferId = (offer_id: string) =>
      withDbCli(getByLocalofferId_imp(offer_id));

  function idToLocalofferId_imp(provider_id: string, id: string): (dbCli: PoolClient) => Promise<string | null> {
    return dbCli => dbCli.
      query(`
        select 
          ${cloudExternaloffersFields.localoffer_id} 
        from 
          ${cloudExternaloffersTable.name} 
        where 
          ${cloudExternaloffersFields.provider_id}=$1 and 
          ${cloudExternaloffersFields.id}=$2`,
        [
          provider_id, id
        ]).
      then(result => result.rows.length === 0
        ? null :
        result.rows[0][cloudExternaloffersFields.localoffer_id] as string
      );
  }
  function getByLocalofferId_imp(offer_id: string): (dbCli: PoolClient) => Promise<CloudExternalofferDTO | undefined> {
    const qry = `
      select 
        ${cloudExternaloffersFields.provider_id},
        ${cloudExternaloffersFields.id}, 
        ${cloudExternaloffersFields.revision},
        ${cloudExternaloffersFields.localoffer_id}
      from
        ${cloudExternaloffersTable.name}
      where
        ${cloudExternaloffersFields.localoffer_id}=$1
    `;
    return dbCli => dbCli.query(qry, [offer_id]).
      then(result => result.rows.length === 0 ? void 0 : result.rows[0]).
      then(row => row ? {
        provider: {
          id: row[cloudExternaloffersFields.provider_id] as string
        },
        id: row[cloudExternaloffersFields.id] as string,
        revision: row[cloudExternaloffersFields.revision] as string,
        localoffer: {
          id: row[cloudExternaloffersFields.localoffer_id]
        }
      } as CloudExternalofferDTO : void 0);
  }
  function createCloudOfferRef_imp(externaloffer: CloudExternalofferDTO): (dbTran: PoolClient) => Promise<void> {
    return trx => insertRecord(cloudExternaloffersTable, externaloffer)(trx).then(_ => void 0);
  }
  function updateRevision_imp(externalRef: CloudExternalofferRefDTO): (dbTran: PoolClient) => Promise<number> {
    return dbTran => dbTran.query(`
      update ${cloudExternaloffersTable.name}
      set 
        ${cloudExternaloffersFields.revision}=$3
      where
        ${cloudExternaloffersFields.provider_id}=$1 and
        ${cloudExternaloffersFields.id}=$2
    `, [
      externalRef.provider.id,
      externalRef.id,
      externalRef.revision
    ]).then(result => result.rowCount ?? 0);
  }
  function listExternaloffersPage_imp(cloudProviderId: string, lastReaded?: CloudExternalofferDTO, limit: number = 2500): (dbCli: PoolClient) => Promise<CloudExternalofferDTO[]> {
    let terms = [];
    let params: any[] = [];

    params.push(cloudProviderId);
    terms.push(`${cloudExternaloffersFields.provider_id} = $${params.length}`);

    if (lastReaded !== void 0) {
      params.push(lastReaded.id);
      terms.push(`${cloudExternaloffersFields.id}> $${params.length}`);
    }

    params.push(limit);

    const sql = `
      select 
        ${cloudExternaloffersFields.provider_id},
        ${cloudExternaloffersFields.id}, 
        ${cloudExternaloffersFields.revision},
        ${cloudExternaloffersFields.localoffer_id}
      from
        ${cloudExternaloffersTable.name}
      where 
        ${terms.join(" and ")}
      order by 
        id asc
      limit 
        $${params.length}
    `;


    return dbCli =>
      dbCli.query(sql, params).then(({ rows }) => rows.map(row => ({
        provider: { id: row[cloudExternaloffersFields.provider_id] },
        id: row[cloudExternaloffersFields.id],
        revision: row[cloudExternaloffersFields.revision],
        localoffer: { id: row[cloudExternaloffersFields.localoffer_id] }
      })));

  }
}