import { withDbCli, withDbTrx } from "lib/AppContextUtils";
import { EcomTransactionDTO } from "./common/dtos/EcomTransactionDTO";
import { rowToEcomtransaction } from "./common/rows2obj";
import { ecomTransactionsTable } from "./common/schema/ecomTransactionsTable";

import { PoolClient } from 'pg';
import { DbCrudUtils, SqlHelpers, SqlWhereHelpers } from "agentor-lib";
import { dbSchema } from './common/schema/dbSchema';
const
  { addEq, addGtEq, addLt, addLtEq, addIsNotNull } = SqlWhereHelpers,
  { insertRecord, updateRecord } = DbCrudUtils,
  { buildSqlFields, buildSqlFrom } = SqlHelpers;


const c_root_alias = "transaction";
export namespace EcomTransactionModel {

  export declare type Filter = {
    id?: string,
    pagination?: {
      offset?: number,
      limit?: number
    }
  };
  export const
    list = (filter: Filter) => withDbCli(
      list_imp(filter)
    ),
    create = (dto: EcomTransactionDTO) => withDbTrx(
      create_imp(dto)
    );

  function create_imp(dto: EcomTransactionDTO): (dbTran: PoolClient) => Promise<EcomTransactionDTO> {
    return async dbTran => {
      let { id } = await insertRecord(ecomTransactionsTable, dto)(dbTran);
      let [newTransaction] = await list_imp({ id })(dbTran);
      return newTransaction;
    }
  }
  function list_imp(filter: Filter): (dbClient: PoolClient) => Promise<EcomTransactionDTO[]> {
    const { terms, params, orderBy, offset, limit } = buildCondition(filter);

    const qryOptions = {
      table: ecomTransactionsTable,
      tableAlias: c_root_alias,
      // Foreign keys
      allowedPaths: [
        c_root_alias,
      ]
    };
    const
      qryFields = buildSqlFields(dbSchema, qryOptions).join(", "),
      qryFrom = buildSqlFrom(dbSchema, qryOptions);

    const qry = `
      select ${qryFields}  
      from ${qryFrom}
      ${terms.length !== 0 ? `where ${terms.join(" and ")}` : ""}
      ${orderBy.length !== 0 ? `order by ${orderBy.join(", ")}` : ""}
      offset ${offset} 
      limit ${limit}
    `;

    return dbClient =>
      dbClient.query(qry, params).
        then(({ rows }) =>
          rows.map(rw => rowToEcomtransaction(rw, c_root_alias))
        ).catch(e => {
          console.log("Error!!!", e);
          throw e;
        });
  }



  function buildCondition(filter: Filter) {
    const { id, pagination } = filter;
    const offset = pagination?.offset ?? 0;
    const limit = pagination?.limit ?? 30;

    let terms: string[] = [];
    let params: any[] = [];
    let orderBy: string[] = [];


    addEq(terms, params, `${c_root_alias}.id`, id);

    return { terms, params, offset, limit, orderBy };
  }
}