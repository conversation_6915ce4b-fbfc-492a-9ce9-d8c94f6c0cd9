import { StripeCheckoutsessionDTO } from './common/dtos/StripeCheckoutsessionDTO';
import { rowToStripeCheckoutsession } from './common/rows2obj';
import { stripeCheckoutsessionsFields, stripeCheckoutsessionsTable } from './common/schema/stripeCheckoutsessionsTable';

import { PoolClient } from 'pg';
import { DbCrudUtils, SqlHelpers, SqlWhereHelpers } from "agentor-lib";
import { dbSchema } from './common/schema/dbSchema';
import { withDbCli, withDbTrx } from 'lib/AppContextUtils';
const
  { addEq, addGt, addGtEq, addLt, addLtEq, addNotIn, addIsNotNull, addIn, addNotExists, addIsFkOf, addIsNotFkOf } = SqlWhereHelpers,
  { insertRecord, removeRecord, updateRecord } = DbCrudUtils,
  { buildSqlFields, buildSqlFrom } = SqlHelpers;

const c_alias = "stripecheckoutsession";

export namespace stripeCheckoutsessionsModel {

  export declare type Filter = {
    id?: string,
    agent_id?: string
    pagination?: {
      offset?: number,
      limit?: number
    }
  };

  export function list(filter: Filter): (dbClient?: PoolClient) => Promise<StripeCheckoutsessionDTO[]> {
    return withDbCli(list_imp(filter));
  }
  export function count(filter: Filter): (dbClient?: PoolClient) => Promise<number> {
    return withDbCli(countContacts_imp(filter));
  }
  export function update(dto: StripeCheckoutsessionDTO): (dbTrx?: PoolClient) => Promise<number> {
    return withDbTrx(updateRecord(stripeCheckoutsessionsTable, dto));
  }
  export function create(dto: StripeCheckoutsessionDTO): (dbTrx?: PoolClient) => Promise<StripeCheckoutsessionDTO> {
    return withDbTrx(async dbCli => {
      const { id } = await insertRecord(stripeCheckoutsessionsTable, dto)(dbCli);
      const [newContact] = await list_imp({ id })(dbCli);
      return newContact;
    });
  }

  function list_imp(filter: Filter): (dbClient: PoolClient) => Promise<StripeCheckoutsessionDTO[]> {
    const
      { terms, params, offset, limit } = buildCondition(filter);
    const
      qryFields = buildSqlFields(dbSchema, {
        table: stripeCheckoutsessionsTable,
        tableAlias: c_alias,
        allowedPaths: [c_alias]
      }).join(', '),
      qryFrom = buildSqlFrom(dbSchema, {
        table: stripeCheckoutsessionsTable,
        tableAlias: c_alias,
        allowedPaths: [c_alias]
      }),
      qry = `
        select 
          ${qryFields} 
        from 
          ${qryFrom}
        where 
          ${terms.join(" and ")}
        order by 
          ${c_alias}.${stripeCheckoutsessionsFields.created_at} asc
        offset 
          ${offset} 
        limit 
          ${limit}
      `;

    return dbClient => dbClient.
      query(qry, params).
      then(result => result.rows.map(r => rowToStripeCheckoutsession(r, c_alias)));
  }

  function countContacts_imp(filter: Filter): (dbClient: PoolClient) => Promise<number> {
    const
      { terms, params } = buildCondition(filter);
    const
      qryFrom = buildSqlFrom(dbSchema, {
        table: stripeCheckoutsessionsTable,
        tableAlias: c_alias,
        allowedPaths: [c_alias]
      });
    const
      qry = `
      select 
        count(*) as howmany 
      from 
        ${qryFrom}
      where 
        ${terms.join(" and ")}
    `;

    return dbClient =>
      dbClient.
        query(qry, params).
        then(({ rows }) =>
          rows.map(row => parseInt(row["howmany"]))[0]
        );
  }

  function buildCondition(filter: Filter) {
    let terms: [string, ...string[]] = ["true"];
    let params: any[] = [];
    const offset = filter.pagination?.offset ?? 0;
    const limit = filter.pagination?.limit ?? 30;

    addEq(terms, params, `${c_alias}.${stripeCheckoutsessionsFields.id}`, filter.id);
    addEq(terms, params, `${c_alias}.${stripeCheckoutsessionsFields.agent_id}`, filter.agent_id);
    return { terms, params, offset, limit };
  }
}