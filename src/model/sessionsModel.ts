

import { SessionDTO } from "./common/dtos/SessionDTO";
import { rowToSession } from "./common/rows2obj";
import { sessionsFields, sessionsTable } from "./common/schema/sessionsTable";

import { DbCrudUtils, SqlHelpers, SqlWhereHelpers } from "agentor-lib";
import { withDbCli, withDbTrx } from "lib/AppContextUtils";
import { PoolClient } from 'pg';
import { dbSchema } from './common/schema/dbSchema';
const
  { addEq, addGt, addGtEq, addLt, addLtEq, addNotIn, addIsNotNull, addIn, addNotExists, addIsFkOf, addIsNotFkOf } = SqlWhereHelpers,
  { insertRecord } = DbCrudUtils,
  { buildSqlFields, buildSqlFrom } = SqlHelpers;

const c_alias = "session";

export declare type SessionsFilter = {
  token?: string,
  agent_id?: string
};

export const
  getSession = (token: string) => withDbCli(getSessionImp(token)),
  createSession = (session: SessionDTO) => withDbTrx(createSessionImp(session));


function createSessionImp(dto: SessionDTO): (dbTrx: PoolClient) => Promise<SessionDTO | undefined> {
  return async dbTrx => {
    try {
      let pkResult = await insertRecord(sessionsTable, dto)(dbTrx);
      if (pkResult)
        return getSessionImp(pkResult["token"])(dbTrx);
      else
        return void 0;
    } catch (e) {
      console.log(e);
      throw e;
    }
  };
}

function getSessionImp(token: string): (dbClient: PoolClient) => Promise<SessionDTO> {

  const
    qryFields = buildSqlFields(dbSchema, { table: sessionsTable, tableAlias: c_alias, allowedPaths: [c_alias] }).join(', '),
    qryFrom = buildSqlFrom(dbSchema, { table: sessionsTable, tableAlias: c_alias, allowedPaths: [c_alias, `${c_alias}_user`] });
  const
    qryParams: any[] = [],
    qryTerms: string[] = [];

  addEq(qryTerms, qryParams, `${c_alias}.${sessionsFields.token}`, token);

  const qry = `
    select 
      ${qryFields}  
    from 
      ${qryFrom}
    where
      ${qryTerms.join(" and ")}
  `;

  return async dbClient => {
    let [result] = await dbClient.query(qry, qryParams).then(result => result.rows.map(row =>
      rowToSession(row, c_alias)
    ));

    return result;
  };
}