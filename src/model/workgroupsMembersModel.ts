import { withDbCli, withDbTrx } from 'lib/AppContextUtils';
import { WorkgroupMemberDTO } from './common/dtos/WrokgroupMemberDTO';
import { rowToWorkgroupMember } from './common/rows2obj';
import { workgroupMembersTable } from './common/schema/workgroupMembersTable';


import { PoolClient } from 'pg';
import { DbCrudUtils, SqlHelpers, SqlWhereHelpers } from "agentor-lib";
import { dbSchema } from './common/schema/dbSchema';
const
  { addEq, addGt, addGtEq, addLt, addLtEq, addNotIn, addIsNotNull, addIn, addNotExists, addIsFkOf, addIsNotFkOf } = SqlWhereHelpers,
  { insertRecord, removeRecord, updateRecord } = DbCrudUtils,
  { buildSqlFields, buildSqlFrom } = SqlHelpers;


const c_rootAlias = "wgm";

export namespace workgroupMembersModel {

  export declare type Filter = {
    /**
     * the identifier of the workgroup
     */
    workgroup_id?: string,
    /**
     * The identifier of the agent
     */
    agent_id?: string,
    /**
     * afiliaciones en las que el agente puede publicar.
     * true->incluir, false->excluir, undefined->no importa
     */
    can_publish?: boolean,
    /**
     * afiliaciones en las que el agente puede leer.
     * true->incluir, false->excluir, undefined->no importa
     */
    can_read?: boolean,
    /**
     * if false, only anget id is included in the result.  By default, this is false
     */
    include_agent_info?: boolean,
    /**
     * if false, only workgroup id is included in the result.  By default, this is true
     */
    include_workgroup_info?: boolean,
    /**
     * Datos de paginación
     */
    pagination?: {
      offset?: number,
      limit?: number
    }
  };

  export const list = (filter: Filter) => withDbCli(list_imp(filter));
  export const create = (workgroupMember: WorkgroupMemberDTO) => withDbTrx(create_imp(workgroupMember));

  function create_imp(workgroupMember: WorkgroupMemberDTO): (dbTran: PoolClient) => Promise<WorkgroupMemberDTO> {
    return async dbTran => {
      let { workgroup_id, agent_id } = await insertRecord(workgroupMembersTable, workgroupMember)(dbTran);
      let [result] = await list_imp({ agent_id, workgroup_id })(dbTran);
      return result;
    };
  }
  function list_imp(filter: Filter): (dbClient: PoolClient) => Promise<WorkgroupMemberDTO[]> {
    const { terms, params, orderBy, offset, limit } = buildCondition(filter);

    const qryOptions = {
      table: workgroupMembersTable,
      tableAlias: c_rootAlias,
    };
    const qryFieldsOptions = {
      ...qryOptions,
      allowedPaths: [
        c_rootAlias,
        ...filter.include_agent_info ?? false ? [`${c_rootAlias}_agent`] : [],
        ...filter.include_workgroup_info ?? true ? [`${c_rootAlias}_workgroup`, `${c_rootAlias}_workgroup_publicationecomproduct`] : [],
      ]
    }
    const qryFromOptions = {
      ...qryOptions,
      allowedPaths: [
        c_rootAlias,
        `${c_rootAlias}_agent`,
        `${c_rootAlias}_workgroup`,
        `${c_rootAlias}_workgroup_publicationecomproduct`,
      ]
    }
    const
      qryFields = buildSqlFields(dbSchema, qryFieldsOptions).join(', '),
      qryFrom = buildSqlFrom(dbSchema, qryFromOptions);

    const qry = `
      select ${qryFields}  
      from ${qryFrom}
      ${terms.length !== 0 ? `where ${terms.join(" and ")}` : ""}
      ${orderBy.length !== 0 ? `order by ${orderBy.join(", ")}` : ""}
      offset ${offset} 
      limit ${limit}
    `;

    return dbClient => dbClient.query(qry, params).
      then(result =>
        result.rows.map(rw => rowToWorkgroupMember(rw, c_rootAlias))
      ).catch(e => {
        console.log("Error!!!", e);
        throw e;
      });

  }


  /**
   *                          
   * can_read, can_publish   include_can_read    include_can_publish  -> true
   * can_read, can_publish   
               
   * can_publish 
   * @param filter 
   * @returns
   */
  function buildCondition(filter: Filter) {
    const {
      workgroup_id,
      agent_id,
      can_publish: can_publish,
      can_read: can_read,
      pagination,

    } = filter;

    const offset = pagination?.offset ?? 0;
    const limit = pagination?.limit ?? 100;

    let terms: string[] = [];
    let params: any[] = [];
    let orderBy: string[] = [];

    addEq(terms, params, `${c_rootAlias}.workgroup_id`, workgroup_id);
    addEq(terms, params, `${c_rootAlias}.agent_id`, agent_id);
    addEq(terms, params, `${c_rootAlias}.can_read`, can_read);
    addEq(terms, params, `${c_rootAlias}.can_publish`, can_publish);

    // Default order by
    if (orderBy.length === 0) {
      orderBy.push(`${c_rootAlias}_workgroup.name asc`);
    }

    return { terms, params, limit, offset, orderBy };
  }

}