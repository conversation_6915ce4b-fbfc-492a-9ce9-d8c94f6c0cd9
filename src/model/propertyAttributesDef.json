[{"code": "totalM2", "label": {"es": "Superficie total", "default": "Total area"}, "type": "m2"}, {"code": "usefulM2", "label": {"es": "Superficie <PERSON><PERSON>", "default": "Useful surface"}, "type": "m2"}, {"code": "solarM2", "label": {"es": "Superficie solar", "default": "Solar surface"}, "type": "m2"}, {"code": "constructionYear", "label": {"es": "Año de construcción", "default": "Construction year"}, "type": "year"}, {"code": "status", "label": {"es": "Estado", "default": "Status"}, "type": "oneOf", "values": [{"code": "new", "label": {"es": "Obra nueva", "default": "New Construction"}}, {"code": "second_hand", "label": {"es": "Segunda mano", "default": "Second hand"}}]}, {"code": "conservationStatus", "label": {"es": "Estado de conservación", "default": "Conservation status"}, "type": "oneOf", "values": [{"code": "mint", "label": {"es": "A estrenar"}}, {"code": "good", "label": {"es": "Buen estado"}}, {"code": "reformed", "label": {"es": "Reformado"}}, {"code": "to_reform", "label": {"es": "A reformar"}}]}, {"code": "nIndividualBedrooms", "label": {"es": "Habitaciones individuales", "default": "Individual rooms"}, "type": "count"}, {"code": "nDoubleBedrooms", "label": {"es": "Habitaciones dobles", "default": "Double bedrooms"}, "help": {"es": "Sin baño integrado", "default": "Without integrated bathroom"}, "type": "count"}, {"code": "nSuiteBedrooms", "label": {"es": "Habitaciones dobles suites", "default": "Double suite bedrooms"}, "help": {"es": "Con baño integrado", "default": "With integrated bathroom"}, "type": "count"}, {"code": "nTotalBedrooms", "label": {"es": "Habitaciones totales", "default": "Total bedrooms"}, "help": {"es": "Individuales + Dobles + Suites", "default": "Individual + Double + Suites"}, "type": "count"}, {"code": "nBathrooms", "label": {"es": "Baños", "default": "Bathrooms"}, "help": {"es": "Total de baños incluidos los de las habitaciones suite, no incluye aseos", "default": "Total bathrooms including those in suite rooms, not including toilets"}, "type": "count"}, {"code": "bathroomsNotes", "label": {"es": "<PERSON><PERSON><PERSON> (notas)", "default": "Bathrooms (notes)"}, "help": {"es": "Notas sobre los baños", "default": "Bathrooms notes"}, "type": "text"}, {"code": "nToilets", "label": {"es": "Aseos", "default": "<PERSON><PERSON><PERSON>"}, "type": "count"}, {"code": "toiletsNotes", "label": {"es": "As<PERSON>s (notas)", "default": "<PERSON><PERSON><PERSON> (notes)"}, "help": {"es": "Notas sobre los aseos", "default": "<PERSON><PERSON><PERSON> notes"}, "type": "text"}, {"code": "<PERSON><PERSON><PERSON><PERSON>", "label": {"es": "<PERSON><PERSON><PERSON>", "default": "Buddle"}, "type": "boolean"}, {"code": "<PERSON><PERSON><PERSON><PERSON>", "label": {"es": "<PERSON><PERSON><PERSON>", "default": "Kitchen"}, "type": "boolean"}, {"code": "kitchenNotes", "label": {"es": "<PERSON><PERSON><PERSON> (notas)", "default": "Kitchen (notes)"}, "type": "text"}, {"code": "hasDinningRoom", "label": {"es": "<PERSON>dor", "default": "Dinning room"}, "help": {"es": "¿Tiene comedor independiente?", "default": "Has independente dinning room?"}, "type": "boolean"}, {"code": "dinningRoomNotes", "label": {"es": "<PERSON>dor (notas)", "default": "Dinning room (notes)"}, "type": "text"}, {"code": "hasStorageRoom", "label": {"es": "<PERSON><PERSON><PERSON>", "default": "Storage Room"}, "type": "boolean"}, {"code": "hasBalcony", "label": {"es": "Balcón", "default": "Balcony"}, "type": "boolean"}, {"code": "balconyNotes", "label": {"es": "Balcón (notas)", "default": "Balcony (notes)"}, "type": "text"}, {"code": "hasTerrace", "label": {"es": "<PERSON><PERSON>", "default": "Terrace"}, "type": "boolean"}, {"code": "nBuiltInCabinets", "label": {"es": "Armarios empotrados", "default": "Built-in cabinets"}, "type": "count"}, {"code": "hasDoubleGlasses", "label": {"es": "<PERSON><PERSON><PERSON><PERSON> dobles", "default": "Double glasses"}, "type": "boolean"}, {"code": "externalJoinery", "label": {"es": "Carpintería exterior", "default": "External joinery"}, "type": "oneOf", "values": [{"code": "aluminium", "label": {"es": "aluminio", "default": "aluminium"}}, {"code": "wood", "label": {"es": "<PERSON>ra", "default": "wood"}}, {"code": "pvc", "label": {"es": "pvc", "default": "pvc"}}, {"code": "other", "label": {"es": "otro", "default": "other"}}]}, {"code": "externalJoineryNotes", "label": {"es": "Carpintería exterior (Notas)", "default": "External joinery (Notes)"}, "type": "text"}, {"code": "ground", "label": {"es": "<PERSON><PERSON><PERSON> de <PERSON>", "default": "Ground type"}, "type": "someOf", "values": [{"code": "gres", "label": {"es": "gres", "default": "gres"}}, {"code": "marble", "label": {"es": "marmol", "default": "marble"}}, {"code": "carpet", "label": {"es": "moqueta", "default": "carpet"}}, {"code": "parquet", "label": {"es": "<PERSON><PERSON><PERSON>", "default": "parquet"}}, {"code": "laminatedFlooring", "label": {"es": "tarima flotante", "default": "laminated flooring"}}, {"code": "solidFlooring", "label": {"es": "tarima maciza", "default": "solid flooring"}}, {"code": "terrazzo", "label": {"es": "terrazo", "default": "terrazzo"}}]}, {"code": "hasWaterSupply", "label": {"es": "Suministro de agua", "default": "Water supply"}, "type": "boolean"}, {"code": "waterSupplyNotes", "label": {"es": "Agua (notas)", "default": "Water (notes)"}, "type": "text"}, {"code": "hasPowerSupply", "label": {"es": "Suministro eléctrico", "default": "Power supply"}, "type": "boolean"}, {"code": "powerSupplyNotes", "label": {"es": "Suministro eléctrico (notas)", "default": "Power supply (notes)"}, "type": "text"}, {"code": "hasGasSupply", "label": {"es": "Suministro de gas", "default": "Gas supply"}, "type": "boolean"}, {"code": "airConditioning", "label": {"es": "Aire acondicionado", "default": "Air conditioning"}, "values": [{"code": "none", "label": {"es": "no", "default": "no"}}, {"code": "cold", "label": {"es": "frío", "default": "cold"}}, {"code": "coldAndHeat", "label": {"es": "frio/calor", "default": "cold/heat"}}]}, {"code": "heating", "label": {"es": "Calefacción", "default": "Heating"}, "type": "oneOf", "values": [{"code": "none", "label": {"es": "Sin calefacción", "default": "None"}}, {"code": "central", "label": {"es": "Central", "default": "Central"}}, {"code": "electric", "label": {"es": "Eléctrica", "default": "Electric"}}, {"code": "naturalGas", "label": {"es": "Gas natural", "default": "Natural gas"}}, {"code": "gasoil", "label": {"es": "Gasoil", "default": "Gasoil"}}]}, {"code": "hasFireplace", "label": {"es": "Chimenea", "default": "Fireplace"}, "type": "boolean"}, {"code": "hasIntercom", "label": {"es": "Portero automático", "default": "Intercom"}, "type": "boolean"}, {"code": "intercomNotes", "label": {"es": "<PERSON><PERSON>á<PERSON> (notas)", "default": "Intercom (notes)"}, "type": "text"}, {"code": "hasReinforcedDoor", "label": {"es": "<PERSON><PERSON><PERSON>", "default": "Reinforced door"}, "type": "boolean"}, {"code": "reinforcedDoorNotes", "label": {"es": "<PERSON><PERSON><PERSON> (notas)", "default": "Reinforced door (notes)"}, "type": "text"}, {"code": "hasAlarmSystem", "label": {"es": "Sistema de alarma", "default": "Alarm system"}, "type": "boolean"}, {"code": "hasElevator", "label": {"es": "Ascensor", "default": "Elevator"}, "type": "boolean"}, {"code": "isHandicappedAccessible", "label": {"es": "Accesible para discapacitados", "default": "Handicapped accessible"}, "type": "boolean"}, {"code": "isFurnished", "label": {"es": "Amueblado", "default": "Furnished"}, "type": "bool<PERSON>an"}, {"code": "garden", "label": {"es": "Jardin", "default": "Garden"}, "type": "oneOf", "values": [{"code": "none", "label": {"es": "no", "default": "none"}}, {"code": "own", "label": {"es": "propio", "default": "own"}}, {"code": "community", "label": {"es": "comunitario", "default": "community"}}]}, {"code": "outsideArea", "label": {"es": "Zona exterior", "default": "Outside area"}, "type": "oneOf", "values": [{"code": "none", "label": {"es": "no", "default": "none"}}, {"code": "own", "label": {"es": "propia", "default": "own"}}, {"code": "community", "label": {"es": "comunitario", "default": "community"}}]}, {"code": "outsideAreaNotes", "label": {"es": "Zona exterior (notas)", "default": "Outside area (notes)"}, "type": "text"}, {"code": "swimmingPool", "label": {"es": "Piscina", "en": "Swimming pool"}, "type": "oneOf", "values": [{"code": "none", "label": {"es": "no", "default": "none"}}, {"code": "own", "label": {"es": "propia", "default": "own"}}, {"code": "community", "label": {"es": "comunitaria", "default": "community"}}]}, {"code": "parkingPlaces", "label": {"es": "Plazas de parking", "default": "Parkings"}, "type": "count"}, {"code": "parkingIsOptional", "label": {"es": "Parking opcional", "default": "Optional parking"}, "type": "boolean"}, {"code": "facade", "label": {"es": "Fachada", "default": "facade"}, "help": {"es": "¿Tiene comedor independiente?", "default": "Has independente dinning room?"}, "type": "someOf", "values": [{"code": "interior", "label": {"es": "interior", "default": "interior"}}, {"code": "exterior", "label": {"es": "exterior", "default": "exterior"}}]}, {"code": "orientation", "label": {"es": "Orientación", "en": "Orientation"}, "type": "someOf", "values": [{"code": "north", "label": {"es": "norte", "default": "north"}}, {"code": "northeast", "label": {"es": "noreste", "default": "northeast"}}, {"code": "east", "label": {"es": "este", "default": "east"}}, {"code": "southeast", "label": {"es": "sureste", "default": "southeast"}}, {"code": "south", "label": {"es": "sur", "default": "south"}}, {"code": "southwest", "label": {"es": "suroeste", "default": "southwest"}}, {"code": "west", "label": {"es": "oeste", "default": "west"}}, {"code": "northwest", "label": {"es": "noroeste", "default": "northwest"}}]}, {"code": "<PERSON><PERSON><PERSON><PERSON>", "label": {"es": "Soleado", "default": "<PERSON>"}, "type": "boolean"}, {"code": "sunny<PERSON><PERSON>", "label": {"es": "<PERSON><PERSON><PERSON> (notas)", "default": "<PERSON> (notes)"}, "type": "text"}, {"code": "communityFees", "label": {"es": "Gastos de comunidad", "default": "Community fees"}, "help": {"es": "<PERSON><PERSON> mensu<PERSON>", "default": "Monthly fees"}, "type": "currency"}, {"code": "nNeighborsPerFlor", "label": {"es": "Vecinos por planta", "default": "Neighbors per floor"}, "type": "count"}, {"code": "nBuildingFloors", "label": {"es": "Plantas del edificio", "default": "Building floors"}, "type": "count"}, {"code": "floor", "label": {"es": "Planta", "default": "Floor"}, "type": "oneOf", "values": [{"code": "basement", "label": {"es": "<PERSON><PERSON><PERSON>", "default": "Basement"}, "help": {"es": "Planta de un edificio situado por debajo del nivel de la calle", "default": "Enclosure of a building located below street level"}}, {"code": "semibasement", "label": {"es": "<PERSON>sótano", "default": "Semi-basement"}, "help": {"es": "Planta de un edificio situada en parte bajo el nivel de la calle.", "default": "Set of enclosures located partly below street level."}}, {"code": "ground", "label": {"es": "<PERSON><PERSON> baja", "default": "Ground floor"}}, {"code": "mezzanine", "label": {"es": "Entresuel<PERSON>", "default": "Mezzanine"}}, {"code": "main", "label": {"es": "Principal", "default": "Main"}}, {"code": "1", "label": {"es": "1", "default": "1"}}, {"code": "2", "label": {"es": "2", "default": "2"}}, {"code": "3", "label": {"es": "3", "default": "3"}}, {"code": "4", "label": {"es": "4", "default": "4"}}, {"code": "5", "label": {"es": "5", "default": "5"}}, {"code": "6", "label": {"es": "6", "default": "6"}}, {"code": "7", "label": {"es": "7", "default": "7"}}, {"code": "8", "label": {"es": "8", "default": "8"}}, {"code": "9", "label": {"es": "9", "default": "9"}}, {"code": "10", "label": {"es": "10", "default": "10"}}, {"code": "11", "label": {"es": "11", "default": "11"}}, {"code": "12", "label": {"es": "12", "default": "12"}}, {"code": "13", "label": {"es": "13", "default": "13"}}, {"code": "14", "label": {"es": "14", "default": "14"}}, {"code": "15", "label": {"es": "15", "default": "15"}}, {"code": "16", "label": {"es": "16", "default": "16"}}, {"code": "17", "label": {"es": "17", "default": "17"}}, {"code": "18", "label": {"es": "18", "default": "18"}}, {"code": "19", "label": {"es": "19", "default": "19"}}, {"code": "20", "label": {"es": "20", "default": "20"}}, {"code": "penthouse", "label": {"es": "<PERSON><PERSON><PERSON>", "en": "Penthouse"}}]}, {"code": "energyCertificate", "label": {"es": "Certificado energético", "default": "Energy certificate"}, "type": "oneOf", "values": [{"code": "available", "label": {"es": "Disponible", "default": "Available"}}, {"code": "inProcess", "label": {"es": "En trámite", "default": "In process"}}, {"code": "exempt", "label": {"es": "Exento", "default": "Exempt"}}]}, {"code": "consumptionLevel", "label": {"es": "Consumo energético", "default": "Energy consumption"}, "values": [{"code": "A", "label": {"es": "A", "default": "A"}}, {"code": "B", "label": {"es": "B", "default": "B"}}, {"code": "C", "label": {"es": "C", "default": "C"}}, {"code": "D", "label": {"es": "D", "default": "D"}}, {"code": "E", "label": {"es": "E", "default": "E"}}, {"code": "F", "label": {"es": "F", "default": "F"}}, {"code": "G", "label": {"es": "G", "default": "G"}}]}, {"code": "emissionLevel", "label": {"es": "Nivel de emisiones", "default": "Energy consumption"}, "type": "oneOf", "values": [{"code": "A", "label": {"es": "A", "default": "A"}}, {"code": "B", "label": {"es": "B", "default": "B"}}, {"code": "C", "label": {"es": "C", "default": "C"}}, {"code": "D", "label": {"es": "D", "default": "D"}}, {"code": "E", "label": {"es": "E", "default": "E"}}, {"code": "F", "label": {"es": "F", "default": "F"}}, {"code": "G", "label": {"es": "G", "default": "G"}}]}]