

import { rowToEcomproduct } from './common/rows2obj';
import { ecomProductsTable } from './common/schema/ecomProductsTable';



import { SqlHelpers, SqlWhereHelpers } from "agentor-lib";
import { withDbCli } from 'lib/AppContextUtils';
import { PoolClient } from 'pg';
import { EcomProductDTO } from './common/dtos/EcomProductDTO';
import { dbSchema } from './common/schema/dbSchema';
const
  { addEq } = SqlWhereHelpers,
  { buildSqlFields, buildSqlFrom } = SqlHelpers;

const c_root_alias = "product";

export namespace EcomProductsModel {

  export declare type Filter = {
    id?: string,
    pagination?: {
      offset?: number,
      limit?: number
    }
  };

  export const list = (filter: Filter) => withDbCli(
    list_imp(filter)
  );

  function list_imp(filter: Filter): (dbClient: PoolClient) => Promise<EcomProductDTO[]> {
    const { terms, params, orderBy, offset, limit } = buildCondition(filter);

    const qryOptions = {
      table: ecomProductsTable,
      tableAlias: c_root_alias,
      // Foreign keys
      allowedPaths: [
        c_root_alias,
      ]
    };
    const
      qryFields = buildSqlFields(dbSchema, qryOptions).join(', '),
      qryFrom = buildSqlFrom(dbSchema, qryOptions);

    const qry = `
      select ${qryFields}  
      from ${qryFrom}
      ${terms.length !== 0 ? `where ${terms.join(" and ")}` : ""}
      ${orderBy.length !== 0 ? `order by ${orderBy.join(", ")}` : ""}
      offset ${offset} 
      limit ${limit}
    `;

    return dbClient => dbClient.query(qry, params).
      then(({ rows }) =>
        rows.map(rw => rowToEcomproduct(rw, c_root_alias))
      );
  }



  function buildCondition(filter: Filter) {
    const { id, pagination } = filter;
    const offset = pagination?.offset ?? 0;
    const limit = pagination?.limit ?? 30;

    let terms: string[] = [];
    let params: any[] = [];
    let orderBy: string[] = [];


    addEq(terms, params, `${c_root_alias}.id`, id);

    return { terms, params, offset, limit, orderBy };
  }
}