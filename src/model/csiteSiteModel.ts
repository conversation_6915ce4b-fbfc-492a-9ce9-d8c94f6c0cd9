import { withDb<PERSON><PERSON>, withDbTrx } from "lib/AppContextUtils";
import { rowToCsiteSite, rowToCsiteSitePK } from "./common/rows2obj";

import { DbCrudUtils, SqlHelpers, SqlWhereHelpers } from "agentor-lib";
import { PoolClient } from 'pg';
import { dbSchema } from './common/schema/dbSchema';
import { CsiteSiteDTO, CsiteSitePkDto } from "./common/dtos/CsiteSiteDTO";
import { csiteSitesFields, csiteSitesTable } from "./common/schema/csiteSitesTable";
import { contactsFields } from "./common/schema/contactsTable";
import { DetailLevel } from "./common/DetailLevel";
const
  { addEq } = SqlWhereHelpers,
  { buildSqlFields, buildSqlFrom } = SqlHelpers,
  { insertRecord } = DbCrudUtils;

const c_root_alias = "site";
const c_contact_alias = `${c_root_alias}_contact`;
const c_lastopportunity_alias = `${c_root_alias}_lastopportunity`;

export namespace csiteSitesModel {
  export declare type ListOptions = {
    /** Slug (e identificador) del site de oportunidades */
    slug?: string,
    /** Contacto al que pertenece el site */
    contact_id?: string,
    contact_agent_id?: string,
    pagination?: {
      /**
       * Número máximo de tokens a obtener.  Por defecto 100
       */
      limit?: number,
      /**
       * Registro inicial en el resultado (contando desde 0).  Por defecto 0
       */
      offset?: number,
    },
    details?: {
      contact?: DetailLevel,
      lastopportunity?: DetailLevel,
    }
  }

  export const list = (options: ListOptions) => withDbCli(list_imp(options));
  export const exists = (options: ListOptions) => withDbCli(exists_imp(options));
  export const create = (contactsite: CsiteSiteDTO) => withDbTrx(create_imp(contactsite));

  // #region Implementation

  function exists_imp(options: ListOptions): (dbClient: PoolClient) => Promise<boolean> {
    const
      { terms, params } = buildCondition(options);
    
    const
      sqlOptions = {
        table: csiteSitesTable,
        tableAlias: c_root_alias        
      };
    const
      qry = `
        select exists (select * 
          from ${buildSqlFrom(dbSchema, sqlOptions)}
          where ${terms.join(" and ")} 
        ) as exists`;

    return dbClient => dbClient.query(qry, params).
      then(result => result.rows[0]["exists"]);
  }

  function list_imp(options: ListOptions): (dbClient: PoolClient) => Promise<CsiteSiteDTO[]> {
    const
      { terms, params, limit, offset, orderby } = buildCondition(options);
      const selectPaths = [
        `${c_root_alias}`,
        ...options.details?.contact !== void 0 ? [
          `${c_root_alias}_contact`
        ] : [],  
        ...options.details?.lastopportunity !== void 0 ? [
          c_lastopportunity_alias
        ] : [],  
        
      ];
    const
      sqlOptions = {
        table: csiteSitesTable,
        tableAlias: c_root_alias,
        
      };
    const
      qry = `
        select ${buildSqlFields(dbSchema, {...sqlOptions, allowedPaths: selectPaths}).join(', ')} 
        from ${buildSqlFrom(dbSchema, { ...sqlOptions, allowedPaths: [c_root_alias, c_contact_alias,c_lastopportunity_alias] })}
        where ${terms.join(" and ")} 
        ${orderby.length === 0 ? "" : `order by ${orderby.join(", ")}`}
        offset ${offset} limit ${limit}
      `;

    return dbClient => dbClient.
      query(qry, params).
      then(({ rows }) =>
        rows.map(r =>
          rowToCsiteSite(r, c_root_alias)
        )
      );
  }

  function create_imp(contactsite: CsiteSiteDTO): (dbTran: PoolClient) => Promise<CsiteSitePkDto> {
    return (dbTran: PoolClient) =>
      insertRecord(csiteSitesTable, contactsite)(dbTran).then(
        pkRow => rowToCsiteSitePK(pkRow, "")
      );

  }
  function buildCondition(options: ListOptions) {

    const { pagination, slug, contact_id, contact_agent_id } = options;
    const orderby = [];
    const offset = pagination?.offset ?? 0;
    const limit = pagination?.limit ?? 30;

    let terms: [string, ...string[]] = ["true"];
    let params: any[] = [];

    addEq(terms, params, `${c_root_alias}.${csiteSitesFields.slug}`, slug)
    addEq(terms, params, `${c_root_alias}.${csiteSitesFields.contact_id}`, contact_id);
    addEq(terms, params, `${c_contact_alias}.${contactsFields.agent_id}`, contact_agent_id);

    // El posible uso de paginación requiere de una ordenación invariante
    orderby.push(`${c_root_alias}.${csiteSitesFields.slug} asc`);

    return {
      terms,
      params,
      offset,
      limit,
      orderby
    };

  }
  // #endregion
}