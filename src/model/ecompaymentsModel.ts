import { withDbCli, withDbTrx } from 'lib/AppContextUtils';
import { AscOrDesc } from './common/AscOrDesc';
import { EcomPaymentDTO } from './common/dtos/EcomPaymentDTO';
import { rowToEcompayment } from './common/rows2obj';
import { ecomPaymentsFields as EcPayFields, ecomPaymentsTable } from './common/schema/ecomPaymentsTable';
import { ecomTransactionsFields as EcTrnFields } from './common/schema/ecomTransactionsTable';

import { PoolClient } from 'pg';
import { DbCrudUtils, SqlHelpers, SqlWhereHelpers } from "agentor-lib";
import { dbSchema } from './common/schema/dbSchema';
const
  { addEq, addGtEq, addLt, addLtEq } = SqlWhereHelpers,
  { insertRecord } = DbCrudUtils,
  { buildSqlFields, buildSqlFrom } = SqlHelpers;

const c_root_alias = "payment";

export namespace EcomPaymentsModel {

  export declare type Filter = {
    id?: string,
    transaction_id?: string,
    /** Fecha valor debe ser estríctamente menor que la indicada */
    valuedate_lt?: Date,
    /** Fecha valor debe ser igual o menor que la indicada */
    valuedate_max?: Date,
    /** Fecha valor debe ser igual o mayor que la indicada */
    valuedate_min?: Date,
    purchase_id?: string,
    transaction_sourceaccount_id?: string,
    transaction_destinationaccount_id?: string,
    sortBy?: {
      valuedate: AscOrDesc
    }
    pagination?: {
      offset?: number,
      limit?: number
    }
  };
  export const
    list = (filter: Filter) => withDbCli(
      list_imp(filter)
    ),
    last = (purchase_id: string) => withDbCli(
      last_imp(purchase_id)
    ),
    create = (dto: EcomPaymentDTO) => withDbTrx(
      create_imp(dto)
    );
  /**
   * Get last purchase payment
   * @param dbClient 
   * @param purchase_id 
   */
  function last_imp(purchase_id: string): (dbClient: PoolClient) => Promise<EcomPaymentDTO | undefined> {
    return async dbClient => {
      const [thLast] = await list_imp({ purchase_id: purchase_id, sortBy: { valuedate: AscOrDesc.desc }, pagination: { limit: 1 } })(dbClient);
      return thLast;
    };
  }
  function create_imp(dto: EcomPaymentDTO): (dbTran: PoolClient) => Promise<EcomPaymentDTO> {
    return async dbTran => {
      let { id } = await insertRecord(ecomPaymentsTable, dto)(dbTran);
      let [newPayment] = await list_imp({ id })(dbTran);
      return newPayment;
    }
  }


  function list_imp(filter: Filter): (dbClient: PoolClient) => Promise<EcomPaymentDTO[]> {
    const { terms, params, orderBy, offset, limit } = buildCondition(filter);

    const qryOptions = {
      table: ecomPaymentsTable,
      tableAlias: c_root_alias,
      // Foreign keys
      allowedPaths: [
        c_root_alias,
        `${c_root_alias}_transaction`
      ]
    };
    const
      qryFields = buildSqlFields(dbSchema, qryOptions).join(', '),
      qryFrom = buildSqlFrom(dbSchema, qryOptions);

    const qry = `
      select ${qryFields}  
      from ${qryFrom}
      ${terms.length !== 0 ? `where ${terms.join(" and ")}` : ""}
      ${orderBy.length !== 0 ? `order by ${orderBy.join(", ")}` : ""}
      offset ${offset} 
      limit ${limit}
    `;

    return dbClient => dbClient.query(qry, params).
      then(result =>
        result.rows.map(rw => rowToEcompayment(rw, c_root_alias))
      ).catch(e => {
        console.log("Error!!!", e);
        throw e;
      });
  }



  function buildCondition(filter: Filter) {
    const { id, valuedate_lt, valuedate_max, valuedate_min, transaction_id, transaction_sourceaccount_id, transaction_destinationaccount_id, purchase_id, pagination } = filter;
    const offset = pagination?.offset ?? 0;
    const limit = pagination?.limit ?? 30;

    let terms: string[] = [];
    let params: any[] = [];
    let orderBy: string[] = [];


    addEq(terms, params, `${c_root_alias}.${EcPayFields.id}`, id);
    addEq(terms, params, `${c_root_alias}.${EcPayFields.transaction_id}`, transaction_id);
    addEq(terms, params, `${c_root_alias}.transaction.${EcTrnFields.sourceaccount_id}`, transaction_sourceaccount_id);
    addEq(terms, params, `${c_root_alias}.transaction.${EcTrnFields.destinationaccount_id}`, transaction_destinationaccount_id);
    addEq(terms, params, `${c_root_alias}.${EcPayFields.purchase_id}`, purchase_id);
    addGtEq(terms, params, `${c_root_alias}.${EcPayFields.valuedate}`, valuedate_min);
    addLtEq(terms, params, `${c_root_alias}.${EcPayFields.valuedate}`, valuedate_max);
    addLt(terms, params, `${c_root_alias}.${EcPayFields.valuedate}`, valuedate_lt);

    if (filter.sortBy?.valuedate) {
      orderBy.push(`${c_root_alias}.${EcPayFields.valuedate} ${filter.sortBy.valuedate === AscOrDesc.desc ? "desc" : "asc"}`);
    } else {
      orderBy.push(`${c_root_alias}.${EcPayFields.date} asc`);
    }

    return { terms, params, offset, limit, orderBy };
  }
}