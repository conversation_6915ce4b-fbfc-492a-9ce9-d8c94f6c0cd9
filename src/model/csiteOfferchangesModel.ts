import { withDb<PERSON><PERSON>, withDbTrx } from "lib/AppContextUtils";
import { rowToCsiteOfferchange } from "./common/rows2obj";
import { DbCrudUtils, SqlHelpers, SqlWhereHelpers } from "agentor-lib";
import { PoolClient } from 'pg';
import { dbSchema } from './common/schema/dbSchema';
import { AscOrDesc } from "./common/AscOrDesc";
import { OfferstatusCode } from "./common/dtos/OfferstatusDTO";
import { offersFields } from "./common/schema/offersTable";
import { CsiteOfferchangeDTO } from "./common/dtos/CsiteOfferchangeDTO";
import { csiteOfferChangesFields, csiteOfferChangesTable } from "./common/schema/csiteOfferChangesTable";
const
  { addEq, addIsNull, addIn, addIsFkOf, addGt } = SqlWhereHelpers,
  { buildSqlFields, buildSqlFrom } = SqlHelpers,
  { insertRecord, updateRecord, removeRecord } = DbCrudUtils;

const c_root_alias = "offerchange";
const c_offer_alias = `${c_root_alias}_offer`;

export namespace csiteOfferchangesModel {
  export declare type ListOptions = {
    /** Identificador mayor que... (usado si deseamos hacer un recorrido secuencial completo) */
    id_gt?: string,
    /** Oferta del stream */
    offer_id?: string,
    /** Agente al que pertenece la oferta del stream */
    offer_agent_id?: string,

    offer_status_codes?: OfferstatusCode[];

    orderby?: {
      id?: AscOrDesc
    },

    pagination?: {
      /**
       * Número máximo de tokens a obtener.  Por defecto 100
       */
      limit: number,
      /**
       * Registro inicial en el resultado (contando desde 0).  Por defecto 0
       */
      offset: number,
    }
  }

  export const list = (options: ListOptions) => withDbCli(
    list_imp(options)
  );

  export const remove = (id: string) => withDbTrx(
    removeRecord(csiteOfferChangesTable, { id })
  );

  // #region Implementation

  function list_imp(options: ListOptions): (dbClient: PoolClient) => Promise<CsiteOfferchangeDTO[]> {
    const
      { terms, params, orderby, limit, offset } = buildCondition(options);
    const
      sqlOptions = {
        table: csiteOfferChangesTable,
        tableAlias: c_root_alias
      };
    const
      qry = `
        select ${buildSqlFields(dbSchema, sqlOptions).join(', ')} 
        from ${buildSqlFrom(dbSchema, { ...sqlOptions, allowedPaths: [c_root_alias, c_offer_alias] })}
        where ${terms.join(" and ")} 
        ${orderby.length === 0 ? "" :
          `order by ${orderby.join(", ")}`
        }
        offset ${offset} limit ${limit}
      `;

    return dbClient => dbClient.
      query(qry, params).
      then(({ rows }) =>
        rows.map(r =>
          rowToCsiteOfferchange(r, c_root_alias)
        )
      );
  }

  function buildCondition(options: ListOptions) {

    const { pagination, id_gt, offer_id, offer_agent_id, offer_status_codes, orderby } = options;
    const orderBy = [];
    const offset = pagination?.offset ?? 0;
    const limit = pagination?.limit ?? 30;

    let terms: [string, ...string[]] = ["true"];
    let params: any[] = [];
    addGt(terms, params, `${c_root_alias}.${csiteOfferChangesFields.id}`, id_gt);
    addEq(terms, params, `${c_root_alias}.${csiteOfferChangesFields.offer_id}`, offer_id);
    addEq(terms, params, `${c_offer_alias}.${offersFields.agent_id}`, offer_agent_id);
    addIn(terms, params, `${c_offer_alias}.${offersFields.status_code}`, offer_status_codes);

    if (orderby?.id !== void 0)
      orderBy.push(`${c_root_alias}.${csiteOfferChangesFields.id} ${orderby.id === AscOrDesc.desc ? "desc" : "asc"}`);
    else
      orderBy.push(`${c_root_alias}.${csiteOfferChangesFields.id} asc`);


    return {
      terms,
      params,
      offset,
      limit,
      orderby: orderBy
    };

  }
  // #endregion
}