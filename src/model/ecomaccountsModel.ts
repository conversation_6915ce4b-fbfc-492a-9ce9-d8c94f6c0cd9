import { Db<PERSON>rudUtils, SqlHelpers, SqlWhereHelpers } from "agentor-lib";
import { withDbCli, withDbTrx } from 'lib/AppContextUtils';
import { PoolClient } from 'pg';
import { EcomAccountDTO } from './common/dtos/EcomAccountDTO';
import { rowToEcomaccount } from './common/rows2obj';
import { dbSchema } from './common/schema/dbSchema';
import { ecomAccountsTable } from './common/schema/ecomAccountsTable';
const
  { addEq } = SqlWhereHelpers,
  { insertRecord } = DbCrudUtils,
  { buildSqlFields, buildSqlFrom } = SqlHelpers;

const c_root_alias = "account";
export namespace EcomAccountsModel {

  export declare type Filter = {
    id?: string,
    agent_id?: string,
    pagination?: {
      offset?: number,
      limit?: number
    }
  };
  export const
    read = (id: string) => withDbCli(
      read_imp(id)
    ),
    list = (filter: Filter) => withDbCli(
      list_imp(filter)
    ),
    create = (dto: EcomAccountDTO) => withDbTrx(
      create_imp(dto)
    ),
    addToBalance = (account_id: string, amount: number | string) => withDbTrx(
      addToBalance_imp(account_id, amount)
    );

  function create_imp(dto: EcomAccountDTO): (dbTran: PoolClient) => Promise<EcomAccountDTO> {
    return async dbTran => {
      let { id } = await insertRecord(ecomAccountsTable, dto)(dbTran);
      let [newAccount] = await list_imp({ id })(dbTran);
      return newAccount;
    }
  }
  function addToBalance_imp(account_id: string, amount: number | string): (dbTran: PoolClient) => Promise<number | null> {
    return dbTran =>
      dbTran.
        query("update ecom_accounts set balance = balance + $2 where id=$1 returning balance", [account_id, amount]).
        then(result => result.rows.length === 0 ? null : Number(result.rows[0]["balance"]));
  }
  function read_imp(id: string): (dbClient: PoolClient) => Promise<EcomAccountDTO | undefined> {
    return dbClient => list_imp({ id })(dbClient).then(([account]) => account)
  }
  function list_imp(filter: Filter): (dbClient: PoolClient) => Promise<EcomAccountDTO[]> {
    const { terms, params, orderBy, offset, limit } = buildCondition(filter);

    const qryOptions = {
      table: ecomAccountsTable,
      tableAlias: c_root_alias,
      // Foreign keys
      allowedPaths: [
        c_root_alias,
      ]
    };
    const
      qryFields = buildSqlFields(dbSchema, qryOptions).join(', '),
      qryFrom = buildSqlFrom(dbSchema, qryOptions);

    const qry = `
      select ${qryFields}  
      from ${qryFrom}
      ${terms.length !== 0 ? `where ${terms.join(" and ")}` : ""}
      ${orderBy.length !== 0 ? `order by ${orderBy.join(", ")}` : ""}
      offset ${offset} 
      limit ${limit}
    `;

    return dbClient => dbClient.query(qry, params).
      then(({ rows }) =>
        rows.map(rw => rowToEcomaccount(rw, c_root_alias))
      ).catch(e => {
        console.log("Error!!!", e);
        throw e;
      });
  }

  function buildCondition(filter: Filter) {
    const { id, agent_id, pagination } = filter;
    const offset = pagination?.offset ?? 0;
    const limit = pagination?.limit ?? 30;

    let terms: string[] = [];
    let params: any[] = [];
    let orderBy: string[] = [];

    addEq(terms, params, `${c_root_alias}.id`, id);
    addEq(terms, params, `${c_root_alias}.agent_id`, agent_id);

    return { terms, params, offset, limit, orderBy };
  }
}