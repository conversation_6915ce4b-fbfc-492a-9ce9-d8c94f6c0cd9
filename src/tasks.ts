import { CrwOffersTasks } from "controllers/tasks/crawlers/CrwOffersTasks";
import { CrwZonesTasks } from "controllers/tasks/crawlers/CrwZonesTasks";
import { CsiteOpportunitiesTasks } from "controllers/tasks/csite/CsiteOpportunitiesTasks";
import { MediasTasks } from "controllers/tasks/MediasTasks";
import { MilunportalesOffersTasks } from "controllers/tasks/milunportales/MilunportalesOffersTasks";
import { InmofactoryOffersTasks } from "controllers/tasks/publications/adevinta/InmofactoryOffersTasks";
import { PisoscomOffersTasks } from "controllers/tasks/publications/pisoscom/pisoscomOffersTasks";
import { WorksGroupsTasks } from "controllers/tasks/WorkgroupsTasks";
import { AppContext, setAppContext } from "lib/AppContext";
import { CloudProvidersModel } from "model/cloudprovidersModel";
const config = require("../config/app.js");
const argv = require('minimist')(process.argv.slice(2));

const ctx = new AppContext({
  db: config.db,
  storages: config.storages,
  geoloc: config.geoloc,
  email: config.email,
  stripe: config.stripe,
  tusolucionhipotecaria: config.tusolucionhipotecaria,
  crwidealista_api: config.crwidealista_api,
  crwhaya_api: config.crwhaya_api,
  crwservihabitat_api: config.crwservihabitat_api,
  crwsolvia_api: config.crwsolvia_api,
  crwindividuals_api: config.crwindividuals_api,
  crwunicaja_api: config.crwunicaja_api,
  crwportalnow_api: config.crwportalnow_api,
  crwaliseda_api: config.crwaliseda_api,
  crwuci_api: config.crwuci_api,
  csite: config.csite,
  messagesbroker: config.messagesbroker,
  logger: {level: argv.logger_level ?? 'info'},
  
});
setAppContext(ctx);

(async () => {

  try {
    const
      sync_crwidealista = argv.sync_crwidealista ?? false,
      sync_crwhaya =  argv.sync_crwhaya ?? false,
      sync_crwservihabitat =  argv.sync_crwservihabitat ?? false,
      sync_crwsolvia =  argv.sync_crwsolvia ?? false,
      sync_crwunicaja =  argv.sync_crwunicaja ?? false,
      sync_crwportalnow =  argv.sync_crwportalnow ?? false,
      sync_crwaliseda =  argv.sync_crwaliseda ?? false,
      sync_crwhabitaclia = argv.sync_crwhabitaclia ?? false,
      sync_crwindividuals = argv.sync_crwindividuals ?? false,
      sync_crwuci =  argv.sync_crwuci ?? false,
      pub_inmofactory = argv.pub_inmofactory ?? false,
      inmofactory_conciliate = argv.inmofactory_conciliate ?? false,
      inmofactory_logjsons = argv.inmofactory_logjsons ?? false,
      pub_pisoscom = argv.pub_pisoscom ?? false,
      pub_milunportales = argv.pub_milunportales ?? false,
      collect_garbage = argv.collect_garbage ?? false,
      automatic_payments = argv.automatic_payments ?? false,
      csite_generateopportunities = argv.csite_generateopportunities ?? false;

    ctx.logger.debug(JSON.stringify({ sync_crwidealista, pub_milunportales, pub_inmofactory, inmofactory_conciliate, inmofactory_logjsons, pub_pisoscom, collect_garbage, automatic_payments, }));

    if (sync_crwidealista) {
      await CrwZonesTasks.importZonesFromCrwIdealista();
      await CrwOffersTasks.importOffers(CloudProvidersModel.providersIds.crwidealista);
    }
    if (sync_crwhabitaclia) {
      await CrwOffersTasks.importOffers(CloudProvidersModel.providersIds.crwhabitaclia);
    }
    if (sync_crwhaya) {
      await CrwOffersTasks.importOffers(CloudProvidersModel.providersIds.crwhaya);
    }
    if(sync_crwindividuals){
      await CrwOffersTasks.importOffers(CloudProvidersModel.providersIds.crwindividuals);
    }
    if (sync_crwservihabitat) {
      await CrwOffersTasks.importOffers(CloudProvidersModel.providersIds.crwservihabitat);
    }
    if (sync_crwsolvia) {
      await CrwOffersTasks.importOffers(CloudProvidersModel.providersIds.crwsolvia);
    }
    if (sync_crwunicaja) {
      await CrwOffersTasks.importOffers(CloudProvidersModel.providersIds.crwunicaja);
    }
    if (sync_crwportalnow) {
      await CrwOffersTasks.importOffers(CloudProvidersModel.providersIds.crwportalnow);
    }
    if (sync_crwaliseda) {
      await CrwOffersTasks.importOffers(CloudProvidersModel.providersIds.crwaliseda);
    }
    if (sync_crwuci) {
      await CrwOffersTasks.importOffers(CloudProvidersModel.providersIds.crwuci);
    }
    if (pub_milunportales) {
      await MilunportalesOffersTasks.publishOffersTask();
    }
    if (pub_inmofactory) {
      await InmofactoryOffersTasks.publishOffersTask({ conciliate: inmofactory_conciliate, logJson: inmofactory_logjsons });
    }
    if (pub_pisoscom) {
      await PisoscomOffersTasks.publishOffersTask( { conciliate: false, logJson: false });
    }
    if (collect_garbage) {
      await MediasTasks.removeUnusedMedias();
    }
    if (automatic_payments) {
      await WorksGroupsTasks.generatePublicationsPayments();
    }
    if(csite_generateopportunities){
      await CsiteOpportunitiesTasks.generateOpportunities();
    }
    // Vistas materializadas.  Debemos refrescarlas.  Pueden tardar 3 minutos para 900000 registros
    //if (sync_crwidealista ) {
    //  await ctx.db.withClient(cli => cli.query("REFRESH MATERIALIZED VIEW nonhistoric_cloudoffers_mvw"));
    //}
  } catch (e) {
    ctx.logger.error("process ended with error", e);
  } finally {
    await ctx.dispose();
  }
})();




