
--
--  ecom_deposits:  Añadimos la cuantía 
--
DO $$
DECLARE
  new_version integer:= 78;
BEGIN
  IF (  SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
  
  
    Alter table stripe_checkoutsessions 
      Add column total numeric(12, 4);
    
    Update stripe_checkoutsessions Set total = (data->>'amount_total') :: numeric(12,4) / 100.0;

    Alter table stripe_checkoutsessions Alter column total Set Not null;

      
    INSERT INTO schema_changes (version, script)
      VALUES (new_version, to_char(new_version, 'FM000') || '.sql');

  /* END */
  END IF;
END;
$$;

