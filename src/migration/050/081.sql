
--
--  <PERSON><PERSON> eliminarse todas las workgroup_offers de ofertas cuyo estado no sea comercialización
--  siempre y cuando no se traten del workgroup "cloud"
--
DO $$
DECLARE
  new_version integer:= 81;
BEGIN
  IF (  SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
  
    
    insert into actiontypes(
      id, label, logicorder
    ) values (
      13, 
      '{"ca": "Hipoteca", "en": "Mortage", "es": "Hipoteca", "default": "Mortage"}', 
      75
    )  on conflict(id) do nothing;
    
    INSERT INTO schema_changes (version, script)
      VALUES (new_version, to_char(new_version, 'FM000') || '.sql');

  /* END */
  END IF;
END;
$$;

