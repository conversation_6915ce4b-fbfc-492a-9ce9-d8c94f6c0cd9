--
--  <PERSON><PERSON> newkey() no debe incluir caracteres '+' ni '/' en el resultado para que puedan ser usados en las URLS de forma friendly
--
DO $$
Declare
  new_version integer:=68;
Begin
  If (select max(version) from public.schema_changes)=(new_version-1) Then
      
  Create or Replace function newkey()
    Returns character varying
    Language 'plpgsql'
    Cost 100
    Volatile
  AS $BODY$
    Begin
      Return replace(replace(left(encode(decode(md5(concat(random()::character varying, now()::character varying)),'hex'),'base64'), 22),'+','x'),'/','7');
    End;
  $BODY$;

    Insert into schema_changes (version, script) values (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */

  End if;

End;
$$;