
--
--  El número de la dirección se define como "number" en vez de "streetnumber" para
--  evitar malentendidos con los datos streettype y streetname que se refieren a 
--  attributos de la propia calle, mientras que number se refiere al número en la 
--  vía (number in street).. no al número "de la" vía, porque la vía tiene nombre, 
--  no número.
--
DO $$
DECLARE
new_version integer:=55;
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
        
    ALTER TABLE public.properties
      RENAME address_streetnumber TO address_number;
 
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */

  END IF;

END;
$$;

