--
--  <PERSON><PERSON><PERSON> tipo de acción "Localizar"
--
DO $$
DECLARE
new_version integer:=50;
BEGIN

  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    
    insert into actiontypes (label, logicorder) values ('{"ca": "Localitzar", "en": "Locate", "es": "Localizar", "default": "Locate"}', 10);

    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */
  END IF;

END;
$$;
