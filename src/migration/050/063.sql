
--
--  Grupos de trabajo para publicar en portales: yaencontre.com, idealista.com, pisos.com.
--  * Los grupos son:  Portales gratuitos, Fotocasa y Habitaclia.
--  Todos los agentes serán miembros de estos grupos (Esto debe ser una regla de negocio en la creación de agentes... no lo gestionamos con SP)
DO $$
DECLARE
new_version integer:=63;
_tb_internal_agent integer;
_tb_cloud_agent integer:=0;
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN

    select id into _tb_internal_agent from agents where email='<EMAIL>';
    
      
    insert into workgroups(id, owner_id, name) values ('7e51c5c6-7b63-11eb-a6ba-06d99be0472b', _tb_internal_agent, 'yaencontre.com');
    insert into workgroups(id, owner_id, name) values ('933e993c-7b63-11eb-a6bb-06d99be0472b', _tb_internal_agent, 'idealista.com');
    insert into workgroups(id, owner_id, name) values ('acaea93e-7b63-11eb-a6bc-06d99be0472b', _tb_internal_agent, 'pisos.com');

    insert into workgroup_members (workgroup_id, agent_id, can_publish, can_read) select '7e51c5c6-7b63-11eb-a6ba-06d99be0472b' as group_id, id as agent_id, true as can_write, false as can_read from agents where id<>_tb_internal_agent and id<>_tb_cloud_agent;
    insert into workgroup_members (workgroup_id, agent_id, can_publish, can_read) select '933e993c-7b63-11eb-a6bb-06d99be0472b' as group_id, id as agent_id, true as can_write, false as can_read from agents  where id<>_tb_internal_agent and id<>_tb_cloud_agent;
    insert into workgroup_members (workgroup_id, agent_id, can_publish, can_read) select 'acaea93e-7b63-11eb-a6bc-06d99be0472b' as group_id, id as agent_id, true as can_write, false as can_read from agents  where id<>_tb_internal_agent and id<>_tb_cloud_agent;


    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */

  END IF;

END;
$$;

