--
--  Optimizaciones (índices)
--
DO $$
DECLARE
  new_version integer:= 89;
  
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */

-- Index: offers_status_type_updated_id_idx

-- DROP INDEX public.offers_status_type_updated_id_idx;

  CREATE INDEX offers_status_type_updated_id_idx
    ON public.offers USING btree
    (status_code COLLATE pg_catalog."default" ASC NULLS LAST, sale_allowed ASC NULLS LAST, rent_allowed ASC NULLS LAST, updated_at DESC NULLS LAST, id DESC NULLS LAST)
    TABLESPACE pg_default;


    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;