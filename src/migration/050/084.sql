--
-- Indicar en los tipos de acción si ofertas y contactos son obligatorios.
-- Indicar si las acciones que se creen deben tener un "disclaimer"
--
DO $$
DECLARE
  new_version integer:= 84;
  _agt_id integer;
BEGIN
  IF (  SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN

    CREATE TABLE public.services
    (
      code character varying NOT NULL,
      label jsonb NOT NULL,
      disclaimer_has boolean NOT NULL DEFAULT false,
      disclaimer_title jsonb NOT NULL DEFAULT '{}'::jsonb,
      "disclaimer_detailHtml" jsonb NOT NULL DEFAULT '{}'::jsonb,
      "disclaimer_requiresAcceptance" boolean NOT NULL DEFAULT false,
      PRIMARY KEY (code)
    );

    insert into services(
      code, 
      label, 
      disclaimer_has, 
      disclaimer_title, 
      "disclaimer_detailHtml", 
      "disclaimer_requiresAcceptance" 
    ) values (
      'tu_solucion_hipotecaria.mortagerequest',
      '{"default":"Solicitud de hipoteca (Tusoluciónhipotecaria)"}',
      true,
      ('{"default":'
        || '"Tu solicitud se enviará a la empresa colaboradora Tusolucionhipotecaria"'
        || '}')::jsonb,
      ('{"default":"' 
        || '<p>Tu solicitud se enviará a la empresa colaboradora <a href=\"http://www.tusolucionhipotecaria.com\">Tusoluciónhipotecaria</a>.</p>'
        || '<p>Los datos que se le comunicarán son:</p>' 
        || '<ul>'
        || '<li>Nombre y apellidos del contacto</li>'
        || '<li>Teléfono del contacto</li>'
        || '<li>Email del contacto</li>'
        || '<li>Tu nombre y apellidos</li>'
        || '<li>Tu teléfono de contacto</li>'
        || '</ul>'
        || '"}')::jsonb,
      true
    );
    

    -- alter table public.actiontypes add column service_has boolean not null default false;
    --'Comment on column public.actiontypes."service_has" is 'Indica que las acciones de este tipo implican la ejecución de una operación.
    -- - Las acciones vinculadas a una operación no pueden modificarse.  Su estado se marca como "hecho" internamente cuando se solicita que la operación ha sido realizda (Ej: solicitar una hipoteca se marcha hecha cuando la solicitud se ha enviado al proveedor externo)';
        
    Alter table public.actiontypes add column service_code character varying;
    Comment on column public.actiontypes."service_code" IS 'Indica el código del tipo de operación (Ej: "mortagerequest")';

    Alter table public.actiontypes add column "offerIsRequired" boolean not null default false;
    comment on column public.actiontypes."offerIsRequired" is 'La acción requiere que se indique una oferta';

    Alter table public.actiontypes add column "contactIsRequired" boolean not null default false;
    comment on column public.actiontypes."contactIsRequired" is 'La acción require que se indique un contacto';

    Alter table actiontypes Add FOREIGN KEY(service_code)  
      References services(code)
      On update restrict
      On delete restrict;
     
    
    Update actiontypes 
      set 
        service_code= 'tu_solucion_hipotecaria.mortagerequest',
        "contactIsRequired" = true,
        "label" = '{"default":"Apply for mortgage", "es":"Solicitar hipoteca"}'
      where 
        id=13;
          

    INSERT INTO schema_changes (version, script)
      VALUES (new_version, to_char(new_version, 'FM000') || '.sql');

  /* END */
  END IF;
END;
$$;

