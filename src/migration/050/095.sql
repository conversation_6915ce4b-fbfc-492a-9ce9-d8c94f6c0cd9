--
-- Eliminamos de la vista updated_at e incorporamos created_at
--
DO $SCHEMAPROC$
DECLARE
  new_version integer:= 95;
  
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */

		CREATE OR REPLACE FUNCTION ngrams(varchar, integer) RETURNS SETOF TEXT AS $$
		SELECT SUBSTRING($1 FROM n FOR $2)::TEXT
		FROM GENERATE_SERIES(1, LENGTH($1)-($2-1), 1) n;
		$$ LANGUAGE SQL IMMUTABLE;

		CREATE OR REPLACE FUNCTION all_ngrams_string(varchar) RETURNS text AS $$
		SELECT 
			ARRAY_TO_STRING(
				case when length($1)>2 then Array(select * from ngrams($1,3)) else array[]::text[] end ||
				case when length($1)>3 then Array(select * from ngrams($1,4)) else array[]::text[] end ||
				case when length($1)>4 then Array(select * from ngrams($1,5)) else array[]::text[] end ||
				case when length($1)>5 then Array(select * from ngrams($1,6)) else array[]::text[] end ||
				case when length($1)>6 then Array(select * from ngrams($1,7)) else array[]::text[] end ||
				case when length($1)>7 then Array(select * from ngrams($1,8)) else array[]::text[] end ||
				case when length($1)>8 then Array(select * from ngrams($1,9)) else array[]::text[] end ||
				case when length($1)>9 then Array(select * from ngrams($1,10)) else array[]::text[] end ||
				case when length($1)>10 then Array(select * from ngrams($1,11)) else array[]::text[] end ||
				case when length($1)>11 then Array(select * from ngrams($1,12)) else array[]::text[] end ||
				case when length($1)>12 then Array(select * from ngrams($1,13)) else array[]::text[] end ||
				case when length($1)>13 then Array(select * from ngrams($1,14)) else array[]::text[] end ||
				case when length($1)>14 then Array(select * from ngrams($1,15)) else array[]::text[] end ||
				case when length($1)>15 then Array(select * from ngrams($1,16)) else array[]::text[] end ||
				case when length($1)>16 then Array(select * from ngrams($1,17)) else array[]::text[] end ||
				case when length($1)>17 then Array(select * from ngrams($1,18)) else array[]::text[] end 
			,' '
			) 	
		$$ LANGUAGE SQL IMMUTABLE;

		DROP MATERIALIZED VIEW public.nonhistoric_cloudoffers_mvw;

		CREATE MATERIALIZED VIEW public.nonhistoric_cloudoffers_mvw
		TABLESPACE pg_default
		AS
		SELECT 
				offers.id,
				offers.status_code,
				offers.sale_allowed,
				offers.sale_amount,
				offers.rent_allowed,
				offers.rent_amount,
				offers."source_announcedByAnIndividual",
				offers."source_contact_phone",
				to_tsvector('english'::regconfig, all_ngrams_string(regexp_replace(source_contact_phone,'[\s+]','','g') )) as source_contact_phone_tsv,
				properties.type_code AS property_type_code,
				properties.zone_id AS property_zone_id,
				properties."attributes_totalSurfaceM2" AS "property_attributes_totalSurfaceM2",
				offers.created_at
			FROM offers
				JOIN properties ON offers.property_id = properties.id
			WHERE 
				offers.status_code::text <> 'historic'::text AND 
				(EXISTS ( 
						SELECT 
							workgroup_offers.member_workgroup_id,
							workgroup_offers.member_agent_id,
							workgroup_offers.offer_id,
							workgroup_offers.ecompurchase_id
						FROM workgroup_offers
						WHERE 
							offers.id = workgroup_offers.offer_id 
							AND workgroup_offers.member_workgroup_id = '9b51df7e-492e-11eb-8f96-cf0cb77c50dd'::uuid
				)) AND
				(
				-- Eliminamos ofertas que se consideran "basurilla": inmuebles de 99999999€ de idealista, por ejemplo
					coalesce(offers.sale_amount,0)<99000000 and
					coalesce(offers.rent_amount,0)<99000000
				)
				--
		WITH DATA;

		
		GRANT ALL ON TABLE public.nonhistoric_cloudoffers_mvw TO app_user;

		CREATE INDEX nonhistoric_cloudoffers_mvw_rent_amount_asc_id_idx
				ON public.nonhistoric_cloudoffers_mvw USING btree
				(sale_allowed, rent_allowed, rent_amount, id)
				TABLESPACE pg_default;
		CREATE INDEX nonhistoric_cloudoffers_mvw_sale_amount_asc_id_idx
				ON public.nonhistoric_cloudoffers_mvw USING btree
				(sale_allowed, rent_allowed, sale_amount, id)
				TABLESPACE pg_default;
		CREATE INDEX nonhistoric_cloudoffers_mvw_type_rent_amount_asc_id_idx
				ON public.nonhistoric_cloudoffers_mvw USING btree
				(property_type_code COLLATE pg_catalog."default", sale_allowed, rent_allowed, rent_amount, id)
				TABLESPACE pg_default;
		CREATE INDEX nonhistoric_cloudoffers_mvw_type_sale_amount_asc_id_idx
				ON public.nonhistoric_cloudoffers_mvw USING btree
				(property_type_code COLLATE pg_catalog."default", sale_allowed, rent_allowed, sale_amount, id)
				TABLESPACE pg_default;
		CREATE INDEX nonhistoric_cloudoffers_mvw_createdat_asc_id_idx
				ON public.nonhistoric_cloudoffers_mvw USING btree
				(sale_allowed, rent_allowed, created_at, id)
				TABLESPACE pg_default;
		CREATE INDEX nonhistoric_cloudoffers_mvw_zone_type_rent_amount_asc_id_idx
				ON public.nonhistoric_cloudoffers_mvw USING btree
				(property_zone_id, property_type_code COLLATE pg_catalog."default", sale_allowed, rent_allowed, rent_amount, id)
				TABLESPACE pg_default;
		CREATE INDEX nonhistoric_cloudoffers_mvw_zone_type_sale_amount_asc_id_idx
				ON public.nonhistoric_cloudoffers_mvw USING btree
				(property_zone_id, property_type_code COLLATE pg_catalog."default", sale_allowed, rent_allowed, sale_amount, id)
				TABLESPACE pg_default;


		CREATE INDEX nonhistoric_cloudoffers_mvw_contactphone_idx
				ON public.nonhistoric_cloudoffers_mvw USING gist(source_contact_phone_tsv)
				TABLESPACE pg_default;


		--select distinct source_contact_phone from nonhistoric_cloudoffers_mvw where source_contact_phone_tsv @@ '656' order by source_contact_phone desc;


    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;
