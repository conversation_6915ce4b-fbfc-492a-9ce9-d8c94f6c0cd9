
--
--  Renombrar grupos de trabajo correspondientes a portales
--
DO $$
Declare
  new_version integer:=64;
Begin
  If (select max(version) from public.schema_changes)=(new_version-1) Then
   
  
    Update workgroups Set name = 'Portales gratuítos' Where id ='7ad4b302-5fb8-11eb-859f-6be55d83bfbc';
    Update workgroups Set name = 'Fotocasa' Where id ='554f2ac6-5fb9-11eb-859f-6be55d83bfbc';
    Update workgroups Set name = 'Habitaclia' Where id ='7b367802-5fb9-11eb-859f-6be55d83bfbc';
    Update workgroups Set name = 'Idealista' Where id ='933e993c-7b63-11eb-a6bb-06d99be0472b';
    Update workgroups Set name = 'Pisos.com' Where id ='acaea93e-7b63-11eb-a6bc-06d99be0472b';
    Update workgroups Set name = 'Yaencontre' Where id ='7e51c5c6-7b63-11eb-a6ba-06d99be0472b';

    
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */

  End if;

End;
$$;

