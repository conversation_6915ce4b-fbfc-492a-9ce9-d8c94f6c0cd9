--
-- tabla de soporte a la publicación en inmofactory.
--  Permite conocer la última versión publicada de cada oferta.
--
DO $$
Declare
new_version Integer:=61;
Begin
  If (Select max(version) from schema_changes)=(new_version-1) Then

    Create table tasks_inmofactory_publishedoffers
    (
      offer_id bigint Not null,
      revision timestamp with time zone Not NULL,
      Constraint tasks_inmofactory_publishedoffers_pkey primary key (offer_id)
    );

    Insert into schema_changes (version, script) values (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */

  End If;

End;
$$;

