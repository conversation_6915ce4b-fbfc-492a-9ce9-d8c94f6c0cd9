
--
--  Ubicación del inmueble (geo location)
--
DO $$
DECLARE
new_version integer:=54;
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
        
    alter table properties
      add location_longitude numeric(12,8),
      add location_latitude numeric(12,8),
      add constraint "property_location_check" CHECK (
        -- La longitud y la latitud deben funcionar como una unidad: o las 2 son nulas o las 2 tienen valor
        (location_longitude is null and location_latitude is null) or (location_longitude is not null and location_latitude is not null)
      )      
    ;
 
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */

  END IF;

END;
$$;

