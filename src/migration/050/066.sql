--
--  Productos de ecommerce asociados a la publicación en portales
--
DO $$
Declare
  new_version integer:=66;
  _tb_internal_agent integer;
  _tb_internal_account character varying := 'zOKAch1RRZ1Hsz1oTothRg';

  _tb_wg_fotocasa uuid := '554f2ac6-5fb9-11eb-859f-6be55d83bfbc';
  _tb_wg_habitaclia uuid := '7b367802-5fb9-11eb-859f-6be55d83bfbc';
  _tb_wg_idealista uuid := '933e993c-7b63-11eb-a6bb-06d99be0472b';
  _tb_wg_pisoscom uuid := 'acaea93e-7b63-11eb-a6bc-06d99be0472b';
  _tb_wg_yaencontre uuid := '7e51c5c6-7b63-11eb-a6ba-06d99be0472b';

Begin
  If (select max(version) from public.schema_changes)=(new_version-1) Then
    select id into _tb_internal_agent from agents where email='<EMAIL>';
    /* Cuenta asociada al agente interno topbrokers y que será la receptora de todos los pagos de productos del ecommerce */
    insert into ecom_accounts(id, agent_id, balance) values (_tb_internal_account,_tb_internal_agent, 0);

    /* --------------------------------------------------------------------------------------
     * Productos iniciales (todos asociados a grupos de trabajo para publicación en portales) 
     * -------------------------------------------------------------------------------------- */

    insert into ecom_products (id, account_id, name, amount, dailyamount, description) values ('PUBFOTOCASA01', _tb_internal_account,'{"default":"Publicación en Fotocasa.com"}',0.05, 0.05, '{"default":"Micropago inicial + micropago diário.  Cada micropago da derecho a 24 horas de publicación. El proceso de publicación/despublicación puede sufrir retrasos de varios minutos." }');
    update workgroups set "publicationecomproduct_id" = 'PUBFOTOCASA01' where id=_tb_wg_fotocasa;
    
    insert into ecom_products (id, account_id, name, amount, dailyamount, description) values ('PUBHABITACLIA01', _tb_internal_account,'{"default":"Publicación en habitaclia.com"}',0.05, 0.05, '{"default":"Micropago inicial + micropago diário.  Cada micropago da derecho a 24 horas de publicación. El proceso de publicación/despublicación puede sufrir retrasos de varios minutos." }');
    update workgroups set "publicationecomproduct_id" = 'PUBHABITACLIA01' where id=_tb_wg_habitaclia;

    insert into ecom_products (id, account_id, name, amount, dailyamount, description) values ('PUBIDEALISTA01', _tb_internal_account,'{"default":"Publicación en Idealista.com"}',0.05, 0.05, '{"default":"Micropago inicial + micropago diário.  Cada micropago da derecho a 24 horas de publicación. El proceso de publicación/despublicación puede sufrir retrasos de hasta 24 horas por limitaciones en la pasarela de publicación." }');  
    update workgroups set "publicationecomproduct_id" = 'PUBIDEALISTA01' where id=_tb_wg_idealista;

    insert into ecom_products (id, account_id, name, amount, dailyamount, description) values ('PUBPISOSCOM01', _tb_internal_account,'{"default":"Publicación en Pisos.com"}',0.05, 0.05, '{"default":"Micropago inicial + micropago diário.  Cada micropago da derecho a 24 horas de publicación. El proceso de publicación/despublicación puede sufrir retrasos de hasta 24 horas por limitaciones en la pasarela de publicación." }');  
    update workgroups set "publicationecomproduct_id" = 'PUBPISOSCOM01' where id=_tb_wg_pisoscom;    
    
    insert into ecom_products (id, account_id, name, amount, dailyamount, description) values ('PUBYAENCONRE01', _tb_internal_account,'{"default":"Publicación en yaencontre.com"}',0.05, 0.05, '{"default":"Micropago inicial + micropago diário.  Cada micropago da derecho a 24 horas de publicación. El proceso de publicación/despublicación puede sufrir retrasos de hasta 24 horas por limitaciones en la pasarela de publicación." }');  
    update workgroups set "publicationecomproduct_id" = 'PUBYAENCONRE01' where id=_tb_wg_yaencontre;    

    INSERT INTO schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */

  End if;

End;
$$;