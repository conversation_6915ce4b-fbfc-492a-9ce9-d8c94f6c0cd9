--
--  Ofertas favoritas de un agente
--
DO $$
DECLARE
  new_version integer:= 91;
  
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */

    create table agent_favouriteoffers (
      agent_id bigint not null,
      offer_id bigint not null,
      primary key (agent_id, offer_id),
      foreign key (agent_id) references agents(id) on update restrict on delete cascade,
      foreign key (offer_id) references offers(id) on update restrict on delete cascade
    );

    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;