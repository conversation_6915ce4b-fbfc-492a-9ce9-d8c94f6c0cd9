--
-- Subtip<PERSON> de inmueble (también llamado "categoría")
--
DO $$
DECLARE
new_version integer:=57;
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN

    alter table properties 
      add column subtype_code character varying,
      add foreign key (subtype_code) references propertysubtypes(code) on update restrict on delete cascade;
      


    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */

  END IF;

END;
$$;

