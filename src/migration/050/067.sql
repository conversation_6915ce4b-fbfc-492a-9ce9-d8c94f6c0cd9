--
--  Creamos las cuentas de ecommerce de todos los agentes (recordar que el agente "internal" ya la tiene
--
DO $$
Declare
  new_version integer:=67;
  _temprow agents%ROWTYPE;
  _id character varying;
Begin
  If (select max(version) from public.schema_changes)=(new_version-1) Then
      
    /* Creamos las cuentas de eCommerce asociadas a cada agente (las que aún no existan)*/
    For _temprow In Select * from agents where not exists (select * from ecom_accounts where agent_id=agents.id)
    Loop
      _id =  newkey();
      Insert into ecom_accounts(id, balance, agent_id) values (_id, 0, _temprow.id);
    End loop;

    Insert into schema_changes (version, script) values (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */

  End if;

End;
$$;