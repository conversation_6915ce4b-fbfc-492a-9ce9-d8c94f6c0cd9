--
--  <PERSON><PERSON><PERSON><PERSON>, como origen parte de los datos del origen de un anuncio, datos de contacto (El teléfono)
--
DO $$
DECLARE
  new_version integer:= 88;
  
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */

    ALTER TABLE public.offers
        ADD COLUMN source_contact_phone character varying;

    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;