--
--  ecommerce: Fijamos el precio de la publicación en fotocasa y habitaclia a 7.5€ iniciales por 30 días + 0.25€ al día
--
DO $$
Declare
  new_version integer:=74;
Begin
  If (select max(version) from public.schema_changes)=(new_version-1) Then
   
    update ecom_products set firstpayment_amount=7.5, firstpayment_days=30, dailyamount=0.25 where id='PUBFOTOCASA01';
    update ecom_products set firstpayment_amount=7.5, firstpayment_days=30, dailyamount=0.25 where id='PUBHABITACLIA01';
    

    INSERT INTO schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */

  End if;

End;
$$;