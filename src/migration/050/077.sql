
--
--  ecom_deposits:  depósito de cuantías monetarias en la cuenta de topbrokers.  
--                  pueden ser manuales o vinculados a una sesión de checkout de stripe.
--
DO $$
DECLARE
  new_version integer:= 77;
BEGIN
  IF (
    SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN

    Create table ecom_deposits(
      transaction_id bigint NOT NULL,
      type_code character varying NOT NULL,
      stripecheckoutsession_id character varying,
      valuedate timestamp with time zone NOT NULL DEFAULT now(),
      created_at timestamp with time zone NOT NULL DEFAULT now(),
      PRIMARY KEY (transaction_id),
      FOREIGN KEY (transaction_id)
          REFERENCES ecom_transactions (id) MATCH SIMPLE
          ON UPDATE RESTRICT
          ON DELETE CASCADE, 
      FOREIGN KEY (stripecheckoutsession_id)
          REFERENCES stripe_checkoutsessions (id) MATCH SIMPLE
          ON UPDATE RESTRICT
          ON DELETE RESTRICT 
    );


  COMMENT ON COLUMN public.ecom_deposits.type_code
      IS '"stripe" o "manual"';

      
  INSERT INTO schema_changes (version, script)
    VALUES (new_version, to_char(new_version, 'FM000') || '.sql');

  /* END */
END IF;
END;
$$;
