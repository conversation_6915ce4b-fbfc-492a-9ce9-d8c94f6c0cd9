--
--  <PERSON><PERSON> "Idealista Website" y Proveedor "Idealista Crawler"
--
DO $$
DECLARE
  new_version integer:= 86;
  -- Agente de Idealista usamos el bigint más grande posible (alejado del autoincremental)
  v_agt_id bigint:= 92*****************;
  -- Provider de Idealista usamos el bigint más grande posible (alejado del autoincremental)
  v_provider_id bigint:= 92*****************;
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */

    -- Idealista Crawler
    insert into agents(
      "id",
      "firstName",
      "email",
      "password"
    ) values (
      v_agt_id,
      'Idealista Website Crawler',
      '<EMAIL>',
      'DomYjQQc5a7Qexv21x8dCQ'
    );

    insert into ecom_accounts(agent_id) values (v_agt_id);

    insert into cloud_providers(id, agent_id, "name") values (
      v_provider_id, v_agt_id, 'Idealista Crawler'
    );
    insert into workgroup_members(workgroup_id, agent_id, can_publish, can_read) values ('9b51df7e-492e-11eb-8f96-cf0cb77c50dd', v_agt_id, true, true);

    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;