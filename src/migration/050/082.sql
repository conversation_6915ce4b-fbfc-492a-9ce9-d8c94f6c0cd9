--
--  <PERSON><PERSON> "crawler" de habitaclia basado en los ficheros obtenidos de Octoparse
--
DO $$
DECLARE
  new_version integer:= 82;
  _agt_id integer;
BEGIN
  IF (  SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
  
    insert into agents(
      "firstName",
      "email",
      "password"
    ) values (
      'Octoparse Habitaclia Manual Crawler',
      '<EMAIL>',
      'BQrN42j0mmvIy47sJ6hz0Q'
    ) returning id into _agt_id;

    insert into ecom_accounts(agent_id) values (_agt_id);
    
    insert into cloud_providers(agent_id, name) values (_agt_id, 'Habitaclia Manual Crawler');

    insert into workgroup_members(workgroup_id, agent_id, can_publish, can_read) values ('9b51df7e-492e-11eb-8f96-cf0cb77c50dd', _agt_id, true, true);
    
    INSERT INTO schema_changes (version, script)
      VALUES (new_version, to_char(new_version, 'FM000') || '.sql');

  /* END */
  END IF;
END;
$$;