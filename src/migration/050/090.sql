--
--  Optimizaciones (índices) para mejorar consultas de ofertas por 
--
DO $$
DECLARE
  new_version integer:= 90;
  
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */



  CREATE INDEX offers_sale_amount_desc_id_desc_idx
    ON public.offers USING btree
    (sale_amount DESC NULLS FIRST, id DESC NULLS FIRST);
	
	CREATE INDEX offers_sale_amount_asc_id_asc_idx
    ON public.offers USING btree
    (sale_amount ASC NULLS FIRST, id ASC NULLS FIRST);

  CREATE INDEX offers_rent_amount_desc_id_desc_idx
    ON public.offers USING btree
    (rent_amount DESC NULLS FIRST, id DESC NULLS FIRST);
	
	CREATE INDEX offers_rent_amount_asc_id_asc_idx
    ON public.offers USING btree
    (rent_amount ASC NULLS FIRST, id ASC NULLS FIRST);

    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;