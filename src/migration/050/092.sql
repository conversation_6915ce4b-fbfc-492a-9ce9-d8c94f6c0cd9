--
--  Optimizaciones (índices) para mejorar consultas de ofertas por 
--
DO $$
DECLARE
  new_version integer:= 92;
  
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */



DROP INDEX public.offers_status_type_sale_amount_id_idx;

CREATE INDEX offers_agent_status_type_sale_amount_desc_id_idx
    ON public.offers USING btree
    (
      agent_id ASC NULLS LAST, 
      status_code  ASC NULLS LAST, 
      sale_allowed ASC NULLS LAST, 
      rent_allowed ASC NULLS LAST, 
      sale_amount DESC NULLS LAST, 
      id ASC NULLS LAST
    );
    
CREATE INDEX offers_agent_status_type_sale_amount_asc_id_idx
    ON public.offers USING btree
    (
      agent_id ASC NULLS LAST, 
      status_code  ASC NULLS LAST, 
      sale_allowed ASC NULLS LAST, 
      rent_allowed ASC NULLS LAST, 
      sale_amount ASC NULLS LAST, 
      id ASC NULLS LAST
    );


    DROP INDEX public.offers_status_type_updated_id_idx;

    CREATE INDEX offers_status_type_updated_desc__id_idx
    ON public.offers USING btree
    (
      status_code COLLATE pg_catalog."default" ASC NULLS LAST, 
      sale_allowed ASC NULLS LAST, 
      rent_allowed ASC NULLS LAST, 
      updated_at DESC NULLS LAST, 
      id DESC NULLS LAST
    );


    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;