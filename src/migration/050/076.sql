
--
--  stripe:  en el checkout, solo podemos controlar de forma efectiva que ha sido completado
--
DO $$
DECLARE
  new_version integer:= 76;
BEGIN
  IF (
    SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN

    Alter table stripe_checkoutsessions 
      drop column failed_event,
      drop column failed_at;
    Alter table stripe_checkoutsessions 
      rename column succeeded_event to completed_event;
    Alter table stripe_checkoutsessions 
      rename column succeeded_at to completed_at;

      
  INSERT INTO schema_changes (version, script)
    VALUES (new_version, to_char(new_version, 'FM000') || '.sql');

  /* END */
END IF;
END;
$$;
