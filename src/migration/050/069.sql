
--
--  ecommerce
--  * Debemos marcar la fecha de finalización del servicio asociado a una compra.
--    Si no se puede determinar, se deja a null.
--    Cuando la compra acompaña a la publicación en un portal, la fecha de final de servicio será la fecha en la que la oferta se "despublica"
--    Este dato se usa para reconocer, en caso de dailyammount>0, si debemos seguir cobrando o no por el servicio.
--  * Los pagos deben tener una fecha de valor, es decir: la fecha a la que se refiere el pago (no la fecha en la que se ha registrado)
DO $$
Declare
  new_version integer:=69;
Begin
  If (select max(version) from public.schema_changes)=(new_version-1) Then


    
    Alter table ecom_payments
      Add valuedate timestamp with time zone;
    
    Comment on column ecom_payments.valuedate Is 'Fecha de valor del pago (que instante es en el que realmente tiene valor el pago) y se discrimina de "date" que es la fecha en la que se ha registrado el pago)';

    update ecom_payments set valuedate = date;

    Alter table ecom_payments
      Alter column valuedate Set Not null;

          

    INSERT INTO schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */

  End if;

End;
$$;