--
--  VISTAS MATERIALIZADAS empleadas en la consulta de ofertas activas del "cloud"
--   - Esta vista solo incluye ofertas "activas" (no históricas) y prescinde del campo "status_code"
--   - Esta vista solo incluye ofertas publicadas en el workgroup "Cloud"
DO $$
DECLARE
  new_version integer:= 93;
  
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */

-- drop materialized view nonhistoric_cloudoffers_mvw;
DROP MATERIALIZED VIEW public.nonhistoric_cloudoffers_mvw;

CREATE MATERIALIZED VIEW public.nonhistoric_cloudoffers_mvw
AS
 SELECT 
 		offers.id,
    offers.status_code,
    offers.sale_allowed,
    offers.sale_amount,
    offers.rent_allowed,
    offers.rent_amount,
    offers."source_announcedByAnIndividual",
    properties.type_code AS property_type_code,
    properties.zone_id AS property_zone_id,
    properties."attributes_totalSurfaceM2" AS "property_attributes_totalSurfaceM2",
    offers.updated_at
   FROM offers
     JOIN properties ON offers.property_id = properties.id
  WHERE
  	offers.status_code::text <> 'historic'::text
		and exists (
			select * from workgroup_offers 
			where 
				offers.id = workgroup_offers.offer_id 
				AND workgroup_offers.member_workgroup_id = '9b51df7e-492e-11eb-8f96-cf0cb77c50dd'::uuid
		);
-- Vista para ofertas del cloud por zona, tipo y precio venta
	CREATE INDEX nonhistoric_cloudoffers_mvw_sale_amount_asc_id_idx
    ON public.nonhistoric_cloudoffers_mvw USING btree ( 
		sale_allowed ASC NULLS LAST, 
		rent_allowed ASC NULLS LAST, 
		sale_amount ASC NULLS LAST, 
		id ASC NULLS LAST
	);
	CREATE INDEX nonhistoric_cloudoffers_mvw_updatedat_asc_id_idx
    ON public.nonhistoric_cloudoffers_mvw USING btree ( 
		sale_allowed ASC NULLS LAST, 
		rent_allowed ASC NULLS LAST, 
		updated_at ASC NULLS LAST, 
		id ASC NULLS LAST
	);
	-- Vista para ofertas del cloud por zona, tipo y precio
	CREATE INDEX nonhistoric_cloudoffers_mvw_type_sale_amount_asc_id_idx
    ON nonhistoric_cloudoffers_mvw USING btree ( 
		property_type_code ASC NULLS LAST,
		sale_allowed ASC NULLS LAST, 
		rent_allowed ASC NULLS LAST, 
		sale_amount ASC NULLS LAST, 
		id ASC NULLS LAST
	);
	-- Vista para ofertas del cloud por zona, tipo y precio venta
	CREATE INDEX nonhistoric_cloudoffers_mvw_zone_type_sale_amount_asc_id_idx
    ON public.nonhistoric_cloudoffers_mvw USING btree ( 
		property_zone_id ASC NULLS LAST,
		property_type_code ASC NULLS LAST,
		sale_allowed ASC NULLS LAST, 
		rent_allowed ASC NULLS LAST, 
		sale_amount ASC NULLS LAST, 
		id ASC NULLS LAST
	);
	
	-- Vista para ofertas del cloud por precio alquiler
	CREATE INDEX nonhistoric_cloudoffers_mvw_rent_amount_asc_id_idx
    ON public.nonhistoric_cloudoffers_mvw USING btree ( 
		sale_allowed ASC NULLS LAST, 
		rent_allowed ASC NULLS LAST, 
		rent_amount ASC NULLS LAST, 
		id ASC NULLS LAST
	);
	-- Vista para ofertas del cloud por zona, tipo y precio alquiler
	CREATE INDEX nonhistoric_cloudoffers_mvw_type_rent_amount_asc_id_idx
    ON public.nonhistoric_cloudoffers_mvw USING btree ( 
		property_type_code ASC NULLS LAST,
		sale_allowed ASC NULLS LAST, 
		rent_allowed ASC NULLS LAST, 
		rent_amount ASC NULLS LAST, 
		id ASC NULLS LAST
	);
	-- Vista para ofertas del cloud por zona, tipo y precio alquiler
	CREATE INDEX nonhistoric_cloudoffers_mvw_zone_type_rent_amount_asc_id_idx
    ON public.nonhistoric_cloudoffers_mvw USING btree ( 
		property_zone_id ASC NULLS LAST,
		property_type_code ASC NULLS LAST,
		sale_allowed ASC NULLS LAST, 
		rent_allowed ASC NULLS LAST, 
		rent_amount ASC NULLS LAST, 
		id ASC NULLS LAST
	);


    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;
