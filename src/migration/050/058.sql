--
-- Check<PERSON> que el subtipo del inmueble está relacionado con el tipo del inmueble 
-- (no se usa una clave compuesta por simplicidad... así que la coherencia se verifica con un trigger en vez de una clave foránea)
--
DO $$
DECLARE
new_version integer:=58;
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN


    Create function check_property_type_and_subtype_fields()
        returns trigger
        language 'plpgsql'
        cost 100
        volatile not leakproof
    As $BODY$
    Begin
        If (NEW.subtype_code is not null) then
          If(  (select count(*) from propertysubtypes where type_code = NEW.type_code and code=NEW.subtype_code) <>1  ) then
            Raise exception 'property subtype is not associated to the property type'; 
          End if;
        End if;
          
        Return NEW;
    End;
    $BODY$;      


    Create trigger check_property_type_and_subtype_fields_trg
      Before update 
      On properties
      For each ROW
        Execute procedure check_property_type_and_subtype_fields();

    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */

  END IF;

END;
$$;

