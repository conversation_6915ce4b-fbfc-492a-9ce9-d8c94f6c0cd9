---
---  Publicar todo el producto publicable de todos los agentes @topbrokers.io en
---  en pisos.com
---  - El proceso genera una compra asociada (y la debida transacción depago)
---  - Por seguridad, solo se aplica a agentes que ya estén publicando en otros portales
---

begin transaction;

create or replace procedure publish_offer_on_pisoscom(var_offer_id bigint)
language plpgsql
as $$
DECLARE 
  
  var_agent_id bigint ;
  var_account_id character varying;
  var_pisoscom_wg_id uuid = 'acaea93e-7b63-11eb-a6bc-06d99be0472b';
  var_ecomproduct_id character varying;
  var_ecomproduct_account_id character varying;
  var_ecomproduct_firstpayment_amount numeric(12,4);
  var_ecomproduct_firstpayment_days integer;
  var_ecomproduct_dailyamount numeric(12,4);
  var_purchase_id bigint;
	var_transaction_id bigint;
	var_amount numeric(12,4) := 10;
	var_balance numeric(12,4);
	var_date timestamptz;
BEGIN
  -- Is actually published?
  if ( select count(*) from workgroup_offers where member_workgroup_id = var_pisoscom_wg_id and offer_id=var_offer_id)>0 then
    RAISE INFO 'OFFER % YA PUBLICADA', var_offer_id;	
  else
  	raise info 'offer % VA A SER PUBLICADA', var_offer_id;
    -- Agent of the offer
	select agent_id into var_agent_id from offers where id=var_offer_id;
	if(var_agent_id is null) then
      RAISE INFO 'OFFER % NO EXISTE', var_offer_id;
	else
	  -- ¿La publicación necesita que se adquiera un producto?
	  select publicationecomproduct_id into var_ecomproduct_id from workgroups where id=var_pisoscom_wg_id;
	  if(var_ecomproduct_id is not null) then
		-- Tenemos que generar una compra!!!

		-- Cuenta de ecommerce
		select  id into var_account_id from ecom_accounts where agent_id=var_agent_id;
		if (var_account_id is  null) then
		  RAISE EXCEPTION 'AGENT % HAS NOT ECOM ACCOUNT', var_agent_id;
		else
		  var_date = now();
		  -- Necesitamos los datos para generar la compra
		  select 
		    account_id,
		  	firstpayment_amount, 
			firstpayment_days, 
			dailyamount
		  into  
		  	var_ecomproduct_account_id,
 			var_ecomproduct_firstpayment_amount,
  			var_ecomproduct_firstpayment_days,
  			var_ecomproduct_dailyamount
		  from
		    ecom_products
		  where 
		    id=var_ecomproduct_id;
		  -- y ahora generamos la compra
		  insert into ecom_purchases(
			  product_id,
			  buyer_id,
			  "date",
			  details,
			  firstpayment_amount,
			  firstpayment_days,
			  dailyamount,
			  service_type_code,
			  service_offer_id,
			  service_workgroup_id
		  ) 
		  values (
			  var_ecomproduct_id,
			  var_account_id,
			  var_date,
			  'Publicación manual de' || cast(var_offer_id as character varying) || 'en pisoscom',
			  var_ecomproduct_firstpayment_amount,
			  var_ecomproduct_firstpayment_days,
			  var_ecomproduct_dailyamount,
			  'publication',
			  var_offer_id,
			  var_pisoscom_wg_id			  
		  ) returning id into var_purchase_id;
		  -- Generamos la transacción del primer pago
		  insert into ecom_transactions(
              sourceaccount_id,
			  destinationaccount_id,
	 		  amount,
			  "date"
          ) values (
              var_account_id,
			  var_ecomproduct_account_id,
			  var_ecomproduct_firstpayment_amount,
			  var_date
          ) returning id into var_transaction_id;
		  -- Descontamos del balance de la cuenta "origen" de la transacción
		  update ecom_accounts set balance = balance - var_ecomproduct_firstpayment_amount where id=var_account_id;
		  if ( select balance from ecom_accounts where id=var_account_id)<0 then
		    RAISE EXCEPTION 'No hay fondos suficientes para publicar %', var_offer_id;
		  else
		      -- Incrementamos el balance de la cuenta "destino" de la transacción
			  update ecom_accounts set balance = balance + var_ecomproduct_firstpayment_amount where id=var_ecomproduct_account_id;
			  -- Generamos el pago asociado a la transacción y a la compra
			  insert into ecom_payments(
				  date,
				  purchase_id,
				  transaction_id,
				  valuedate
			  ) values (
				  var_date,
				  var_purchase_id,
				  var_transaction_id,
				  var_date			  
			  );
            end if;
			
			-- creamos la compartición asociada a la compra
			RAISE NOTICE '% % % %', var_pisoscom_wg_id, var_agent_id, var_offer_id, var_purchase_id;
            insert into workgroup_offers(member_workgroup_id, member_agent_id, offer_id, ecompurchase_id) values (var_pisoscom_wg_id, var_agent_id, var_offer_id, var_purchase_id);
		end if;
	  else
	    -- no hay compra asociada
	    insert into workgroup_offers(member_workgroup_id, member_agent_id, offer_id) values (var_pisoscom_wg_id, var_agent_id, var_offer_id);
	  end if;
	  
	end if;
  end if;
  raise info 'yata';
END$$;


DO $$
DECLARE 
  r record;
BEGIN
  for r in select distinct offer_id 
  			from 
				workgroup_offers wgo
				inner join agents a on wgo.member_agent_id = a.id
			where 
				a.email ilike '%@topbrokers.io' and
				wgo.member_workgroup_id in ('554f2ac6-5fb9-11eb-859f-6be55d83bfbc','7b367802-5fb9-11eb-859f-6be55d83bfbc','933e993c-7b63-11eb-a6bb-06d99be0472b')
			--limit 10
  loop
    call publish_offer_on_pisoscom(r.offer_id);
  end loop;
  
END$$;

drop procedure publish_offer_on_pisoscom(bigint);

rollback;
-- commit;