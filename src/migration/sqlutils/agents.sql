select 
  id,
  "firstName", "lastName", email, mobile, created_at, 
  (select count(*) from offers where agent_id=agents.id) offers_cnt,  
  (select count(*) from contacts where agent_id=agents.id) contacts_cnt,
  (select count(*) from actions where agent_id=agents.id) actions_cnt,
  (select count(*) from demands where agent_id=agents.id) demands_cnt
from agents 
where id<>9223372036854775807
order by id desc;
