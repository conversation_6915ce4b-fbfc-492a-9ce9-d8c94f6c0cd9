---
---
---  INGRESAR DINERO MANUALMENTE A UN AGENTE (ej:)
---
---

begin transaction;

DO $$
DECLARE 
	var_agent_id bigint := 91;
	var_account_id character varying;
	var_transaction_id bigint;
	var_amount numeric(12,4) := 10;
	var_balance numeric(12,4);
BEGIN
	
  select  id into var_account_id from ecom_accounts where agent_id=var_agent_id;
	if (var_account_id is not null) then
		RAISE INFO 'ACCOUNT %', var_account_id;	
	
		insert into ecom_transactions(
			destinationaccount_id,
			amount
		) values (
			var_account_id,
			var_amount
		) returning id into var_transaction_id;
	
	  RAISE INFO 'TRANSACTION %', var_transaction_id;
		
		insert into ecom_deposits(
			transaction_id,
			type_code
		) values (
			var_transaction_id,
			'manual'
		);
		
		update ecom_accounts set balance = balance + var_amount where id=var_account_id;
		
		select balance into var_balance from ecom_accounts where id=var_account_id;
		
		RAISE INFO 'TOTAL BALANCE %', var_balance;
	end if;
 
END$$;

rollback;