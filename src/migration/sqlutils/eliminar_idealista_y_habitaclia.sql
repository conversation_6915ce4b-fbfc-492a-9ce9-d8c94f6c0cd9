
begin transaction;

update 
	ecom_purchases 
set 
	service_enddate = now() 
where 
	service_workgroup_id in('933e993c-7b63-11eb-a6bb-06d99be0472b','7b367802-5fb9-11eb-859f-6be55d83bfbc')
	and service_enddate is null;
	
delete
from 
	workgroup_offers 
where
	member_workgroup_id in ('933e993c-7b63-11eb-a6bb-06d99be0472b','7b367802-5fb9-11eb-859f-6be55d83bfbc');

delete
from
	workgroup_members
where 
	workgroup_id in ('933e993c-7b63-11eb-a6bb-06d99be0472b','7b367802-5fb9-11eb-859f-6be55d83bfbc') and
	agent_id<>4;

ROLLBACK;
-- COMMIT;