--
--  action types table
--
DO $$
DECLARE
  new_version integer:= 16;
BEGIN
   IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */
    
   CREATE TABLE public.action_types
    (
        id bigserial NOT NULL,
        label jsonb NOT NULL DEFAULT '{}',
        PRIMARY KEY (id)
    );

    insert into action_types(label) values 
      ('{"es":"Llamar por teléfono", "en":"Phonen call", "ca":"Trucar per telèfon"}'),
      ('{"es":"Visitar inmueble", "en":"Visit the property", "ca":"visitar l''immoble"}'),
      ('{"es":"Firmar arras", "en":"sign down payment contract", "ca":"Signar contracte d''arres"}');

    alter table actions add column type_id bigint not null;
    alter table actions add constraint actions_type_id_fk foreign key (type_id) references action_types(id);
    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;


      
      