--
--  media files de los inmuebles
--
DO $$
DECLARE
  new_version integer:= 27;
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */

    CREATE TABLE propertymedias
    (
      property_id bigint NOT NULL,
      media_key character varying NOT NULL,
      PRIMARY KEY (property_id, media_key),
      FOREIGN KEY (property_id)
          REFERENCES properties (id) MATCH SIMPLE
          ON UPDATE RESTRICT
          ON DELETE CASCADE
          NOT VALID,
      FOREIGN KEY (media_key)
          REFERENCES medias (key) MATCH SIMPLE
          ON UPDATE RESTRICT
          ON DELETE CASCADE
          NOT VALID
    );
    
    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;