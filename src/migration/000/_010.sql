--
--  Users
--
DO $$
DECLARE
new_version integer:=10;
BEGIN

  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */
    -- Table: public.users

    -- DROP TABLE public.users;

    CREATE TABLE public.users
    (
        id bigserial NOT NULL,
        username character varying COLLATE pg_catalog."default" NOT NULL,
        password character varying COLLATE pg_catalog."default" NOT NULL,
        created_at timestamp with time zone NOT NULL DEFAULT now(),
        updated_at timestamp with time zone NOT NULL DEFAULT now(),
        CONSTRAINT users_pkey PRIMARY KEY (id)
    )
    WITH (
        OIDS = FALSE
    )
    TABLESPACE pg_default;

    ALTER TABLE public.users
        OWNER to admin;

    CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE PROCEDURE  update_updated_at_column();

    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */

  END IF;

END;
$$;