--
--  Campos usados pero que no estaban en las migraciones
--
DO $$
DECLARE
  new_version integer:= 25;
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
  /* BEGIN */

    ALTER TABLE public.properties
      add column created_at timestamp with time zone NOT NULL DEFAULT now(),
      add column updated_at timestamp with time zone NOT NULL DEFAULT now()
    ;
    
    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;