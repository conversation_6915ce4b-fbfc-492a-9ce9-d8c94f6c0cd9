--
--  Los inmuebles deben asociarse a una zona
--  De momento, dicha zona es opcional (debido a que ya existen inmuebles en la BBDD)... 
--
DO $$
DECLARE
  new_version integer:= 39;
BEGIN
   IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */
    
    ALTER TABLE properties 
      ADD COLUMN zone_id uuid,
      ADD CONSTRAINT properties_zone_id_fk FOREIGN KEY (zone_id) REFERENCES propertyzones(id) on delete SET NULL on update RESTRICT;
    

   /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;