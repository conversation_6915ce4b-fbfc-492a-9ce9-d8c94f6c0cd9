--
--  actions table
--
DO $$
DECLARE
  new_version integer:= 9;
BEGIN
   IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */


   CREATE TABLE public.action_types
    (
        id bigserial NOT NULL,
        label jsonb NOT NULL DEFAULT '{}',
        PRIMARY KEY (id)
    );

    insert into action_types(label) values 
      ('{"default":"Phone call", "es":"Llamar por teléfono", "en":"Phonen call", "ca":"Trucar per telèfon"}'),
      ('{"default":"Meeting with the owner", "es":"Reunión con propietario", "en":"Meeting with the owner", "ca":"Reunió amb el propietari"}'),
      ('{"default":"Visit the property", "es":"Visitar inmueble", "en":"Visit the property", "ca":"visitar l''immoble"}'),
      ('{"default":"Sign down payment contract", "es":"Firmar arras", "en":"Sign down payment contract", "ca":"Signar contracte d''arres"}');

    
    CREATE TABLE public.actions
    (
        id bigserial NOT NULL,
        type_id bigint not null,
        description character varying,
        agent_id bigint NOT NULL,
        offer_id bigint,
        scheduled_for timestamp with time zone NOT NULL,
        done boolean,
        created_at timestamp with time zone NOT NULL DEFAULT now(),
        updated_at timestamp with time zone NOT NULL DEFAULT now(),
        PRIMARY KEY (id),
        FOREIGN KEY (type_id)
            REFERENCES action_types(id)
            on update RESTRICT
            on delete cascade,
        FOREIGN KEY (agent_id)
            REFERENCES public.agents (id)
            ON UPDATE RESTRICT
            ON DELETE CASCADE,
        FOREIGN KEY (offer_id)
            REFERENCES public.offers (id)
            ON UPDATE RESTRICT
            ON DELETE CASCADE
    )
    WITH (
        OIDS = FALSE
    );

    CREATE TRIGGER update_actions_updated_at BEFORE UPDATE ON actions FOR EACH ROW EXECUTE PROCEDURE  update_updated_at_column();
  
    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;


      
      