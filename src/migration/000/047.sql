--
--  Descripción pública de la oferta
--  Causas de histórico
--
DO $$
DECLARE
new_version integer:=47;
BEGIN

  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    

    DROP TRIGGER update_computed_offer_fields ON public.offers;
    DROP FUNCTION public.update_computed_offer_fields();

    CREATE FUNCTION public.update_computed_offer_fields()
        RETURNS trigger
        LANGUAGE 'plpgsql'
        COST 100
        VOLATILE NOT LEAKPROOF
    AS $BODY$
    BEGIN

      IF TG_OP = 'UPDATE' THEN
        IF OLD.status_code<>'historic' and NEW.status_code='historic' THEN
        -- Assign historic date when status changes to historic
          NEW.historic_date = coalesce(NEW."historic_date", now());
        ELSIF OLD.status_code='historic' and NEW.status_code<>'historic' THEN
          -- Nullify all historic status associated data
          NEW.historic_date = null;
          NEW.historic_cause_code = null;
        END IF;
      ELSE
        IF NEW.status_code='historic' THEN
          -- Assign historic date when status changes to historic
          NEW.historic_date = coalesce(NEW."historic_date", now());
        END IF;
      END IF;
            
      RETURN NEW; 
    END;
    $BODY$;


    CREATE TRIGGER update_computed_offer_fields
        BEFORE INSERT OR UPDATE 
        ON public.offers
        FOR EACH ROW
        EXECUTE PROCEDURE public.update_computed_offer_fields();

        

    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */

  END IF;

END;
$$;
