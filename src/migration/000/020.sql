--
--  offer status
--
DO $$
DECLARE
  new_version integer:= 20;
BEGIN
   IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */

    CREATE TABLE offer_statuses (
      code varchar not null primary key,
      label jsonb not null default '{}'::json,
      -- usado para conocer el orden lógico de los estados (para no mostrarlos desordenados)
      logicorder integer not null
    );

    insert into offer_statuses(
      code, logicorder, label
    ) values 
      ('news',1,'{"default":"news", "es":"noticia", "en":"news"}'), 
      ('location',2,'{"default":"location", "es":"localización","en":"location"}'),
      ('commercialization',3,'{"default":"commercialization", "es":"comercialización","en":"commercialization"}'),
      ('finished',4, '{"default":"finished", "es":"finalizada", "en":"finished" }');

    alter table offers 
      add column status_code varchar not null default 'news';


    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;