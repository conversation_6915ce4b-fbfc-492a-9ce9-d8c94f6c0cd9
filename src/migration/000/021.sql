--
--  offer status: Se añade "catchment" ("captación") y se substituye "finished" ("finalizada") por "historic" ("histórico")
--
DO $$
DECLARE
  new_version integer:= 21;
BEGIN
   IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */

    insert into offer_statuses(
      code, logicorder, label
    ) values 
      ('news',1,'{"default":"news", "es":"noticia", "en":"news"}'), 
      ('location',2,'{"default":"location", "es":"localización","en":"location"}'),
      ('catchment',3,'{"default":"catchment", "es":"captación","en":"catchment"}'),
      ('commercialization',4,'{"default":"commercialization", "es":"comercialización","en":"commercialization"}'),
      ('historic',5, '{"default":"historic", "es":"histórico", "en":"historic" }')
    on conflict(code)
    do update set logicorder=EXCLUDED.logicorder, label=EXCLUDED.label;

    delete from offer_Statuses where code='finished';
    

    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;