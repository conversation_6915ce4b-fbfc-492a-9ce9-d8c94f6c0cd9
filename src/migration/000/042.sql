--
--  La oferta tiene un principio y final de mandato
--
DO $$
DECLARE
  new_version integer:= 42;
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */

    CREATE TABLE cloud_providers
    (
        id bigserial NOT NULL,
        agent_id bigint NOT NULL,
        name character varying NOT NULL,
        PRIMARY KEY (id)
    );

    CREATE TABLE public.cloud_offers
    (
      offer_id bigint NOT NULL,
      cloud_provider_id bigint NOT NULL,
      cloud_id character varying NOT NULL,
      cloud_revision character varying  NOT NULL,      
      
      CONSTRAINT cloud_offers_pkey PRIMARY KEY (offer_id),
      CONSTRAINT cloud_offers_offer_id_fkey FOREIGN KEY (offer_id)
          REFERENCES offers (id) MATCH SIMPLE
          ON UPDATE RESTRICT
          ON DELETE CASCADE,
      CONSTRAINT cloud_offers_provider_id_fkey FOREIGN KEY (cloud_provider_id)
          REFERENCES cloud_providers (id) MATCH SIMPLE
          ON UPDATE RESTRICT
          ON DELETE CASCADE
    );

    ALTER TABLE cloud_offers
      ADD UNIQUE (cloud_provider_id, cloud_id)
      DEFERRABLE;

    -- Homez Crawler
    insert into agents(id, "firstName", email, password) values (
      0, 'Cloud Crawler', '<EMAIL>', '44433322211109867542346'
    );
    insert into cloud_providers(id, agent_id, "name") values (
      0, 0, 'homez'
    );


    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;