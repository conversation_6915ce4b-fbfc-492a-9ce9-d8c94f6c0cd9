--
--  add private notes to contact and offer
--
DO $$
DECLARE
  new_version integer:= 12;
BEGIN
   IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */
    ALTER TABLE public.properties
      ADD COLUMN attributes_m2 numeric not null,
      ADD COLUMN "attributes_constructionYear" integer,
      ADD COLUMN "attributes_realFloor" integer,
      ADD COLUMN "attributes_nRooms" integer,
      ADD COLUMN "attributes_nBathrooms" integer,
      ADD COLUMN "attributes_nToilets" integer,
    
      ADD COLUMN "attributes_hasKitchen" boolean,
      ADD COLUMN "attributes_hasDiningRoom" boolean,
      ADD COLUMN "attributes_hasStorageRoom" boolean,
      ADD COLUMN "attributes_hasGarage" boolean,
      ADD COLUMN "attributes_hasLaundry" boolean,

      ADD COLUMN "attributes_hasBalcony" boolean,
      ADD COLUMN "attributes_hasTerrace" boolean,
      ADD COLUMN "attributes_hasGarden" boolean,
      ADD COLUMN "attributes_hasPool" boolean,

      ADD COLUMN "attributes_hasElevator" boolean,
      ADD COLUMN "attributes_hasHeating" boolean,
      ADD COLUMN "attributes_hasAirConditioner" boolean,
      ADD COLUMN "attributes_hasGas" boolean,
      ADD COLUMN "attributes_hasFurniture" boolean,

      ADD COLUMN "attributes_isSunny" boolean,
      ADD COLUMN "attributes_isExterior" boolean;
    
    ALTER TABLE public.properties
      DROP COLUMN attributes;
  
    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;


      
      