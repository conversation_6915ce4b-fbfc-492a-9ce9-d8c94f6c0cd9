--
--  estados de la demanda
--
DO $$
DECLARE
  new_version integer:= 32;
BEGIN
   IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */

    CREATE TABLE demandstatuses (
      code varchar not null primary key,
      label jsonb not null default '{}'::json,
      -- usado para conocer el orden lógico de los estados (para no mostrarlos desordenados)
      logicorder integer not null
    );

    insert into demandstatuses(
      code, logicorder, label
    ) values 
      ('active',1,'{"default":"activa", "es":"activa", "en":"active"}'), 
      ('historic',5, '{"default":"historic", "es":"histórico", "en":"historic" }')
    ;

    alter table demands 
      add column status_code varchar not null default 'active',
      add constraint demands_status_code_fk foreign key (status_code) references demandstatuses (code) on update restrict on delete restrict;

    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;