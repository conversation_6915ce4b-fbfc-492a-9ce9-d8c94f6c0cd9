--
--  add private notes to contact and offer
--
DO $$
DECLARE
  new_version integer:= 11;
BEGIN
   IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */
    ALTER TABLE public.contacts
      ADD notes varchar;
    ALTER TABLE offers
      ADD notes varchar;
    ALTER TABLE actions
      ADD notes varchar;
      
  
    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;


      
      