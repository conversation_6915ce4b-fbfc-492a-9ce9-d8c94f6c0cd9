--
--  Property structure definition
--
DO $$
DECLARE
new_version integer:=7;
BEGIN

  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */

      create table countries (
        code character varying not null,
        label jsonb not null,
        constraint countries_pkey primary key (code)
      );
      insert into public.countries(code, label) values ('ES', '{"default": "España", "en":"Spain", "es":"España", "ca":"Espanya", "fr":"Espagne"}');

        CREATE TABLE public.provinces (
            code character varying not null,
            country_code character varying not null,            
            label jsonb not null,
            primary key (code),
            foreign key (country_code) references countries(code)
                on update restrict
                on delete cascade
        );
        insert into provinces(
          country_code, 
          code, 
          label
        ) select 
          country_code, 
          (country_code || '_' || county_code) as code, 
          ('{"default":"' || min(county_name) || '", "es":"' || min(county_name) || '"}')::json as "label" 
        from 
          geonames 
        where 
          country_code='ES' and 
          county_code is not NULL 
          and not exists (select * from provinces where code= (country_code || '_' || county_code))
        group by country_code, county_code;

        CREATE TABLE cities (
            code varchar not null,
            province_code varchar not null,          
            label jsonb not null,  
            primary key (code),
            foreign key (province_code) references provinces(code) on delete cascade on update restrict
        );

        insert into cities(
          code, 
          province_code, 
          label
        ) select  
          (country_code || '_' || city_code) as code, 
          -- Montesquieu tiene 2 poblaciones dependientes de 2 provincias distintas, por lo que tenemos que hacer el min(..) que, menos mal, se corrsponde con la provincia de barcelona
          (country_code || '_' || min(county_code)) as province_code, 
          ('{"default":"' || min(city_name) || '", "es":"' || min(city_name) || '"}')::json as label 
         from 
          geonames 
         where 
          country_code='ES' 
          and county_code is not null 
          and city_name is not null 
           and not exists (select * from cities where code = (country_code || '_' || city_code))
         group by 
          country_code, 
          city_code;



        CREATE TABLE public.properties
        (
            -- identifier of the property
            id bigserial NOT NULL,
            -- agent that collected and manages the property 
            -- agent_id bigint NOT NULL,
            -- home, flat, ...
            type_code varchar not null,
            -- address:  province and country must be deduced from city.  city is mandatory.
            address_city_code varchar NOT NULL,
            address_line1 varchar,
            address_line2 varchar,           
            address_postcode varchar,
            -- referencia catastral
            "cadastralReference" varchar,
            -- Zone
            -- zone_id bigint,

            -- The set of attributes depending on type_code
            attributes json NOT NULL DEFAULT '{}'::json,
            --
            
            PRIMARY KEY (id),
            FOREIGN KEY (type_code)
                REFERENCES property_types (code) MATCH SIMPLE
                ON UPDATE RESTRICT
                ON DELETE CASCADE,
            FOREIGN KEY (address_city_code)   
                references cities(code) match SIMPLE
                on update RESTRICT
                on delete RESTRICT
        );       

    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */

  END IF;

END;
$$;