--
--  Renombrar tabla "offer_statuses" a "offerstatuses" (Es la tabla donde se enumeran todos los posibles estados de oferta, no la tabla que relaciona ofertas y estados)
--
DO $$
DECLARE
  new_version integer:= 30;
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
  /* BEGIN */
    ALTER TABLE public.offer_statuses RENAME TO offerstatuses;
    ALTER TABLE public.offerstatuses DROP CONSTRAINT offer_statuses_pkey;
    ALTER TABLE public.offerstatuses ADD CONSTRAINT offerstatuses_pkey PRIMARY KEY (code);
    
    ALTER TABLE public.offers ADD CONSTRAINT offers_status_code_fkey FOREIGN KEY (status_code)
              REFERENCES public.offerstatuses (code) MATCH SIMPLE
              ON UPDATE RESTRICT
              ON DELETE RESTRICT;

    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;