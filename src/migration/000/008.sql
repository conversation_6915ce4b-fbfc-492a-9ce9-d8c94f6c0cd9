--
--  Offer structure definition
--
DO $$
DECLARE
new_version integer:=8;
BEGIN

  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */

   
    CREATE TABLE currencies
    (
        code character varying NOT NULL,
        symbol character varying NOT NULL,
        PRIMARY KEY (code)
    );
    insert into public.currencies(code, symbol) values ('EUR','€'),('USD','$');
   


    CREATE TABLE public.offers
    (
      id bigserial NOT NULL,
      -- agent that manages the property 
      agent_id bigint NOT NULL,
      -- customer that offers the property
      customer_id bigint,
      -- Property (propiedad con la que se opera)
      property_id bigint NOT NULL,
      -- La oferta permite Venta?
      sale_allowed boolean NOT NULL default true,
      -- Cuantía de venta
      sale_amount numeric,
      -- La oferta permite alquiler?
      rent_allowed boolean NOT NULL default false,
      -- Cuantía del alquiler (alquiler mensual)
      rent_amount numeric,
      -- Moneda en la que se expresan las cuantías
      currency_code varchar,
      --
      created_at timestamp with time zone NOT NULL DEFAULT now(),
      updated_at timestamp with time zone NOT NULL DEFAULT now(),
      PRIMARY KEY (id),
      FOREIGN KEY (agent_id)
          REFERENCES agents (id) MATCH SIMPLE
          ON UPDATE RESTRICT
          ON DELETE CASCADE,
      FOREIGN KEY (customer_id)
          REFERENCES contacts (id)
          ON UPDATE RESTRICT
          ON DELETE SET NULL,
      -- Offer must be deleted first then, property must be deleted
      FOREIGN KEY (property_id)
          REFERENCES properties (id)
          ON UPDATE RESTRICT
          ON DELETE RESTRICT,
      FOREIGN KEY (currency_code)
          REFERENCES currencies (code) MATCH SIMPLE
          ON UPDATE RESTRICT
          ON DELETE RESTRICT

    );

    CREATE TRIGGER update_offers_updated_at BEFORE UPDATE ON offers FOR EACH ROW EXECUTE PROCEDURE  update_updated_at_column();
    
    COMMENT ON COLUMN public.offers.sale_amount
        IS 'Cuantía monetaria de la operamción (total) sin tener en cuenta datos internos como comisiones y demás.
    Se permite "null" pero en capa de negocio deberán imponerse restricciones adicionales según sale_allowed, el estado de la oferta
    .';

    /*
    COMMENT ON COLUMN public.offers.status_code
        IS '''prospecting'', ''active'', ''reserved'', ''signed'', ''out_of_market''

    Possible flows:

                        (create property)    --> ''prospecting''
    ''prospecting'' --- (cancel prospection) --> ''out_of_market'' 
    ''prospecting'' --- (start mandate )     --> ''active''
    ''active''      --- (renew mandate)      --> ''active''
    ''active''      --- (cancel mandate)     --> ''out_of_market''
    ''active''      --- (make reservation)   --> ''reserved''
    ''reserved''    --- (cancel reservation) --> ''active''
    ''reserved''    --- (sign contract)      --> ''signed'' 
    ''active''      --- (date exceed)        --> ''out_or_market'' 
    ' ;
*/
    

    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */

  END IF;

END;
$$;