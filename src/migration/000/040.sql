--
--  Clave alternativa para el arbol de contención de zonas.  Permite indexar por contenedor, contenido
--
DO $$
DECLARE
  new_version integer:= 40;
BEGIN
   IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */
    
    
    CREATE UNIQUE INDEX propertyzones_containers_altkey ON public.propertyzones_containers USING btree (
      container_id ASC NULLS LAST, 
      contained_id ASC NULLS LAST
    );

    COMMENT ON INDEX public.propertyzones_containers_altkey
      IS 'La PK es por contained, container. Esta clave Alternativa es por container,contained,  lo cual facilita algunas consultas (buscar items contenidos en otros)';

   /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;