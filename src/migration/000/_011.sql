--
--  User of the agent (wich user can loggin and act as the agent)
--
DO $$
DECLARE
new_version integer:=11;
BEGIN

  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */
    
    ALTER TABLE public.agents
      ADD COLUMN user_id bigint,  
      ADD FOREIGN KEY (user_id)
        REFERENCES public.users (id) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE SET NULL
        NOT VALID;

    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */

  END IF;

END;
$$;