--
--  Imagen favorita (representativa) de un inmueble
--
DO $$
DECLARE
  new_version integer:= 29;
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
  /* BEGIN */
    ALTER TABLE public.propertymedias
      ADD COLUMN "isFavourite" boolean NOT NULL DEFAULT False;
    
    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;