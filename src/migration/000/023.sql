--
--  Reordenamos los campos de la tabla "offers":
--
DO $$
DECLARE
  new_version integer:= 23;
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
  /* BEGIN */

        

      ALTER TABLE public.offers RENAME CONSTRAINT offers_pkey to offers_old_pkey;
      ALTER TABLE public.offers RENAME CONSTRAINT offers_agent_id_fkey TO offers_old_agent_id_fkey;
      ALTER TABLE public.offers RENAME CONSTRAINT offers_currency_code_fkey to offers_old_currency_code_fkey;
      ALTER TABLE public.offers RENAME CONSTRAINT offers_customer_id_fkey to offers_old_customer_id_fkeyton;
      ALTER TABLE public.offers RENAME CONSTRAINT offers_property_id_fkey to offers_old_property_id_fkey;
      ALTER TABLE public.offers RENAME CONSTRAINT offers_urgency_check to offers_old_urgency_check;
      ALTER TABLE public.offers ALTER COLUMN id DROP DEFAULT;    

      DROP TRIGGER offer_creation ON public.offers;    
      DROP TRIGGER offer_update ON public.offers;
      DROP TRIGGER update_offers_updated_at ON public.offers;

      DROP SEQUENCE public.offers_id_seq;

      ALTER TABLE public.actions DROP CONSTRAINT actions_offer_id_fkey;
      ALTER TABLE public.opportunities DROP CONSTRAINT opportunities_offer_id_fkey;





      ALTER TABLE public.offers
          RENAME TO offers_old;
          
          

      CREATE TABLE public.offers
      (
          id bigserial NOT NULL,
          agent_id bigint NOT NULL,
          customer_id bigint,
          property_id bigint NOT NULL,
          sale_allowed boolean NOT NULL DEFAULT true,
          sale_amount numeric,
          "sale_marketAmount" numeric,
          rent_allowed boolean NOT NULL DEFAULT false,
          rent_amount numeric,
          "rent_marketAmount" numeric,
          currency_code character varying COLLATE pg_catalog."default",
          status_code character varying COLLATE pg_catalog."default" NOT NULL DEFAULT 'news'::character varying,
          urgency integer,
          notes character varying COLLATE pg_catalog."default",
          created_at timestamp with time zone NOT NULL DEFAULT now(),
          updated_at timestamp with time zone NOT NULL DEFAULT now(),

          CONSTRAINT offers_pkey PRIMARY KEY (id),
          CONSTRAINT offers_agent_id_fkey FOREIGN KEY (agent_id)
              REFERENCES public.agents (id) MATCH SIMPLE
              ON UPDATE RESTRICT
              ON DELETE CASCADE,
          CONSTRAINT offers_currency_code_fkey FOREIGN KEY (currency_code)
              REFERENCES public.currencies (code) MATCH SIMPLE
              ON UPDATE RESTRICT
              ON DELETE RESTRICT,
          CONSTRAINT offers_customer_id_fkey FOREIGN KEY (customer_id)
              REFERENCES public.contacts (id) MATCH SIMPLE
              ON UPDATE RESTRICT
              ON DELETE SET NULL,
          CONSTRAINT offers_property_id_fkey FOREIGN KEY (property_id)
              REFERENCES public.properties (id) MATCH SIMPLE
              ON UPDATE RESTRICT
              ON DELETE RESTRICT,
          CONSTRAINT offers_urgency_check CHECK (urgency >= 0 AND urgency <= 3)
      )
      WITH (
          OIDS = FALSE
      )
      TABLESPACE pg_default;

      perform setval('public.offers_id_seq', 91, true);

      COMMENT ON COLUMN public.offers.sale_amount
          IS 'Cuantía monetaria de la operamción (total) sin tener en cuenta datos internos como comisiones y demás.
          Se permite "null" pero en capa de negocio deberán imponerse restricciones adicionales según sale_allowed, el estado de la oferta
          .';

      -- Trigger: offer_creation

      -- DROP TRIGGER offer_creation ON public.offers;

      CREATE TRIGGER offer_creation
          AFTER INSERT
          ON public.offers
          FOR EACH ROW
          EXECUTE PROCEDURE public.create_offer_opportunity();

      -- Trigger: offer_update

      -- DROP TRIGGER offer_update ON public.offers;

      CREATE TRIGGER offer_update
          AFTER UPDATE 
          ON public.offers
          FOR EACH ROW
          EXECUTE PROCEDURE public.update_offer_opportunity();

      -- Trigger: update_offers_updated_at

      -- DROP TRIGGER update_offers_updated_at ON public.offers;

      CREATE TRIGGER update_offers_updated_at
          BEFORE UPDATE 
          ON public.offers
          FOR EACH ROW
          EXECUTE PROCEDURE public.update_updated_at_column();    
          
      insert into offers select id,
          agent_id,
          customer_id,
          property_id,
          sale_allowed,
          sale_amount,
          "sale_marketAmount",
          rent_allowed,
          rent_amount,
          "rent_marketAmount",
          currency_code,
          status_code,
          urgency,
          notes,
          created_at,
          updated_at
      from offers_old;

      ALTER TABLE public.actions
          ADD CONSTRAINT actions_offer_id_fkey FOREIGN KEY (offer_id)
          REFERENCES public.offers (id) MATCH SIMPLE
          ON UPDATE RESTRICT
          ON DELETE CASCADE;
      ALTER TABLE public.opportunities
          ADD CONSTRAINT opportunities_offer_id_fkey FOREIGN KEY (offer_id)
          REFERENCES public.offers (id) MATCH SIMPLE
          ON UPDATE RESTRICT
          ON DELETE CASCADE;


      drop table offers_old;



    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;