--
--  Renombrar tabla "property_types" a "propertytypes" (Es la tabla donde se enumeran todos los posibles tipos de inmueble, no la tabla que relaciona inmuebles y tipos)
--
DO $$
DECLARE
  new_version integer:= 31;
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
  /* BEGIN */
    
    ALTER TABLE public.properties DROP CONSTRAINT properties_type_code_fkey;
    
    ALTER TABLE public.property_types RENAME TO propertytypes;
    ALTER TABLE public.propertytypes DROP CONSTRAINT property_types_pkey CASCADE;
    ALTER TABLE public.propertytypes ADD CONSTRAINT propertytypes_pkey PRIMARY KEY (code);
        
    ALTER TABLE public.properties ADD CONSTRAINT properties_type_code_fkey FOREIGN KEY (type_code)
        REFERENCES public.propertytypes (code) MATCH SIMPLE
        ON UPDATE RESTRICT
        ON DELETE RESTRICT;

    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;