--
--  Unificamos en la tabla "opportunities" los distintos campos de búsqueda (venta/alquiler, precio, tipo inmueble, estado oferta/demanda)
--
DO $$
DECLARE
  new_version integer:= 34;
BEGIN
   IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */
   
    ALTER TABLE public.opportunities
      ADD COLUMN rent_allowed boolean NOT NULL DEFAULT false;

      update opportunities set rent_allowed = 
        exists (select * from offers where rent_allowed and offers.id=opportunities.offer_id) 
        or exists (select * from demands where rent_allowed and demands.id=opportunities.demand_id);
      
    ALTER TABLE public.opportunities
      ADD COLUMN rent_amount numeric;
      
      update opportunities set rent_amount = coalesce(
        ( select rent_amount from offers where id=opportunities.offer_id),
        ( select rent_amount from demands where id=opportunities.demand_id)
      );
      
    ALTER TABLE public.opportunities
      ADD COLUMN sale_allowed boolean NOT NULL DEFAULT false;

      update opportunities set sale_allowed = 
        exists (select * from offers where sale_allowed and offers.id=opportunities.offer_id) 
        or exists (select * from demands where sale_allowed and demands.id=opportunities.demand_id);
      
    ALTER TABLE public.opportunities
      ADD COLUMN sale_amount numeric;
      
      update opportunities set sale_amount = coalesce(
        ( select sale_amount from offers where id=opportunities.offer_id),
        ( select sale_amount from demands where id=opportunities.demand_id)
      );

    ALTER TABLE Public.opportunities
      ADD COLUMN offer_status_code varchar,
      ADD COLUMN demand_status_code varchar,
      ADD CONSTRAINT opportunities_offer_status_code_fk FOREIGN KEY (offer_status_code) REFERENCES offerstatuses(code) on delete restrict on update cascade,
      ADD CONSTRAINT opportunities_demand_status_code_fk FOREIGN KEY (demand_status_code) REFERENCES demandstatuses(code) on delete restrict on update cascade;

      update opportunities set 
        offer_status_code = (select status_code from offers where offers.id=opportunities.offer_id),
        demand_status_code = (select status_code from demands where demands.id=opportunities.demand_id);   
          
    ALTER TABLE public.opportunities
      ADD COLUMN property_type_code character varying,
      ADD CONSTRAINT opportunities_property_type_code_fk FOREIGN KEY (property_type_code) REFERENCES propertytypes(code) on delete restrict on update cascade;

      update opportunities set 
        property_type_code = coalesce(
          (select properties.type_code from offers inner join properties on offers.property_id=properties.id where offers.id=opportunities.offer_id),
          (select properties.type_code from demands inner join properties on demands.property_id=properties.id where demands.id=opportunities.demand_id)
        );

    create or replace FUNCTION public.create_offer_opportunity() RETURNS trigger
      LANGUAGE 'plpgsql'
      COST 100
      VOLATILE NOT LEAKPROOF
    AS $BODY$
      DECLARE
        v_property_type_code  varchar;
      BEGIN
        SELECT properties.type_code into strict v_property_type_code from properties where properties.id=NEW.property_id;
        INSERT INTO opportunities (
          agent_id, offer_id, created_at, updated_at, rent_allowed, rent_amount, sale_allowed, sale_ammount, offer_status_code, property_type_code
        ) VALUES (
          NEW.agent_id, NEW.id, NEW.created_at, NEW.updated_at, NEW.rent_allowed, NEW.rent_amount, NEW.sale_allowed, NEW.sale_ammount, NEW.status_code, v_property_type_code
        );
        RETURN NEW;
      END;
    $BODY$;
    
    CREATE or replace FUNCTION public.create_demand_opportunity()
        RETURNS trigger
        LANGUAGE 'plpgsql'
        COST 100
        VOLATILE NOT LEAKPROOF
    AS $BODY$
    DECLARE
        v_property_type_code varchar;
    BEGIN
      SELECT properties.type_code into strict v_property_type_code from properties where properties.id=NEW.property_id;
      INSERT INTO opportunities (
        agent_id, demand_id, created_at, updated_at, rent_allowed, rent_amount, sale_allowed, sale_ammount, demand_status_code, property_type_code
      ) VALUES (
        NEW.agent_id, NEW.id, NEW.created_at, NEW.updated_at, NEW.rent_allowed, NEW.rent_amount, NEW.sale_allowed, NEW.sale_ammount, NEW.status_code, v_property_type_code
      );
      RETURN NEW;
    END;
    $BODY$;
      
    CREATE or REPLACE FUNCTION public.update_demand_opportunity()
        RETURNS trigger
        LANGUAGE 'plpgsql'
        COST 100
        VOLATILE NOT LEAKPROOF
    AS $BODY$
    DECLARE
      v_property_type_code character varying;
    BEGIN
      SELECT properties.type_code into strict v_property_type_code 
        from properties 
        where properties.id=NEW.property_id;
        
      UPDATE
        opportunities
      SET
        updated_at = NEW.updated_at,
        sale_allowed = NEW.sale_allowed,
        sale_amount = NEW.sale_amount,
        rent_allowed = NEW.rent_allowed,
        rent_amount = NEW.rent_amount,
        demand_status_code = NEW.status_code,
        property_type_code = v_property_type_code
      WHERE
        opportunities.demand_id = NEW.id;
      RETURN NEW;
    END;
    $BODY$;

    CREATE or REPLACE FUNCTION public.update_offer_opportunity()
        RETURNS trigger
        LANGUAGE 'plpgsql'
        COST 100
        VOLATILE NOT LEAKPROOF
    AS $BODY$
    DECLARE
      v_property_type_code character varying;
    BEGIN
      SELECT properties.type_code into strict v_property_type_code 
        from properties 
        where properties.id=NEW.property_id;
        
      UPDATE
        opportunities
      SET
        updated_at = NEW.updated_at,
        sale_allowed = NEW.sale_allowed,
        sale_amount = NEW.sale_amount,
        rent_allowed = NEW.rent_allowed,
        rent_amount = NEW.rent_amount,
        offer_status_code = NEW.status_code,
        property_type_code = v_property_type_code
      WHERE
        opportunities.offer_id = NEW.id;
      RETURN NEW;
    END;
    $BODY$;
    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;