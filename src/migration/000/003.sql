--
--  Agents and their contacts
--
DO $$
DECLARE
new_version integer:=3;
BEGIN

  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */

    /* DROP TABLE agents */
    CREATE TABLE public.agents
    (
        id bigserial NOT NULL,

        firstname VA<PERSON>HAR NOT NULL,
        lastname <PERSON><PERSON><PERSON><PERSON>,
        
        email VARCHAR NOT NULL,
        password VARCHAR NOT NULL,

        mobile VARCHAR,

        created_at timestamp with time zone NOT NULL DEFAULT now(),
        updated_at timestamp without time zone NOT NULL DEFAULT now(),
        
        PRIMARY KEY (id),
        UNIQUE (email)

    );
    CREATE TRIGGER update_agents_updated_at BEFORE UPDATE ON agents FOR EACH ROW EXECUTE PROCEDURE  update_updated_at_column();

    CREATE TABLE public.sessions
    (
        token character varying NOT NULL,
        agent_id bigint NOT NULL,
        created_at timestamp with time zone NOT NULL DEFAULT now(),
        updated_at timestamp with time zone NOT NULL DEFAULT now(),
        expires_at timestamp with time zone NOT NULL default (now()+'1 month'),
        <PERSON><PERSON>ARY KEY (token),
        <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (agent_id)
            REFERENCES agents (id)
            ON UPDATE RESTRICT
            ON DELETE CASCADE
    );
    
    /** Actualizar fecha expiración */
    CREATE OR REPLACE FUNCTION update_session_expiration(token character varying) RETURNS VOID AS $func$
        BEGIN
          update sessions set expires_at = (now()+'1 month') where expires_at.token = token;
        END;
        $func$ language 'plpgsql';
    
    
    CREATE TRIGGER update_sessions_updated_at BEFORE UPDATE ON sessions FOR EACH ROW EXECUTE PROCEDURE  update_updated_at_column();
    -- Table: public.contacts

    -- DROP TABLE public.contacts;

    CREATE TABLE public.contacts
    (
        id bigserial NOT NULL,
        agent_id bigint NOT NULL,
        
        firstname VARCHAR NOT NULL,
        lastname VARCHAR,
        
        email VARCHAR,
        mobile VARCHAR,
                
        created_at timestamp with time zone NOT NULL DEFAULT now(),
        updated_at timestamp with time zone NOT NULL DEFAULT now(),
        PRIMARY KEY (id),
        FOREIGN KEY (agent_id)
            REFERENCES agents (id) MATCH SIMPLE
            ON UPDATE RESTRICT
            ON DELETE CASCADE
            NOT VALID
    );

    CREATE TRIGGER update_contacts_updated_at BEFORE UPDATE ON contacts FOR EACH ROW EXECUTE PROCEDURE  update_updated_at_column();
    COMMENT ON TABLE public.contacts
        IS 'People that is contact of the agent... it is a private "contacts" book.
    If a contact matches with an external agent or customer, then this can be "linked" to... must be analyzed';


    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */

  END IF;

END;
$$
