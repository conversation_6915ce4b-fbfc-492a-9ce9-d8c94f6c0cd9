--
--  add private notes to contact and offer
--
DO $$
DECLARE
  new_version integer:= 16;
BEGIN
   IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */

ALTER TABLE public.properties
  add column "attributes_totalSurface_m2" numeric not null default 0,
  add column "attributes_usefulSurface_m2" numeric,
  add column "attributes_solarSurface_m2" numeric,
  add column "attributes_construction_year" integer,
  add column "attributes_status_code" varchar check ( "attributes_status_code" in ('new','second_hand')),
  add column "attributes_conservationStatus_code" varchar check("attributes_conservationStatus_code" in ('mint','good','reformed','to_reform')),
  add column "attributes_bedrooms_individual_count" integer check ("attributes_bedrooms_individual_count" >= 0),
  add column "attributes_bedrooms_double_count" integer check ("attributes_bedrooms_double_count" >= 0),
  add column "attributes_bedrooms_suite_count" integer check ("attributes_bedrooms_suite_count" >= 0),
  add column "attributes_bedrooms_total_count" bigint, -- Campo "computado", se recalcula al insertar o modificar un registro
  add column "attributes_bathrooms_count" integer check ("attributes_bathrooms_count" >= 0),
  add column "attributes_bathrooms_notes" varchar,
  add column "attributes_toilets_count" integer check ("attributes_toilets_count" >= 0),
  add column "attributes_toilets_notes" varchar,
  add column "attributes_buddle_has" boolean,
  add column "attributes_kitchen_has" boolean,
  add column "attributes_kitchen_notes" varchar,
  add column "attributes_dinningRoom_has" boolean,
  add column "attributes_dinningRoom_notes" varchar,
  add column "attributes_storageRoom_has" boolean,
  add column "attributes_balcony_has" boolean,
  add column "attributes_balcony_notes" varchar,
  add column "attributes_terrace_has" boolean,
  add column "attributes_buildInCabinets_count" integer check ("attributes_buildInCabinets_count" >= 0),
  add column "attributes_doubleGlasses_has" boolean,
  add column "attributes_externalJoinery_code" varchar check("attributes_externalJoinery_code" in ('aluminium', 'wood', 'pvc', 'other' )),
  add column "attributes_externalJoinery_notes" varchar,
  add column "attributes_ground_codes" varchar[] check("attributes_ground_codes" <@ Array['gres','marble','carpet','parquet','laminatedFlooring','solidFlooring','terrazzo']::varchar[]),
  add column "attributes_waterSupply_has" boolean,
  add column "attributes_waterSupply_notes" varchar,
  add column "attributes_powerSupply_has" boolean,
  add column "attributes_powerSupply_notes" varchar,
  add column "attributes_gasSupply_has" boolean,
  add column "attributes_gasSupply_notes" varchar,
  add column "attributes_airConditioning_code" varchar check("attributes_airConditioning_code" in ('none', 'cold', 'coldAndHeat' )),
  add column "attributes_heating_code" varchar check("attributes_heating_code" in ('none', 'central', 'electric','naturalGas','gasoil' )),
  add column "attributes_fireplace_has" boolean,
  add column "attributes_intercom_has" boolean,
  add column "attributes_intercom_notes" varchar,
  add column "attributes_reinforcedDoor_has" boolean,
  add column "attributes_reinforcedDoor_notes" varchar,
  add column "attributes_alarmSystem_has" boolean,
  add column "attributes_elevator_has" boolean,
  add column "attributes_handicappedAccessible_is" boolean,
  add column "attributes_furnished_is" boolean,
  add column "attributes_garden_code" varchar check("attributes_garden_code" in ('none', 'own', 'community')),
  add column "attributes_outsideArea_code" varchar check("attributes_outsideArea_code" in ('none', 'own', 'community')),
  add column "attributes_outsideArea_notes" varchar,
  add column "attributes_swimmingPool_has" boolean,
  add column "attributes_parkingPlaces_count" integer check ("attributes_parkingPlaces_count" >= 0),
  add column "attributes_facade_codes" varchar[] check("attributes_facade_codes" <@ Array['interior','exterior']::varchar[]),
  add column "attributes_orientation_codes" varchar[] check("attributes_orientation_codes" <@ Array['north', 'northeast', 'east', 'southeast', 'south', 'southwest', 'west', 'northwest']::varchar[]), 
  add column "attributes_sunny_is" boolean,
  add column "attributes_sunny_notes" varchar,
  add column "attributes_communityFees_amount" decimal  check ("attributes_communityFees_amount" >= 0),
  add column "attributes_neighborsPerFloor_count" integer check ("attributes_neighborsPerFloor_count" >= 0),
  add column "attributes_buildingFloors_count" integer check ("attributes_buildingFloors_count" >= 0),
  add column "attributes_floor_code" varchar check ("attributes_floor_code" in ('basement','semibasement','ground','mezzanine','main','1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17','18','19','20','penthouse')),
  add column "attributes_energyCertificate_code" varchar check ("attributes_energyCertificate_code" in ('available','inProcess','exempt')),
  add column "attributes_consumptionLevel_code" varchar check ("attributes_consumptionLevel_code" in ('A','B','C','D','E','F','G')),
  add column "attributes_emissionLevel_code" varchar check ("attributes_emissionLevel_code" in ('A','B','C','D','E','F','G'))
  ;
  
  CREATE FUNCTION public.update_computed_property_fields()
    RETURNS trigger
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE NOT LEAKPROOF
  AS $BODY$
  BEGIN
     NEW.attributes_bedrooms_total_count = coalesce(NEW.attributes_bedrooms_individual_count,0) + coalesce(NEW.attributes_bedrooms_double_count,0)  + coalesce(NEW.attributes_bedrooms_suite_count,0);
        
     RETURN NEW;
  END;
  $BODY$;

   
  CREATE TRIGGER update_computed_property_fields BEFORE INSERT OR UPDATE ON public.properties FOR EACH ROW EXECUTE PROCEDURE public.update_computed_property_fields();
    
    
    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;


      
      