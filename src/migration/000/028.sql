--
--  
--
DO $$
DECLARE
  new_version integer:= 28;
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
  /* BEGIN */
    drop table actions;
    drop table action_types;
    
    CREATE TABLE actiontypes
    (
      id BIGSERIAL NOT NULL,
      label jsonb NOT NULL DEFAULT '{}'::jsonb,
      CONSTRAINT action_types_pkey PRIMARY KEY (id)
    );

    INSERT INTO actiontypes (label) 
      values 
        ('{"ca": "Visita", "en": "Visit", "es": "Visita", "default": "Visit"}'),
        ('{"ca": "Trucada telefónica", "en": "Phonen call", "es": "Llamada telefònica", "default": "Phone call"}'),
        ('{"ca": "Reunió", "en": "Meeting", "es": "Reunión", "default": "Meeting"}'),        
        ('{"ca": "Valoració", "en": "Assessment", "es": "Valoración", "default": "Assessment"}'),
        ('{"ca": "Visita de captació", "en": "Catchment visit", "es": "Visita de captación", "default": "Catchment visit"}'),
        ('{"ca": "Captació", "en": "Catchment", "es": "Captación", "default": "Catchment"}'),
        ('{"ca": "Reserva", "en": "Reservation", "es": "Reserva", "default": "Reservation"}'),
        ('{"ca": "Signar arres", "en": "Sign deposit", "es": "Firmar arras", "default": "Sign deposit"}'),
        ('{"ca": "Signar escriptures", "en": "Sign deeds", "es": "Firmar escrituras", "default": "Sign deeds"}');      


   
    CREATE TABLE actions
    (
        "id" bigserial NOT NULL,
        "agent_id" bigint NOT NULL,
        "type_id" bigint NOT NULL,
        "when" timestamp with time zone NOT NULL,
        "priority" integer NOT NULL DEFAULT 2,
        "offer_id" bigint,
        "contact_id" bigint,        
        "done" boolean not null default false,
        "description" varchar,
        "created_at" timestamp with time zone NOT NULL DEFAULT now(),
        "updated_at" timestamp with time zone NOT NULL DEFAULT now(),
        CONSTRAINT actions_pkey PRIMARY KEY (id),
        CONSTRAINT actions_agent_id_fkey FOREIGN KEY (agent_id)
            REFERENCES agents (id) MATCH SIMPLE
            ON UPDATE RESTRICT
            ON DELETE CASCADE,
        CONSTRAINT actions_offer_id_fkey FOREIGN KEY (offer_id)
            REFERENCES offers (id) MATCH SIMPLE
            ON UPDATE RESTRICT
            ON DELETE CASCADE,
        CONSTRAINT actions_contact_id_fkey FOREIGN KEY (contact_id)
            REFERENCES contacts (id) MATCH SIMPLE
            ON UPDATE RESTRICT
            ON DELETE CASCADE,
        CONSTRAINT actions_type_id_fkey FOREIGN KEY (type_id)
            REFERENCES actiontypes (id) MATCH SIMPLE
            ON UPDATE RESTRICT
            ON DELETE CASCADE,
        CONSTRAINT actions_priority_check CHECK (priority >= 0 AND priority <= 3)
    );
    
    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;