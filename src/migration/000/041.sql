--
--  La oferta tiene un principio y final de mandato
--
DO $$
DECLARE
  new_version integer:= 41;
BEGIN
   IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */

    ALTER TABLE public.offers
        ADD COLUMN mandate_start timestamp with time zone;

    ALTER TABLE public.offers
        ADD COLUMN mandate_end timestamp with time zone;


   /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;