--
--  add private notes to contact and offer
--
DO $$
DECLARE
  new_version integer:= 17;
BEGIN
   IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */
    ALTER TABLE public.properties
      DROP COLUMN "attributes_m2",
      DROP COLUMN "attributes_constructionYear",
      DROP COLUMN "attributes_realFloor",
      DROP COLUMN "attributes_nRooms",
      DROP COLUMN "attributes_nBathrooms",
      DROP COLUMN "attributes_nToilets",
    
      DROP COLUMN "attributes_hasKitchen" ,
      DROP COLUMN "attributes_hasDiningRoom" ,
      DROP COLUMN "attributes_hasStorageRoom" ,
      DROP COLUMN "attributes_hasGarage" ,
      DROP COLUMN "attributes_hasLaundry" ,

      DROP COLUMN "attributes_hasBalcony" ,
      DROP COLUMN "attributes_hasTerrace" ,
      DROP COLUMN "attributes_hasGarden" ,
      DROP COLUMN "attributes_hasPool" ,

      DROP COLUMN "attributes_hasElevator" ,
      DROP COLUMN "attributes_hasHeating" ,
      DROP COLUMN "attributes_hasAirConditioner" ,
      DROP COLUMN "attributes_hasGas" ,
      DROP COLUMN "attributes_hasFurniture" ,

      DROP COLUMN "attributes_isSunny" ,
      DROP COLUMN "attributes_isExterior" ;
       
  
    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;


      
      