--
--  property_types (per domain)
--
DO $$
DECLARE
new_version integer:=4;
BEGIN

  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */

    CREATE TABLE public.property_types
    (
        code character varying NOT NULL,
        label jsonb NOT NULL,      
        fieldsdef jsonb NOT NULL DEFAULT '[]',
        PRIMARY KEY (code)
    )
    WITH (
        OIDS = FALSE
    );

    
    COMMENT ON COLUMN public.property_types.fieldsdef
        IS 'Fields deffinition.  It is a JSON array  [ {def1}, {def2}, ... ]';


    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */

  END IF;

END;
$$



