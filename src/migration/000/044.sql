--
--  T<PERSON><PERSON> de mandatos (para ofertas).
--  En la oferta es obligatorio indicar un mandato si se está en estado "comercialización"
--
DO $$
DECLARE
new_version integer:=44;
BEGIN

  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
   
    CREATE TABLE offermandatetypes (
      code character varying NOT NULL,
      label jsonb NOT NULL DEFAULT '{}'::json,
      logicorder integer NOT NULL,
      CONSTRAINT offermandatetypes_pkey PRIMARY KEY (code)
    );
    
    insert into offermandatetypes(code, label, logicorder) values 
      ('exclusive', '{"default":"exclusive","es":"exclusiva"}',10),
      ('open', '{"default":"open", "es":"abierto"}', 20),
      ('verbal', '{"default":"verbal", "es":"verbal"}', 30),
      ('flat_rate','{"default":"flat rate", "es": "tarifa plana"}', 40),
      ('other', '{"default":"other", "es":"otro"}', 50)
    ;
    
    ALTER TABLE offers
      ADD COLUMN mandate_type_code character varying;
    
    UPDATE offers set mandate_type_code='other' where status_code='commercialization';

    ALTER TABLE offers
      ADD CONSTRAINT offers_mandate_type_code_fk FOREIGN KEY (mandate_type_code)
          REFERENCES offermandatetypes (code) MATCH SIMPLE
          ON UPDATE CASCADE
          ON DELETE SET NULL,
      ADD CONSTRAINT "offers_mandate_type_code_check" CHECK (
        (mandate_type_code is not null and status_code='commercialization') or (status_code<>'commercialization')
      )
    ;

    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */

  END IF;

END;
$$;
