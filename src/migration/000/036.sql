--
--  La moneda usada en la oferta debe ser obligatoria
--
DO $$
DECLARE
  new_version integer:= 36;
BEGIN
   IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */
    UPDATE OFFERS SET currency_code='EUR' WHERE currency_code is null;
    ALTER TABLE public.offers
      ALTER COLUMN currency_code SET default 'EUR',
      ALTER COLUMN currency_code SET NOT NULL ;

    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$$;