--
-- Acciones para "llamadas perdidas"
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 157;
BEGIN
  IF (
    SELECT
      max(version)
    FROM
      public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
    
    -- Nuevos tipos de acción
    INSERT INTO actiontypes (id, label, logicorder, service_code, "offerIsRequired", "contactIsRequired", "agentCanCreate")
      VALUES 
        ('MR33k3i1U373C8KKHLf7Vw', '{"es": "Registro visita servicer", "default": "Servicer visit record"}', 1000002, NULL, TRUE, TRUE, TRUE)  ;

    /* END */
    INSERT INTO public.schema_changes (version, script)
      VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;