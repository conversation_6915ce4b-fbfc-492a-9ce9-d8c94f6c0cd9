--
-- Provedores de publicación
--   Configuración de API no debe diferenciar entre devel/production... la propia BBDD es de uno de los contextos concretos
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 131;
BEGIN
  IF (
    SELECT
      max(version)
    FROM
      public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */

    UPDATE
      tasks_publications_providers
    SET
      api_conf = '{
          "apiKey":"2a6e3c22f5e04db8823402fe6a489e2f89879bcf5f6346deaadcea1ef75c933b",
          "baseUrl": "https://api.inmofactory.com/api"
      }'::json
    WHERE
      code = 'inmofactory';

    UPDATE
      tasks_publications_providers
    SET
      api_conf = '{
          "agencyId": "SA3813",
          "key": "vJhUbQGWNRAthXh2FFHMpoh0IdWGCqIZ",
          "baseUrl": "https://gateway.habitatsoft.com/api"
      }'::json
    WHERE
      code = 'pisoscom';

    /* END */
    INSERT INTO public.schema_changes (version, script)
      VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;