--
-- Provedores de publicación
--   Qué grupos de trabao alimenta cada publicación
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 127;
BEGIN
  IF ( SELECT max(version) FROM schema_changes) = (new_version - 1) THEN
    /* BEGIN */
    CREATE TABLE public.tasks_publications_workgroupproviders
    (
        workgroup_id uuid NOT NULL,
        provider_code character varying NOT NULL,
        
        PRIMARY KEY (workgroup_id, provider_code)
    );

    COMMENT ON TABLE tasks_publications_workgroupproviders
      IS 'Which workgroups feed each provider  (i.e.:  offers shared in any of the workgroups will be publised in the associated publications providers)';

    insert into tasks_publications_workgroupproviders 
      (workgroup_id, provider_code) 
    values 
      -- Fotocasa
      ('554f2ac6-5fb9-11eb-859f-6be55d83bfbc', 'inmofactory'),
      -- Ha<PERSON>aclia
      ('7b367802-5fb9-11eb-859f-6be55d83bfbc', 'inmofactory'),
      -- Pisos.com
      ('acaea93e-7b63-11eb-a6bc-06d99be0472b', 'pisoscom');

    /* END */
    INSERT INTO public.schema_changes (version, script)
      VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;