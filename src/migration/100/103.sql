--
-- Vista de una Oferta utilizada por el subsistema de mailing
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 103;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
    
    create or replace view mailing_offers_vw as
      select 
        o.id as 
          id,
        agent."firstName" as
          "agent_firstName",
        agent."lastName" as 
          "agent_lastName",
        agent."email" as
          "agent_email",
        customer."firstName" as
          "customer_firstName",
        customer."lastName" as
          "customer_lastName",
        customer.email as
          customer_email,
        customer.mobile as
          customer_mobile,
        property.type_code as 
          property_type_code,
        property.address_city_code as
          property_address_city_code,
        property_address_city.label->>'default' as
          property_address_city_name,
        property_address_city_province.code as
          property_address_city_province_code,
        property_address_city_province.label->>'default' as
          property_address_city_province_name,
        property.address_streettype_id as
          property_address_streettype_id,
        property_address_streettype.label->>'default' as
          property_address_streettype_name,
        property.address_streetname as
          property_address_streetname,
        property.address_number as
          property_address_number,
        property."attributes_totalSurfaceM2" as
          "property_attributes_totalSurfaceM2",  
        o.rent_amount as
          rent_amount,
        o.sale_amount as
          sale_amount    
      from 
        offers o
        inner join agents 
          agent on o.agent_id=agent.id
        left join contacts 
          customer on o.customer_id=customer.id
        inner join properties 
          property on o.property_id = property.id
        inner join cities 
          property_address_city on property.address_city_code = property_address_city.code
        inner join provinces 
          property_address_city_province on property_address_city.province_code = property_address_city_province.code
        left join streettypes 
          property_address_streettype on property.address_streettype_id = property_address_streettype.id;

      grant all on table public.mailing_offers_vw TO app_user;
    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;
