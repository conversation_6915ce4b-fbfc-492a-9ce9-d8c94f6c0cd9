-- La clave primaria de contact_tag_events causa duplicados para eventos disparados en el mismo milisegundo...
-- Debe crearse una clave primaria única (que, de paso, ofrezca secuencialidad temporal)
DO $SCHEMAPROC$
DECLARE
  new_version integer := 144;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */

   -- Table: public.contact_tag_events
    DROP TABLE public.contact_tag_events;

    CREATE TABLE public.contact_tag_events
    (
        id bigserial NOT NULL,
		    contact_id bigint NOT NULL,
        tag_id integer NOT NULL,
        at timestamp with time zone NOT NULL DEFAULT now(),
        type_code smallint NOT NULL,
        by_id character varying not null,        
        CONSTRAINT contact_tag_events_pkey PRIMARY KEY (id),
        foreign key (by_id) references scrty_users(id) on update restrict on delete cascade
    );
    GRANT ALL ON TABLE public.contact_tag_events TO app_user;

    COMMENT ON TABLE public.contact_tag_events
        IS 'Historial de creación/borrado de un tag de un contacto';

    COMMENT ON COLUMN public.contact_tag_events.at
        IS 'Instante del evento';

    COMMENT ON COLUMN public.contact_tag_events.type_code
        IS 'tipo del evento: 1=cr, 2=dl';

    COMMENT ON COLUMN public.contact_tag_events.by_id
        IS 'Usuario (scrty_user) que ha realizado la operación';
    -- Index: contact_tag_events_by_:contact_tag_at_idx

    -- DROP INDEX IF EXISTS public."contact_tag_events_by_:contact_tag_at_idx";

    CREATE INDEX "contact_tag_events_by_contact_tag_at_idx"
        ON public.contact_tag_events USING btree
        (contact_id ASC NULLS LAST, tag_id ASC NULLS LAST, at ASC NULLS LAST, id ASC NULLS LAST)
        TABLESPACE pg_default;
    
    CREATE INDEX "contact_tag_events_by_contact_at_idx"
        ON public.contact_tag_events USING btree
        (contact_id ASC NULLS LAST, at ASC NULLS LAST, id ASC NULLS LAST)
        TABLESPACE pg_default;


    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;