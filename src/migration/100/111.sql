--
-- <PERSON>ñadida URL de imagen favoríta del inmueble (versión thumbnail y versión para "publicar")
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 111;
BEGIN
  IF (
    SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
    
    DROP VIEW public.mailing_offers_vw;

    CREATE OR REPLACE VIEW public.mailing_offers_vw  AS
      SELECT o.id,
        agent."firstName" AS "agent_firstName",
        agent."lastName" AS "agent_lastName",
        agent.email AS agent_email,
        customer."firstName" AS "customer_firstName",
        customer."lastName" AS "customer_lastName",
        customer.email AS customer_email,
        customer.mobile AS customer_mobile,
        property.type_code AS property_type_code,
        COALESCE(property_type.label ->> 'es'::text, property_type.label ->> 'default'::text) AS property_type_name,
        property.address_city_code AS property_address_city_code,
        property_address_city.label ->> 'default'::text AS property_address_city_name,
        property_address_city_province.code AS property_address_city_province_code,
        property_address_city_province.label ->> 'default'::text AS property_address_city_province_name,
        property.address_streettype_id AS property_address_streettype_id,
        COALESCE(property_address_streettype.label ->> 'es'::text, property_address_streettype.label ->> 'default'::text) AS property_address_streettype_name,
        property.address_streetname AS property_address_streetname,
        property.address_number AS property_address_number,
        property."attributes_totalSurfaceM2" AS "property_attributes_totalSurfaceM2",
        (select max(publishing_url) from medias m inner join propertymedias pm on m.key=pm.media_key and  pm.property_id=property.id and pm."isFavourite") as property_favouritepicture_publishing_url,
        (select max(thumbnail_url) from medias m inner join propertymedias pm on m.key=pm.media_key and  pm.property_id=property.id and pm."isFavourite") as property_favouritepicture_thumbnail_url,
        o.rent_amount,
        o.sale_amount
      FROM offers o
        JOIN agents agent ON o.agent_id = agent.id
        LEFT JOIN contacts customer ON o.customer_id = customer.id
        JOIN properties property ON o.property_id = property.id
        JOIN propertytypes property_type ON property.type_code::text = property_type.code::text
        JOIN cities property_address_city ON property.address_city_code::text = property_address_city.code::text
        JOIN provinces property_address_city_province ON property_address_city.province_code::text = property_address_city_province.code::text
        LEFT JOIN streettypes property_address_streettype ON property.address_streettype_id = property_address_streettype.id;
        
    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;

