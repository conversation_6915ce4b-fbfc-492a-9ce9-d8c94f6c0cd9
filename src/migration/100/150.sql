--
-- Acción "solicitar hipoteca":  texto mucho más corto, ya no hay porcentajes ni nada
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 150;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
    update services set 
      label='{"default": "Solicitud de hipoteca (Tu Solución Hipotecaria)"}', 
      "disclaimer_title"='{"default": "Solicita un estudio gratuito y personalizado."}',
      "disclaimer_detailHtml"='{"default": "<p>Te contactaremos para conocer tu caso y proponerte la mejor solución financiera para la compra de tu casa.</p><p>Nuestro estudio es gratuito y sin compromiso.</p><p>Haz clic en el Avión para empezar.</p>"}' 
    where 
      code = 'tu_solucion_hipotecaria.mortagerequest';
    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;