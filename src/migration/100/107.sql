--
-- Cuando un media proviene de una URL conocida (URL de la que descargamos el fichero original), almacenaremos dicha URL
-- ¿Para qué?
--   Cuando sincronizamos ofertas que vienen de idealista, no volveremos a descargar imágenes cuya URL es conocida.
DO $SCHEMAPROC$
DECLARE
  new_version integer := 107;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
    ALTER TABLE medias
      ADD source_url character varying;
    
    COMMENT ON COLUMN medias.source_url
      IS 'Url origen de la que se ha extraido el media';

    CREATE INDEX medias_source_url_idx
      ON public.medias USING btree
        (source_url ASC NULLS LAST);

    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;
