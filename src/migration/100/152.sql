
--
--  Agentes de la nueva red de Activos Bancarios
--  Les han puesto un email de @topbrokers.io
--  
--  
DO $$
DECLARE
new_version integer:=152;
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN    

    insert into workgroup_members (workgroup_id, agent_id, can_publish, can_read) 
    select 'f2fb4715-970a-04ee-c1da-7a4b269da74b' as group_id, 
    id as agent_id, 
    false as can_write, 
    true as can_read 
    from agents where id>500 and id<99999 and email like '%@topbrokers.io';
    --from agents where id>1 and id<99999 and email like '%@arilla.es';
    

    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */

  END IF;

END;
$$;
