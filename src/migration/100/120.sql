--
-- Nuevo subsistema tasks_publications 
-- Migración de tasks_inmofactory_*  a tasks_publications_*
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 120;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
    create table tasks_publications_publishers (
      code character varying not null,
      name character varying not null,
      PRIMARY KEY (code)
    );
    insert into tasks_publications_publishers(code,name) values ('inmofactory', 'Inmofactory (fotocasa, habitaclia)'), ('pisoscom','pisos.com');
    
    alter table tasks_inmofactory_offerlog RENAME TO tasks_publications_offerlog;
    
    alter table tasks_publications_offerlog add column publisher_code character varying;
    update tasks_publications_offerlog set publisher_code='inmofactory' ;
    alter table tasks_publications_offerlog alter column publisher_code set not null;
    alter table tasks_publications_offerlog add constraint tasks_publications_offerlog_publisher_code_fk foreign key (publisher_code) references tasks_publications_publishers(code) on update restrict on delete cascade;

    alter table tasks_inmofactory_publishedoffers rename to tasks_publications_publishedoffers;
    alter table tasks_publications_publishedoffers add column publisher_code character varying;
    update tasks_publications_publishedoffers set publisher_code='inmofactory' ;
    alter table tasks_publications_publishedoffers alter column publisher_code set not null;
    alter table tasks_publications_publishedoffers add constraint tasks_publications_publishedoffers_publisher_code_fk foreign key (publisher_code) references tasks_publications_publishers(code) on update restrict on delete cascade;
    
    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;
