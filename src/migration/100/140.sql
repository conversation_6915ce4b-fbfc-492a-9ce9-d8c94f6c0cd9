--
-- tabla sessions
-- -> Deja de estar asociada al agente
-- -> Se asocia al usuario
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 140;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */

    -- Habilitamos usuarios para hacer login en nombre de un agente.
    ALTER TABLE sessions
      ADD COLUMN user_id character varying;
    
    update sessions set 
      user_id = (select id from scrty_users u where u.agent_id=sessions.agent_id);

    ALTER TABLE sessions 
      ALTER COLUMN user_id SET NOT NULL,
      ADD FOREIGN KEY (user_id) REFERENCES scrty_users (id) ON UPDATE RESTRICT ON DELETE CASCADE;
    
    alter table sessions
      drop column agent_id;
      
    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;


