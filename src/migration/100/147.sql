-- Eliminar tablas de avatares, tags y eventos de tags
--   Esta info es gestionada por whatsapp-srv, no por agentor
DO $SCHEMAPROC$
DECLARE
  new_version integer := 147;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
    alter table contacts drop column avatar_id cascade;

    drop table avatars;

    drop table contact_tag_events;

    drop table contact_tags;

    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;