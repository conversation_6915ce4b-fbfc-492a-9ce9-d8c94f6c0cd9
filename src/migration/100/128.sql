--
-- Contacto haya tendrá un calificativo de tipo "bancario" (o bankServicer)
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 128;
  -- Identificador del contacto haya
  var_haya_id bigint:= 9223372036854775807;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
    ALTER TABLE contacts add column "isBankServicer" boolean not null default false;
    UPDATE contacts set "isBankServicer"=true where id=var_haya_id;   
    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;

END;
$SCHEMAPROC$;

