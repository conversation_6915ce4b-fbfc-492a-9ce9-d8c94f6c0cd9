--
-- Provedores de publicación
--   El tipo de API que usan y su configuración se indica en la BBDD como parte de la configuración del proveedor
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 126;
BEGIN
  IF (
    SELECT
      max(version)
    FROM
      public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
    ALTER TABLE tasks_publications_providers
      ADD COLUMN api_code character varying;
    -- NOT NULL DEFAULT 'inmofactory';
    ALTER TABLE tasks_publications_providers
      ADD COLUMN api_conf jsonb NOT NULL DEFAULT '{"devel":{},"production":{}}'::json;
    ALTER TABLE tasks_publications_providers
      ADD COLUMN offermapper_options jsonb;

    UPDATE
      tasks_publications_providers
    SET
      api_code = 'inmofactory',
      api_conf = '{
        "production":{
          "apiKey":"2a6e3c22f5e04db8823402fe6a489e2f89879bcf5f6346deaadcea1ef75c933b",
          "baseUrl": "https://api.inmofactory.com/api"
        },
        "devel":{
          "apiKey":"21212edcec3a4f6a81971c8dcf46ee3aeac018e92cf948a9944bc37ae9a60420",
          "baseUrl": "https://api.inmofactory.com/api"
        }
      }'::json
    WHERE
      code = 'inmofactory';

    UPDATE
      tasks_publications_providers
    SET
      api_code = 'pisoscom',
      api_conf = '{
        "production":{
          "agencyId": "SA3813",
          "key": "vJhUbQGWNRAthXh2FFHMpoh0IdWGCqIZ",
          "baseUrl": "https://gateway.habitatsoft.com/api"
        },
        "devel":{
          "agencyId": "SA3813",
          "key": "vJhUbQGWNRAthXh2FFHMpoh0IdWGCqIZ",
          "baseUrl": "https://gateway.habitatsoft.com/api"
        }
      }'::json,
      offermapper_options = '{
        "agencyId": "SA3813",
        "propertyContact": [
          {
            "email": "<EMAIL>",
            "phone": "696908934",
            "publicationId": 1
          }
        ]
      }'
    WHERE
      code = 'pisoscom';
    alter table tasks_publications_providers
      ALTER COLUMN api_code SET NOT NULL;
    ALTER TABLE tasks_publications_providers
      ADD CHECK (api_code IN ('inmofactory', 'pisoscom')) NOT VALID;

    /* END */
    INSERT INTO public.schema_changes (version, script)
      VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;