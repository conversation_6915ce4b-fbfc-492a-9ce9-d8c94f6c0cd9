--
-- Matcher domains:
--    Qué agentes usan el servicio matcher y bajo qué circunstancias.
-- Matcher offers stream
--    En qué momento las ofertas del dominio han sufrido cambios (cr, up).  No se implementa "dl" porque el stream de una oferta se borra al borrarse la oferta.  
--    Las ofertas pasadas a histórico constan como "up"
--    El stream sirve de base para la generación de oportunidades en procesos batch (de hecho, actúa como una cola):
--      Cada entrada del stream procesada es borrada por el código.
DO $SCHEMAPROC$
DECLARE
  new_version integer := 164;
  ofe RECORD;
BEGIN
  IF ( SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */

    Create table matcher_domains (
      agent_id bigint Not null,
      Primary key (agent_id),
      Foreign key (agent_id) REFERENCES public.agents (id) ON UPDATE RESTRICT ON DELETE CASCADE
    );

    Comment on table matcher_domains is 'Define qué "dominios" (=agentes) generarán oportunidades.
    Las ofertas de esos dominios generarán stream y vector (por trigger)
    Los contactos de esos dominios tendrán oportunidades (por código)
    ';

    Insert into matcher_domains(agent_id) select id as agent_id from agents where email='<EMAIL>';
    If not FOUND then
      insert into matcher_domains(agent_id) select id as agent_id from agents where email='<EMAIL>';
    End if;

    CREATE or replace FUNCTION offer_rebuild_vector ( )
      RETURNS TRIGGER
      LANGUAGE 'plpgsql'
      COST 100 VOLATILE NOT LEAKPROOF AS $BODY$
	  DECLARE
	    prop RECORD;
    BEGIN
      If (NEW.status_code='news' or NEW.status_code='commercialization') and exists (select * from matcher_domains md where md.agent_id=NEW.agent_id) then
	      
        For prop in 
          select 
				    zone_id, 
				    "attributes_totalSurfaceM2",
				    "attributes_totalBedroomsCount" 
			    from properties where id=NEW.property_id limit 1
		    loop
			    Insert into matcher_offer_vectors(
				    offer_id,
				    zone_id,
				    "m2Ln",
				    "bedroomsLn",
				    "priceLn"
			    ) values (
				    NEW.id,
				    prop.zone_id,
				    LN(greatest(COALESCE(prop."attributes_totalSurfaceM2", 1),1)),
				    LN(greatest(COALESCE(prop."attributes_totalBedroomsCount", 1),1)), 
				    LN(greatest(COALESCE(NEW."sale_amount", NEW."rent_amount",1 ),1))
			    ) ON CONFLICT(offer_id) DO UPDATE SET
				    zone_id = prop.zone_id,
				    "m2Ln" = LN(greatest(COALESCE(prop."attributes_totalSurfaceM2", 1),1)),
				    "bedroomsLn" = LN(greatest(COALESCE(prop."attributes_totalBedroomsCount", 1),1)), 
				    "priceLn" = LN(greatest(COALESCE(NEW."sale_amount", NEW."rent_amount",1 ),1))
			    ;
		    end loop;
      end if;
      RETURN NEW;
    END;
    $BODY$;

    Create table matcher_offer_changes (
        id Bigserial Not null,
        offer_id Bigint Not null,
        op_code Character varying Not null Check (op_code='cr' or op_code='up'),
        created_at Timestamp with time zone Not null Default now(),
        Primary key (id),
        Foreign key (offer_id) 
          References public.offers (id) 
          On update restrict 
          On delete cascade
    );
    
    Comment on table matcher_offer_changes is '
Registro de cuándo sufre cambios una oferta:
- Cuándo se crea una oferta
- Cuándo se modifica una oferta

- La detección de oportunidades sigue el orden fijado por el stream.  Cada cr/up es cruzado con los deseos de cada contacto del mismo agente.  Una vez cruzado, la entrada del stream es eliminada para que no ocupe espacio.  (Funciona como una cola de cruces pendientes).

Es útil para realizar operativa incremental sobre las ofertas.  Las tareas puden purgar la parte del stream ya procesado ';
    

    Create or replace function offer_record_changes ( )
      Returns TRIGGER
      Language 'plpgsql'
      Cost 100 Volatile Not leakproof 
    As 
    $BODY$
    Begin
        If (NEW.status_code='news' or NEW.status_code='commercialization') and exists (select * from matcher_domains md where md.agent_id=NEW.agent_id) then
          If TG_OP='INSERT' then
            Insert into matcher_offer_changes(
              offer_id,
              op_code
            ) values (NEW.ID, 'cr');
          Elsif TG_OP='UPDATE' then
            Insert into matcher_offer_changes(
              offer_id,
              op_code
            ) values (NEW.ID, 'up');
          End if;
        End if;
        Return NEW;
    End;
    $BODY$;
    
    CREATE TRIGGER offer_record_changes_on_upsert
      AFTER INSERT OR UPDATE ON offers
      FOR EACH ROW
      EXECUTE PROCEDURE offer_record_changes ( );

    --
    For ofe in select id from offers where agent_id in (select agent_id from matcher_domains) order by updated_at asc
    Loop
      update offers set updated_at=updated_at where id=ofe.id;
    End loop;


    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;
