--
-- Grupo de trabajo para ofertas bancarias de topbrokers ("Activos bancarios")
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 151;
  var_member_id integer;
  var_workgroup_owner_email character varying := '<EMAIL>';
  var_workgroup_owner_id bigint;
  var_workgroup_id uuid := 'f2fb4715-970a-04ee-c1da-7a4b269da74b';  
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
	  select id into var_workgroup_owner_id from agents where email = '<EMAIL>';
    if(var_workgroup_owner_id is null) then
      RAISE EXCEPTION 'Se necesita el agente <NAME_EMAIL>';
    else
      -- Creamos el grupo de trabajo
      insert into workgroups(
        id, owner_id, name
      ) values (
        var_workgroup_id, var_workgroup_owner_id, 'Activos Bancarios'
      ) 
      on conflict (id) do nothing;

    end if;
    
    select id into var_member_id from agents where email='<EMAIL>';
    if(var_member_id is null) then
      RAISE WARNING 'No se ha encontrado <NAME_EMAIL> que es quien publicará las ofertas bancarias.  Esto solo es una alerta, no un error. \nTendrás que vincular a mano el agente que puede publicar en "portales bancarios"';
    else
      -- Creamos el miembro que publicará
      insert into workgroup_members(
        workgroup_id,
        agent_id,
        can_publish,
        can_read
      ) values (
        var_workgroup_id,
        var_member_id,
        true,
        false
      ) on conflict (workgroup_id,agent_id) do 
        update 
        set 
          can_publish=true, 
          can_read=false;
    end if;
    
    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;

END;
$SCHEMAPROC$;