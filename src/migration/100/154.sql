DO $SCHEMAPROC$
DECLARE
  new_version integer := 154;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */

   -- Table: public.bank_servicers

    CREATE TABLE public.bank_servicers
    (
        id bigint NOT NULL DEFAULT nextval('properties_id_seq'::regclass),
        custodian_name character varying COLLATE pg_catalog."default",
        custodian_phone character varying COLLATE pg_catalog."default",
        custodian_email character varying COLLATE pg_catalog."default",
        address_full character varying COLLATE pg_catalog."default",
        address_city_name character varying COLLATE pg_catalog."default",
        address_province_name character varying COLLATE pg_catalog."default",
        address_streettype character varying COLLATE pg_catalog."default",
        address_streetname character varying COLLATE pg_catalog."default",
        address_streetnumber character varying COLLATE pg_catalog."default",
        address_streetportal character varying COLLATE pg_catalog."default",
        address_streetfloor character varying COLLATE pg_catalog."default",
        address_streetdoor character varying COLLATE pg_catalog."default",
        address_postcode character varying COLLATE pg_catalog."default",
        address_stair character varying COLLATE pg_catalog."default",
        tstamp timestamp with time zone NOT NULL DEFAULT now(),
    
        CONSTRAINT bank_servicers_pkey PRIMARY KEY (id)
    );
    GRANT ALL ON TABLE public.bank_servicers TO app_user;

    COMMENT ON TABLE public.bank_servicers
        IS 'Datos volcados desde Haya, informe servicers';

    COMMENT ON COLUMN public.bank_servicers.tstamp
        IS 'Fecha del volcado';


    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;