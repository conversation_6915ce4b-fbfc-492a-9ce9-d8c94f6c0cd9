--
-- Versión de imagen para publicaciones sin marca de agua
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 137;
  
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */

    update public.offerversiontypes
    set disclaimer = '{"en":"Disclaimer Monthly Payments...","es":"Este inmueble no se alquila. Puedes comprarlo hasta el 100% financiado por una cuota de [[sale_monthlyPayment]] al mes. Cuota calculada al [[interest_rate]] TIN el primer año, a [[mortgage_years]] años. Precio de venta: [[sale_amount]]", "default":""}'
    where code = 'monthlypayment';          
    
    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;

END;
$SCHEMAPROC$;
