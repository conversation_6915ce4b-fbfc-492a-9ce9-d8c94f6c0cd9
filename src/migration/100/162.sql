--
-- Acciones para "llamadas perdidas"
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 162;
  haya_provider_id bigint := 9223372036854775805;
  individuals_provider_id bigint :=9223372036854775801;
BEGIN
  IF ( SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
    
    -- Proveedor para individuals.  Vinculado al agente al que se vinculan los otros provedores (Haya, en este caso)
    INSERT INTO cloud_providers(id, agent_id, name) values(
      individuals_provider_id,
      (select max(agent_id) from cloud_providers where id=haya_provider_id),
      'Individuals crawler'
    );

    /* END */
    INSERT INTO public.schema_changes (version, script)
      VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;
