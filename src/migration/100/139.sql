--
-- tabla scrty_users
-- -> + agent_id:  Agente asociado a un Usuario (es opcional):  el usuario actúa en nombre de dicho agente
-- -> + scrty_credentials:  Credenciales asociadas a usuario (username y password):  hasta ahora solo teníamos apikeys.
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 139;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */

    -- Habilitamos usuarios para hacer login en nombre de un agente.
    ALTER TABLE scrty_users
      ADD COLUMN agent_id bigint;
    -- Credenciales de un usuario... podría llegar a tener varias:  login es cadena única
    CREATE TABLE scrty_credentials (
      user_id character varying not null,
      username character varying not NULL,
      password character varying not null,
      primary key (username),
      foreign key (user_id)
          REFERENCES scrty_users (id)
          ON UPDATE RESTRICT
          ON DELETE CASCADE
    );
    -- Todos los agentes tienen un usuario por defecto...  
    insert into scrty_users(id, name, agent_id) select newkey() as id, 'Agente ' || a.email as name, a.id as agent_id from agents a;
    -- "Sacamos" las credenciales del agente
    --   Nota: el email del agente es inmutable, por eso podemos copiarlo a sus credenciales sabiendo que nadie lo tocará.  Si se tocara tendríamos que decidir como afecta eso a las credenciales (tendríamos un username distinto al email).  
    insert into scrty_credentials(user_id, username, password) select u.id,a.email,a.password from agents a inner join scrty_users u on a.id=u.agent_id;
    -- Eliminamos la contraseña de la tabla "agents"
    ALTER TABLE agents
      drop column password;
      
    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;


