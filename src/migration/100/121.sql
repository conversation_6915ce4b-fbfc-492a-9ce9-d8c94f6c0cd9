--
-- Renombramos task_publications_publishers a task_publications_providers
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 121;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
    create table tasks_publications_providers (
      code character varying not null,
      name character varying not null,
      PRIMARY KEY (code)
    );
    insert into tasks_publications_providers(code,name) values ('inmofactory', 'Inmofactory (fotocasa, habitaclia)'), ('pisoscom','pisos.com');
        
    alter table tasks_publications_offerlog RENAME publisher_code TO provider_code;
    alter table tasks_publications_offerlog drop constraint tasks_publications_offerlog_publisher_code_fk;
    alter table tasks_publications_offerlog add constraint tasks_publications_offerlog_provider_code_fk foreign key (provider_code) references tasks_publications_providers(code) on update restrict on delete cascade;

    alter table tasks_publications_publishedoffers RENAME publisher_code TO provider_code;
    alter table tasks_publications_publishedoffers drop constraint tasks_publications_publishedoffers_publisher_code_fk;
    alter table tasks_publications_publishedoffers add constraint tasks_publications_publishedoffers_provider_code_fk foreign key (provider_code) references tasks_publications_providers(code) on update restrict on delete cascade;
    

    drop table tasks_publications_publishers;
    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;
