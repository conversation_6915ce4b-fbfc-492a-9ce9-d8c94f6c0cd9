--
-- Provedores externos para haya
--   Tiene un identificador de proveedor determinado.
--   Se asocia al agente agente con el email indicado (si no existe, el script se interrumpe)
--   Se crea un contacto con identificador preestablecido que será asignado como propietario de las ofertas provenientes de haya
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 125;
  var_provider_id bigint := 9223372036854775805;
  var_provider_agent_email character varying := '<EMAIL>';
  var_provider_agent_id bigint;
  -- Identificador del contactg haya
  var_haya_id bigint:= 9223372036854775807;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
	select agent_id into var_provider_agent_id from cloud_providers where id=var_provider_id;
    if(var_provider_agent_id is not null) then
      RAISE INFO 'Ya existe el proveedor de ofertas Haya y está asociado al agente %', var_provider_agent_id;
    else
      select id into var_provider_agent_id from agents where email=var_provider_agent_email;  
      if(var_provider_agent_id is null ) then
        RAISE INFO 'NO EXISTE EL AGENTE % al que asignar las ofertas arañadas de Haya ', var_provider_agent_email;
        RAISE INFO 'Cambia este script indicando el email del agente al que deseas asignar las oferas de Haya';
        RAISE EXCEPTION 'INTERRUMPIDO:  NO EXISTE EL AGENTE % al que asignar las ofertas arañadas de Haya ', var_provider_agent_email;
      else
        insert into cloud_providers(id, agent_id, name) values ('9223372036854775805', var_provider_agent_id, 'Haya crawler');    
      end if;
    end if;
    
    
    if (select count(*) from contacts where id=var_haya_id)=0 then
        INSERT INTO contacts(
            id, agent_id, "firstName", "lastName", notes
        ) VALUES (
            var_haya_id, var_provider_agent_id, 'Haya', 'Bank Servicer', 'Contacto ficticio que actúa como propietario de los inmuebles de Haya'
        );
     else
        RAISE INFO 'El contacto con id % ya existe', var_haya_id;
     end if;
    
    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;

END;
$SCHEMAPROC$;

