--
-- Versión de imagen para publicaciones sin marca de agua
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 129;
  
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
    ALTER TABLE medias 
      add column "publishingNoWm_mediatype" character varying,
      add column "publishingNoWm_url" character varying;
    
    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;

END;
$SCHEMAPROC$;

