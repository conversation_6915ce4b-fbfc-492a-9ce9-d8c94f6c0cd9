--
-- Las acciones tienen un agente origen (Ej: Agente que representa a un portal inmobiliario)
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 123;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
    ALTER TABLE actions ADD COLUMN source_id bigint;
    ALTER TABLE actions ADD FOREIGN KEY (source_id) REFERENCES public.agents (id) ON UPDATE CASCADE ON DELETE SET NULL NOT VALID;  

    insert into agents(id, "firstName", email, password, type_code) values 
	    (9223372036854770000, 'idealista.com', '<EMAIL>', newkey(), 'professional'),
	    (9223372036854769999, 'fotocasa.es','<EMAIL>', newkey(), 'professional'),
	    (9223372036854769998, 'habitaclia.com','<EMAIL>', newkey(), 'professional'),
	    (9223372036854769997, 'pisos.com','<EMAIL>', newkey(), 'professional'),
	    (9223372036854769996, 'indomio.es','<EMAIL>', newkey(), 'professional');

    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;
