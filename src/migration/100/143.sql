-- Renombramos contact_tags_history a contact_tag_events
DO $SCHEMAPROC$
DECLARE
  new_version integer := 143;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
   
    drop table contact_tags_history;
	
    create table contact_tag_events (
      -- Identificador del contacto
      contact_id bigint not null,
      -- Tag añadido/eliminado
      tag_id integer not null,
      -- Fecha del evento
      "at" timestamp with time zone not null default now(),
      -- Typo del evento (1==cr, 2==dl)
      type_code smallint not null,
      -- Usuario que ha realizado la operación
      by_id bigint,
      -- <PERSON><PERSON><PERSON> primaria:  
      primary key(contact_id, tag_id, "at")
    );
    comment on table contact_tag_events is 'Historial de creación/borrado de un tag de un contacto';
    comment on column contact_tag_events.at is 'Instante del evento';
    comment on column contact_tag_events.type_code is 'tipo del evento: 1=cr, 2=dl';
    comment on column contact_tag_events.by_id is '<PERSON><PERSON><PERSON> (scrty_user) que ha realizado la operación';


    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;