--
-- Clave primaria de tasks_publications_publishedoffers ahora es compuesta (provider_code, offer_id)
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 122;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
    
    ALTER TABLE tasks_publications_publishedoffers DROP CONSTRAINT IF EXISTS tasks_inmofactory_publishedoffers_pkey;
    ALTER TABLE tasks_publications_publishedoffers ADD CONSTRAINT tasks_publications_publishedoffers_pk PRIMARY KEY (provider_code, offer_id);

    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;
