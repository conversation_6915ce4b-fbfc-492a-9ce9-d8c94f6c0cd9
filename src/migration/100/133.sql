--
-- Confi<PERSON><PERSON> proveedor de publicación inmofactory para topbrokers bancos
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 133;
BEGIN
  IF (
    SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */

      insert into tasks_publications_providers(code, name, api_code, api_conf, offermapper_options)
      values(
        'inmofactory_bancos',
        'inmofactory para "Oportunidades bancarias" (fotocasa, habitaclia, ...)',
        'inmofactory',
        '{ "apiKey": "pmevuHWgqsqpbvqdfKvpEQslxHfcyFFtszCIDpOHXY1RpCHgjwKa48BuM2yaaPxW", "baseUrl": "https://api.inmofactory.com/api"}',
        '{ "useWatermark": false, "topbrokers_agent_options": { "phoneNumber": "656475590", "email":"<EMAIL>" } }'
      );

      insert into tasks_publications_workgroupproviders(
        workgroup_id, provider_code
      ) values (
        'e08603dc-e198-11ec-b7f6-0a3876e24873',
        'inmofactory_bancos'
      );
  
    /* END */
    INSERT INTO public.schema_changes (version, script)
      VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;