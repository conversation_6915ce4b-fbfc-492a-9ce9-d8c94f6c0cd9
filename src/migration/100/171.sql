--
-- Solicitudes asociadas a una oportunidad (las crea el contacto)
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 171;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) =(new_version - 1) THEN
    /* BEGIN */
    -- DROP TABLE csite_requesttypes
    CREATE TABLE csite_requesttypes(
      code character varying NOT NULL,
      label jsonb NOT NULL DEFAULT '{"default":""}',
      PRIMARY KEY (code )
    );
    INSERT INTO csite_requesttypes(code, label) VALUES 
      ('more_info', '{"default":"More information", "es":"Más información"}'),
      ('to_visit', '{"default":"To visit", "es":"Visitar"}');
      
    -- DROP TABLE csite_requests
    CREATE TABLE csite_requests(
      id bigserial NOT NULL,
      opportunity_id character varying NOT NULL,
      type_code character varying NOT NULL,
      message character varying,
      created_at timestamp with time zone NOT NULL DEFAULT now( ),
      created_ix bigserial NOT NULL,
      PRIMARY KEY (id ),
      FOREIG<PERSON> KEY (opportunity_id ) REFERENCES csite_opportunities(id ) MATCH SIMPLE ON UPDATE RESTRICT ON DELETE CASCADE,
      FOREIGN KEY (type_code ) REFERENCES csite_requesttypes(code ) MATCH SIMPLE ON UPDATE RESTRICT ON DELETE RESTRICT
    );
    CREATE INDEX ix_csite_requests_opportunity_id_a_created_ix_d ON csite_requests(opportunity_id ASC, created_ix DESC);
    CREATE INDEX ix_csite_requests_opportunity_id_a_type_code_a_created_ix_d ON csite_requests(opportunity_id ASC, type_code ASC, created_ix DESC);

    /* END */
    INSERT INTO public.schema_changes(version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;

