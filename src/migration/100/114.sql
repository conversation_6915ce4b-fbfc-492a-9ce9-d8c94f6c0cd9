--
-- Los agentes pueden ser profesionales o particulares
--
-- Inicialmente se establece que los agentes con email acabado en @topbrokers.io son profesionales
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 114;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
    
    CREATE TABLE agenttypes
    (
      code character varying NOT NULL,
      name character varying NOT NULL,
      PRIMARY KEY (code)
    );
    
    insert into agenttypes (code,name) values 
      ('individual','Individual'), 
      ('professional', 'Profesional');

    alter table agents 
      add column type_code character varying not null default 'individual';

    update agents set 
      type_code= 'professional' 
    where 
      email ilike '%topbrokers.io';
    
    ALTER TABLE agents 
      add constraint agents_type_code_fk 
    foreign key (type_code) 
    references agenttypes (code) on update restrict on delete restrict;

    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;

