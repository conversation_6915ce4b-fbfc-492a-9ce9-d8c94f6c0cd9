--
-- Provedores externos para Hipoges-portalnow (hipoges en portugal, portalnow en españa) y Aliseda
--   Tiene un identificador de proveedor determinado.
--   Se asocia al agente agente con el email indicado (si no existe, el script se interrumpe)
--   Se crea un contacto con identificador preestablecido que será asignado como propietario de las ofertas provenientes de Unicaja
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 167;
  var_provider_portal_id bigint := 9223372036854775800;
  var_provider_aliseda_id bigint := 9223372036854775799;
  var_provider_agent_por_id bigint;
  var_provider_agent_ali_id bigint;
  -- Identificador del contacto Hipoges-portalnow
  var_portalnow_id bigint:= 9223372036854775803;
  -- Identificador del contacto Aliseda
  var_aliseda_id bigint:= 9223372036854775802;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN HIPOGES-PORTALNOW*/
	select agent_id into var_provider_agent_por_id from cloud_providers where id=var_provider_portal_id;

    if(var_provider_agent_por_id is not null) then
      RAISE INFO 'Ya existe el proveedor de ofertas Portalnow y está asociado al agente %', var_provider_agent_por_id;
    else
      -- Usamos el agente de un proveedor conocido (para que este script soporte BBDD de desarrollo que usan otro agente principal)
      select agent_id into var_provider_agent_por_id  from cloud_providers where id=9223372036854775805;
      
      INSERT INTO cloud_providers(id, agent_id, name) values(
        var_provider_portal_id,
        var_provider_agent_por_id,
        'Portalnow crawler'
      );
     
    end if;
    
    
    if (select count(*) from contacts where id=var_portalnow_id)=0 then
        INSERT INTO contacts(
            id, agent_id, "firstName", "lastName", notes
        ) VALUES (
            var_portalnow_id, var_provider_agent_por_id, 'Portalnow', 'Bank Servicer', 'Contacto ficticio que actúa como propietario de los inmuebles de Portalnow'
        );
     else
        RAISE INFO 'El contacto con id % ya existe', var_portalnow_id;
     end if;
    
     UPDATE contacts set "isBankServicer"=true where id=var_portalnow_id;  
    /* END */

    /* BEGIN ALISEDA*/
	select agent_id into var_provider_agent_ali_id from cloud_providers where id=var_provider_aliseda_id;

    if(var_provider_agent_ali_id is not null) then
      RAISE INFO 'Ya existe el proveedor de ofertas Aliseda y está asociado al agente %', var_provider_agent_ali_id;
    else
      -- Usamos el agente de un proveedor conocido (para que este script soporte BBDD de desarrollo que usan otro agente principal)
      select agent_id into var_provider_agent_ali_id  from cloud_providers where id=9223372036854775805;
      
      INSERT INTO cloud_providers(id, agent_id, name) values(
        var_provider_aliseda_id,
        var_provider_agent_ali_id,
        'Aliseda crawler'
      );
     
    end if;
    
    
    if (select count(*) from contacts where id=var_aliseda_id)=0 then
        INSERT INTO contacts(
            id, agent_id, "firstName", "lastName", notes
        ) VALUES (
            var_aliseda_id, var_provider_agent_ali_id, 'Aliseda', 'Bank Servicer', 'Contacto ficticio que actúa como propietario de los inmuebles de Aliseda'
        );
     else
        RAISE INFO 'El contacto con id % ya existe', var_aliseda_id;
     end if;
    
     UPDATE contacts set "isBankServicer"=true where id=var_aliseda_id;  
    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;