--
-- Añadidos nuevos campos a la vista mailing_offers_vw
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 134;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */

    DROP VIEW public.mailing_offers_vw;

    CREATE OR REPLACE VIEW public.mailing_offers_vw AS
    SELECT 
      o.id,
      o.updated_at,
      o.status_code,
      o.mandate_type_code,
      o.mandate_start,
      o.mandate_end,
      agent.id AS
        agent_id,
      agent."firstName" AS 
        "agent_firstName",
      agent."lastName" AS
        "agent_lastName",
      agent.email AS
        agent_email,
      o.customer_id AS
        customer_id,
      customer."firstName" AS
        "customer_firstName",
      customer."lastName" AS
        "customer_lastName",
      customer.email AS 
        customer_email,
      customer.mobile AS
        customer_mobile,
      customer."isBankServicer" AS
        "customer_isBankServicer",
      property.type_code AS 
        property_type_code,
      COALESCE(property_type.label ->> 'es'::text, property_type.label ->> 'default'::text) AS 
        property_type_name,
      property.subtype_code AS
        property_subtype_code,
      COALESCE(property_subtype.label->> 'es'::text, property_subtype.label ->> 'default'::text) as 
        property_subtype_name,
      property.address_postcode AS
        property_address_postcode,
      property.address_city_code AS 
        property_address_city_code,
      property_address_city.label ->> 'default'::text AS 
        property_address_city_name,
      property_address_city_province.code AS
        property_address_city_province_code,
      property_address_city_province.label ->> 'default'::text AS
        property_address_city_province_name,
      property.address_streettype_id AS 
        property_address_streettype_id,
      COALESCE(property_address_streettype.label ->> 'es'::text, property_address_streettype.label ->> 'default'::text) AS 
        property_address_streettype_name,
      property.address_streetname AS 
        property_address_streetname,
      property.address_number AS 
        property_address_number,
      property.zone_id as
        property_zone_id,
      array(
        select 
          x.name 
        from (
          select 
            pz.name,
            (select count(*) from propertyzones_containers pzcl where pzcl.contained_id=pzc.container_id) as level
          from 
            propertyzones_containers pzc 
            inner join propertyzones pz on 
              pzc.container_id=pz.id 
          where 
            pzc.contained_id=property.zone_id
        ) x 
        order by x.level asc
      ) AS
        property_zone_path,
      property.location_longitude AS
        property_location_longitude,
      property.location_latitude AS
        property_location_latitude,		
      property."attributes_totalSurfaceM2" AS 
        "property_attributes_totalSurfaceM2",
      property."attributes_totalBedroomsCount" AS 
        "property_attributes_totalBedroomsCount",	
      property."attributes_bathroomsCount" AS 
        "property_attributes_bathroomsCount",
      property."attributes_elevatorHas" AS 
        "property_attributes_elevatorHas",	
      array(select member_workgroup_id from workgroup_offers where offer_id=o.id) as 
        published_on ,		
      ( SELECT max(m.publishing_url::text) AS max FROM medias m JOIN propertymedias pm ON m.key::text = pm.media_key::text AND pm.property_id = property.id AND pm."isFavourite"	) AS 
        property_favouritepicture_publishing_url,
      ( SELECT max(m.thumbnail_url::text) AS max FROM medias m JOIN propertymedias pm ON m.key::text = pm.media_key::text AND pm.property_id = property.id AND pm."isFavourite") AS 
        property_favouritepicture_thumbnail_url,
      o.rent_amount,
      o.sale_amount
    FROM 
      offers o
      JOIN agents agent ON o.agent_id = agent.id
      LEFT JOIN contacts customer ON o.customer_id = customer.id
      JOIN properties property ON o.property_id = property.id
      JOIN propertytypes property_type ON property.type_code::text = property_type.code::text
      LEFT JOIN propertysubtypes property_subtype ON property.subtype_code::text = property_subtype.code::text
      JOIN cities property_address_city ON property.address_city_code::text = property_address_city.code::text
      JOIN provinces property_address_city_province ON property_address_city.province_code::text = property_address_city_province.code::text
      LEFT JOIN streettypes property_address_streettype ON property.address_streettype_id = property_address_streettype.id;
    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;

