--
--  Publicamos todas las ofertas de bancos en el grupo de travajo "Activos Bancarios"
--  Todas las ofertas bancarias deberan publicarse en esta red
DO $$
DECLARE
new_version integer:=153;
BEGIN
  IF (select max(version) from public.schema_changes)=(new_version-1) THEN    

     -- En esta ejecución, todas las ofertas del agente 0 las compartimos en el workgroup "Cloud"
    insert into workgroup_offers 
      select 
        'f2fb4715-970a-04ee-c1da-7a4b269da74b' as member_workgroup_id, 
        agent_id as member_agent_id,
        id as offer_id
      from 
        offers 
      where agent_id=134 and 
      (customer_id=9223372036854775807 or customer_id=9223372036854775806 or customer_id=9223372036854775805);
       
    

    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000')||'.sql');
    /* END */

  END IF;

END;
$$;
