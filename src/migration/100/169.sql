--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 169;
BEGIN
  IF ( SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */

    Create table matcher_contact_sites (
        slug character varying NOT NULL DEFAULT newkey(32),
        contact_id bigint NOT NULL,
        PRIMARY KEY (slug),
        FOREIGN KEY (contact_id)
            REFERENCES public.contacts (id) MATCH SIMPLE
            ON UPDATE CASCADE
            ON DELETE CASCADE
            NOT VALID
    );

    Comment on table matcher_contact_sites is '
      Todo contacto puede tener uno o más sites desde los que poder consultar los matchings (u otras entidades en el futuro).
      Cada site se identifica con un slug único (secuencia de 32 caracteres base64url) para ocultar la identidad real del contacto.
      Nota: "puede" significa que es opcional.
      Nota: Esta entidad es creada por los procesos de matchings al procesar';

	  -- Aseguramos que haya, al menos, un site por cada contacto con oportunidades
	  insert into matcher_contact_sites (contact_id) select distinct contact_id from matcher_opportunities where contact_id  not in (select contact_id from matcher_contact_sites);


    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;

END;
$SCHEMAPROC$;