--
-- El deseado final de opportunities!!!
--
DO $SCHEMAPROC$
declare
  new_version integer := 119;
begin
  if (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* begin */
    drop trigger  offer_creation ON offers;
    drop trigger  offer_update ON offers;
    drop function update_offer_opportunity();
	  drop function create_offer_opportunity();    
    
    drop trigger  demand_creation ON demands;
    drop trigger  demand_update ON demands;
    drop function update_demand_opportunity();
    drop function create_demand_opportunity();
		
    drop table opportunities;
    /* end */
    insert into public.schema_changes (version, script)  values (new_version, to_char(new_version, 'FM000') || '.sql');
  end if;
end;
$SCHEMAPROC$;