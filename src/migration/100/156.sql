DO $SCHEMAPROC$
DECLARE
  new_version integer := 156;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */

    ALTER TABLE public.agent_postcodes
    ADD CONSTRAINT agent_postcodes_agent_id_fkey
    FOREIGN KEY (agent_id)
    REFERENCES public.agents(id);

    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;