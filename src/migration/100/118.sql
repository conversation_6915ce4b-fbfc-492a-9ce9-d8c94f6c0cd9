--
-- <PERSON><PERSON><PERSON> "idealista web crawler" a "Cloud (Idealista)" 
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 118;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
    update 
      agents set "firstName" = 'Cloud (Idealista)' 
    where 
      id=9223372036854775807;
    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;

