DO $SCHEMAPROC$
DECLARE
  new_version integer := 155;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */

   -- Table: public.agent_postcodes
    DROP TABLE IF EXISTS public.agent_postcodes;

    CREATE TABLE public.agent_postcodes (
        postcode varchar NOT NULL,
        agent_id int8 NOT NULL,        
        CONSTRAINT agent_postcodes_pkey PRIMARY KEY (postcode)
    );
    
    GRANT ALL ON TABLE public.agent_postcodes TO app_user;

    COMMENT ON TABLE public.agent_postcodes
        IS 'Datos importados desde Airtable: Agents Gestion - CP Agentes - api-topbrokers';
    

    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;