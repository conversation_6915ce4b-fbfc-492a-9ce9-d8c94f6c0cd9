--
-- Acciones para "llamadas perdidas"
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 124;
BEGIN
  IF (
    SELECT
      max(version)
    FROM
      public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
    
    -- Nuevos tipos de acción
    INSERT INTO actiontypes (id, label, logicorder, service_code, "offerIsRequired", "contactIsRequired", "agentCanCreate")
      VALUES 
        ('sDr2xqBJBjahYgIx7vTTBg', '{"es": "Llamada perdida", "default": "Lost call"}', 1000001, NULL, FALSE, TRUE, FALSE)  ;

    /* END */
    INSERT INTO public.schema_changes (version, script)
      VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;

