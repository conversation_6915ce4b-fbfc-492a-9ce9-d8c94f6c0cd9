--
-- Tabla de cuentas de acceso (usuarios)
-- Tabla de apikeys
-- De momento no se vinculan a agentes (quedará pendiente migrar credenciales del agente a esta tabla)
DO $SCHEMAPROC$
DECLARE
  new_version integer := 108;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
    create table scrty_users (
      id character varying not null default newkey(),
      name character varying not null,
      primary key (id)
    );
    create table scrty_apikeys (
      token character varying not null default newkey(),
      created_at timestamp with time zone  not null default now(),
      deleted_at timestamp with time zone,
      user_id character varying not null,
      primary key (token),
      foreign key (user_id) references scrty_users(id) on delete cascade on update restrict
    );
    
    comment on column public.scrty_apikeys."deleted_at" is 'Instante en el que el token fue invalidado.';

    /* Usuario operador para servicio procesado de emails de portales */
    insert into scrty_users(id, name) values ('penYFDfGLQzqa88EgcmXBw', 'Portals emails processor scripts');
    insert into scrty_apikeys(token, user_id) values ('nQynZC8b4tS1gccxd7VYuw', 'penYFDfGLQzqa88EgcmXBw');
    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;