--
-- Provedores de publicación
--   Añadimos/completamos configuración de mapeo de ofertas
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 130;
BEGIN
  IF (
    SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
    
    UPDATE
      tasks_publications_providers
    SET
      offermapper_options = '{
        "topbrokers_agent_options": {
          "phoneNumber":"696908934"
        },
        "useWatermark": true
      }'::json
    WHERE
      code = 'inmofactory';

    UPDATE
      tasks_publications_providers
    SET
      offermapper_options = '{
        "agencyId": "SA3813",
        "propertyContact": [
          {
            "email": "<EMAIL>",
            "phone": "696908934",
            "publicationId": 1
          }
        ],
        "useWatermark": true
      }'
    WHERE
      code = 'pisoscom';
    

    /* END */
    INSERT INTO public.schema_changes (version, script)
      VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;