--
-- Versión de imagen para publicaciones sin marca de agua
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 135;
  
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */

    /* NUEVA TABLA CON LOS TIPOS DE VERSIONES */
    CREATE TABLE public.offerversiontypes (
      code varchar NOT NULL,
      "label" jsonb NOT NULL DEFAULT '{}'::json,
      "disclaimer" jsonb NOT NULL DEFAULT '{}'::json,
      CONSTRAINT offerversiontypes_pkey PRIMARY KEY (code)
    );

    insert into public.offerversiontypes( code, "label","disclaimer") values ( 'monthlypayment', '{"en":"Monthly payments","es":"Cuotas mensuales", "default":"Monthly payments"}','{"en":"Disclaimer Monthly Payments...","es":"Este inmueble no se alquila. Puedes comprarlo pagando xxx€ cada més.Esta cuota ha sido calculada simulando una hipoteca a 30 años sobre el precio total del inmueble con un interés fijo del 1.4%.", "default":""}');
    insert into public.offerversiontypes( code, "label","disclaimer") values ( 'reformproject', '{"en":"Reform project","es":"Proyecto de reforma", "default":"Reform project"}','{"en":"","es":"", "default":""}');
    insert into public.offerversiontypes( code, "label","disclaimer") values ( 'monthlypayment_with_reformproject', '{"en":"Monthly payments with reform project","es":"Cuotas mensuales con proyecto de reforma", "default":"Monthly payments with reform project"}','{"en":"","es":"", "default":""}');
        
    /* COLUMNAS PARA VERSIONES DE ANUNCIOS EN OFERTAS*/
    ALTER TABLE offers 
      add column "version_of_id" int8,
      add column "version_type_code" character varying,
      add column "version_disclaimer" varchar NULL,
      add column "sale_monthlyPayment" numeric NULL;
      
    /* CLAVES FORANEAS */
    alter table offers add constraint offers_version_of_id_fk foreign key (version_of_id) references offers(id) on update restrict on delete restrict;
    alter table offers add constraint offers_version_type_code_fk foreign key (version_type_code) references offerversiontypes(code) on update restrict on delete restrict;
  
    
    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;

END;
$SCHEMAPROC$;
