---
-- Matcher offers stream
--    Los únicos cambios de oferta que nos interesan son los cambios de estado y cambios de precio
DO $SCHEMAPROC$
DECLARE
  new_version integer := 166;
  ofe RECORD;
BEGIN
  IF ( SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
    /* BEGIN */
   

    Create or replace function offer_record_changes ( )
      Returns TRIGGER
      Language 'plpgsql'
      Cost 100 Volatile Not leakproof 
    As 
    $BODY$
    Begin
        -- Una vez creado, sólo nos interesa detectar cambios en estado y precio... 
        If (NEW.status_code='news' or NEW.status_code='commercialization') and exists (select * from matcher_domains md where md.agent_id=NEW.agent_id) then
          If TG_OP='INSERT' then
            Insert into matcher_offer_changes(
              offer_id,
              op_code
            ) values (NEW.ID, 'cr');
          Elsif TG_OP='UPDATE' and (
            coalesce(NEW.status_code,'')<>coalesce(OLD.status_code,'') OR 
            coalesce(NEW.sale_amount,-1)<>coalesce(OLD.sale_amount,-1) OR
            coalesce(NEW.rent_amount,-1)<>coalesce(OLD.rent_amount,-1)
          ) then
            Insert into matcher_offer_changes(
              offer_id,
              op_code
            ) values (NEW.ID, 'up');
          End if;
        End if;
        Return NEW;
    End;
    $BODY$;


    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;
