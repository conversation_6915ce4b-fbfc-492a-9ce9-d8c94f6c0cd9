--
-- Provedores externos para UCI
--   Tiene un identificador de proveedor determinado.
--   Se asocia al agente agente con el email indicado (si no existe, el script se interrumpe)
--   Se crea un contacto con identificador preestablecido que será asignado como propietario de las ofertas provenientes de Unicaja
--
DO $SCHEMAPROC$
DECLARE
  new_version integer := 172;
  var_provider_uci_id bigint := 9223372036854775798;
  var_provider_agent_uci_id bigint;
  -- Identificador del contacto UCI
  var_uci_id bigint:= 9223372036854775801;
BEGIN
  IF (SELECT max(version) FROM public.schema_changes) = (new_version - 1) THEN
   

    /* BEGIN UCI*/
	select agent_id into var_provider_agent_uci_id from cloud_providers where id=var_provider_uci_id;

    if(var_provider_agent_uci_id is not null) then
      RAISE INFO 'Ya existe el proveedor de ofertas UCI y está asociado al agente %', var_provider_agent_uci_id;
    else
      -- Usamos el agente de un proveedor conocido (para que este script soporte BBDD de desarrollo que usan otro agente principal)
      select agent_id into var_provider_agent_uci_id  from cloud_providers where id=9223372036854775805;
      
      INSERT INTO cloud_providers(id, agent_id, name) values(
        var_provider_uci_id,
        var_provider_agent_uci_id,
        'UCI crawler'
      );
     
    end if;
    
    
    if (select count(*) from contacts where id=var_uci_id)=0 then
        INSERT INTO contacts(
            id, agent_id, "firstName", "lastName", notes
        ) VALUES (
            var_uci_id, var_provider_agent_uci_id, 'UCI', 'Bank Servicer', 'Contacto ficticio que actúa como propietario de los inmuebles de UCI'
        );
     else
        RAISE INFO 'El contacto con id % ya existe', var_uci_id;
     end if;
    
     UPDATE contacts set "isBankServicer"=true where id=var_uci_id;  
    /* END */
    INSERT INTO public.schema_changes (version, script)  VALUES (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END;
$SCHEMAPROC$;