
import { CrwZoneStruct } from "external/crwcommon-api/CrwZoneStruct";
import { Db } from "agentor-lib";

const C_ZONES_TO_DISCARD = [
  "1e48010f-3a20-4556-beec-3ccc97259cbf", //	"Andorra"	
  "4cb09e0c-62cd-40a8-8c5b-5fe7d6f37c47", //	"Cerdanya Francesa"	
  "af0bd385-5d7d-44c3-b231-eac766fcf242", //	"Gibraltar"	
];
export namespace CrwIdealistaZonesDAO {

  export async function getZonesAsTree(crwidealistaDb: Db): Promise<CrwZoneStruct[]> {
    // 1. Read ALL zones from Idealista
    const IdealistaZones = await _listAllIdealistaZones(crwidealistaDb);
    // 2. Build a with all reade zones by its id
    const IdealistaMap = new Map<string, CrwZoneStruct>(IdealistaZones.map(a => [a.id ?? "", a]));
    // 3. Reference each zone from its parent "children" property
    IdealistaZones.forEach(zone => {
      IdealistaMap.get(zone.parent.id ?? "")?.children.push(zone);
    });
    // Devolver las zonas raíz que no sean zonas fuera de España
    return IdealistaZones.filter(z => z.parent.id === null && !C_ZONES_TO_DISCARD.includes(z.id));
  }



  async function _listAllIdealistaZones(crwidealistaDb: Db): Promise<CrwZoneStruct[]> {
    let chunkedZones = [];
    let idealistaZones = await _listIdealistaZones(crwidealistaDb);
    while (idealistaZones.length !== 0) {
      chunkedZones.push(idealistaZones);
      idealistaZones = await _listIdealistaZones(crwidealistaDb, idealistaZones[idealistaZones.length - 1]);
    }
    return chunkedZones.length !== 0 ? ([] as CrwZoneStruct[]).concat(...chunkedZones) : [];
  }

  async function _listIdealistaZones(crwidealistaDb: Db, lastReaded?: CrwZoneStruct, limit: number = 2500): Promise<CrwZoneStruct[]> {
    let terms = [];
    let params = [];

    if (lastReaded !== void 0) {
      params.push(lastReaded.id);
      terms.push(`id>$${params.length}`);
    }


    return crwidealistaDb.
      query(`select id, parent_id, name from zones ${terms.length !== 0 ? `where ${terms.join(" and ")}` : ""}  order by id asc limit $${params.length + 1}`, [...params, limit])().
      then(result => result.rows.map(row => ({
        id: row["id"],
        // Si la zona padre es la zona raíz "fake", la cambiamos para que no tenga padre.
        parent: { id: row["parent_id"] },
        name: row["name"],
        children: []
      })));
  }


}