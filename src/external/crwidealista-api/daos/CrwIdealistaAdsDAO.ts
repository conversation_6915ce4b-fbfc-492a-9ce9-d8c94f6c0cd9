import { PoolClient } from "pg";
import { CrwStructs } from "external/crwcommon-api/CrwAdStruct";
import { SqlWhereHelpers } from "agentor-lib";

const { addIn, addIsNotNull, addIsNull, addGt, addEq } = SqlWhereHelpers;

/**
 * En homes, la entidad que define una oferta inmobiliaria (con su inmueble) se llama "homez"
 */
export namespace CrwIdealistaAdsDAO {

  export type ListFilter = {
    ids?: string[],
    lastRead?: CrwStructs.AdVersion,
    advertiser_id?: string,
    address_city_province_codes?: string[],
    hasContactPhone?: boolean,
    size?: number
  }
  export async function listVersions(crwidealistaDbCli: PoolClient, options: ListFilter): Promise<CrwStructs.AdVersion[]> {
    return _listVersions(crwidealistaDbCli, options);
  }

  export async function list(crwidealistaDbCli: PoolClient, options: ListFilter): Promise<CrwStructs.Ad[]> {
    return _list(crwidealistaDbCli, options);
  }

  async function _listVersions(crwidealistaDbCli: PoolClient, options: ListFilter): Promise<CrwStructs.AdVersion[]> {
    const { ids, advertiser_id, address_city_province_codes, lastRead, hasContactPhone, size = 2000 } = options;
    let terms: string[] = [];
    let params: any[] = [];

    addIn(terms, params, "ads.id", ids);
    // Permitimos anuncios borrados (para poder marcar que se han pasado a histórico)
    //terms.push(`deleted_at is null`);
    // Debe tener datos refinados
    addIsNotNull(terms, "ads.refinedcontent_data", true);
    // Debe tener fecha de actualización
    addIsNotNull(terms, "ads.refinedcontent_when", true);
    addGt(terms, params, "ads.id", lastRead?.id);
    addEq(terms, params, "ads.advertiser_id", advertiser_id);
    // Tiene teléfono de contacto?
    addIsNotNull(terms, `ads.refinedcontent_data#>>'{"source", "contact", "phone"}'`,  hasContactPhone )
    addIn(terms, params, `cities.province_code`, address_city_province_codes)

    params.push(size);
    const qry = `
      select 
        ads.id, 
        greatest(ads.refinedcontent_when, ads.deleted_at) revision,
        ads.deleted_at
      from
        ads
        ${address_city_province_codes ? `inner join cities on ads.refinedcontent_data #>> '{"property","address","city","code"}' = cities.code` : ""}  
      where
        ${terms.length !== 0 ? terms.join(" and ") : "true"}
      order by 
        ads.id asc
      limit 
        $${params.length}
    `;

    const qryResult = await crwidealistaDbCli.query(qry, params);
    return qryResult.rows.map(row => (
      {
        id: row["id"],
        // Sabemos que no es null porque lo hemos impuesto como condión
        revision: (row["revision"] as Date).toISOString(),
        isDeleted: row["deleted_at"] !== null,
      }
    ));
  }

  async function _list(crwidealistaDbCli: PoolClient, options: ListFilter): Promise<CrwStructs.Ad[]> {
    const { ids, advertiser_id, address_city_province_codes, lastRead, hasContactPhone, size = 2000 } = options;
    let terms: string[] = [];
    let params: any[] = [];

    addIn(terms, params, "ads.id", ids);
    // Permitimos anuncios borrados (para poder marcar que se han pasado a histórico)
    //terms.push(`deleted_at is null`);
    // Debe tener datos refinados
    addIsNotNull(terms, "ads.refinedcontent_data", true);
    // Debe tener fecha de actualización
    addIsNotNull(terms, "ads.refinedcontent_when", true);
    addGt(terms, params, "ads.id", lastRead?.id);
    addEq(terms, params, "ads.advertiser_id", advertiser_id);
    addIsNotNull(terms, `ads.refinedcontent_data#>>'{"source", "contact", "phone"}'`,  hasContactPhone )
    addIn(terms, params, `cities.province_code`, address_city_province_codes)

    params.push(size);
    const qry = `
      select 
        ads.id, 
        ads.refinedcontent_data, 
        ads.refinedcontent_when, 
        ads.deleted_at, 
        greatest(ads.refinedcontent_when, ads.deleted_at) revision
      from
        ads
        ${address_city_province_codes ? `inner join cities on ads.refinedcontent_data #>> '{"property","address","city","code"}' = cities.code` : ""}  
      where
        ${terms.length !== 0 ? terms.join(" and ") : "true"}
      order by 
        ads.id asc
      limit 
        $${params.length}
    `;

    const qryResult = await crwidealistaDbCli.query(qry, params);
    return qryResult.rows.map(row => CrwStructs.Ad.deserialize(
      {
        id: row["id"],
        refinedcontent: {
          data: row["refinedcontent_data"],
          when: row["refinedcontent_when"]
        },
        deleted: row["deleted_at"] == null ? null : {
          at: row["deleted_at"]
        },
        revision: (row["revision"] as Date).toISOString()
      }
    ))
  }



}