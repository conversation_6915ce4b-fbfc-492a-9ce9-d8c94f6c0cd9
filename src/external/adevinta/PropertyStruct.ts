export namespace adevinta {

  export declare type PublicationPortalStruct = {
    "PublicationId": DIC_Publication,
    "Name": string,
    "TypeId": DIC_PublicationType,
    "Url": string
    "MaxNumPublishedProperties": number
  }
  /**
   * Estructura con la que Inmofactory identifica un inmueble
   */
  export declare type PropertyRefStruct = {
    AgencyReference: string,
    ExternalId: string
  }
  /**
   * Estructura completa de un inmueble
   */
  export declare type PropertyStruct = {
    /**
     * The unique identifier for a property
     * required 	true
     * type 	string  
       */
    ExternalId: string,
    /**
     * If the property belongs to a promotion, its unique identifier
     * required 	false
     * type 	integer
     */
    ParentId?: number,
    /**Reference given by the agency to the property
     * required 	true
     * type 	string 
     */
    AgencyReference: string,
    /**Identifies the type of the property within the following enumeration
     * required 	true
     * type 	integer        	
     */
    TypeId: DIC_BuildingType,
    /** Indicates the subtype of the property from the value selected in 'TypeId' field
     * required 	false
     * type 	integer 
     **/
    SubTypeId?: DIC_Piso_Subtype | DIC_Casa_Subtype,
    /**
     * description 	Indicates if this is a new construction individual property
     * required 	false
     * type 	boolean
     */
    IsNewConstruction?: boolean,
    /**
     * Indicates the property status for the agency
     * required 	true
     * type 	integer
     */
    PropertyStatusId: DIC_PropertyStatus,
    /** If the property has expired, this field indicates the cause
     * required 	false
     * type 	integer 
     **/
    ExpirationCauseId?: DIC_ExpirationCause,
    /**
     * Indicates if the surface has to be shown in the public view of the property
     * required 	true
     * type 	boolean
     */
    ShowSurface: boolean,
    /**
     * Defines the type of contact provided
     * required 	true
     * type 	integer
     */
    ContactTypeId: DIC_PropertyContactType,
    /**
     * Complete name (and surname) of the property's contact person
     * required 	false
     * type 	string
     */
    ContactName?: string,
    /**
     * Indicates if this is a set of new construction properties
     * required 	true
     * type 	boolean
     */
    IsPromotion: boolean

    /**
     * If the property is a promotion, this field defines its type
     * required 	false
     * type 	integer 
     */
    PromotionTypeId?: number,
    /**
     * If the property is bank awarded, it indicates the entity id
     * required 	false
     * type 	integer
     */
    BankAwardedId?: DIC_BankAwarded,
    /** PropertyAddress
     * 	Defines where is exactly located the property
     * required 	true 
     */
    PropertyAddress: PropertyAddress[],
    /** PropertyDocument
     * Entity used to define the property's document(s) like pictures, videos and others
     * required: false
     */
    PropertyDocument: PropertyDocument[],
    /**
     * Entity used to define the property's features (extras)
     */
    PropertyFeature: PropertyFeature[],

    PropertyContactInfo: PropertyContactInfo[],

    PropertyTransaction: PropertyTransaction[],
    /**
     * List of publications which the property will be sent to
     */
    PropertyPublications: PropertyPublication[],
  }

  export declare type PropertyAddress = {
    /**  ZIP Code (not required if x and y are specified)
     * required: false,
     * type: string
     */
    ZipCode?: string,
    /** 
     * Country 
     * required: true
     * type: integer
     * */
    CountryId: DIC_LocationCountry,
    /** 
     * Free text. Zone where the property is located
     * required: false
     * type: string
     */
    Zone?: string
    /**
     * The type of the street where the property is located according to the following enum
     * required: true
     * type: integer
     */
    StreetTypeId: DIC_StreetType,
    /** "Free text. The name of the street"
     * required: false,
     * type: string
     */
    Street?: string,
    /** Number of the street */
    Number?: string,
    /** Enum of the floor */
    FloorId?: DIC_Floor,
    /** "Free text. Indicates the door number (or letter) in the floor where is located the property", */
    Door?: string,
    /** "Free text. Indicates the Stair (if any) where is located the property", */
    Stair?: string,
    /** Longitude coordinate used to geographycally place the property */
    x: number,
    /** Latitude coordinate used to geographycally position the property */
    y: number,
    /** Defines how to display the property address */
    VisibilityModeId: DIC_VisibilityMode
  }
  export declare type PropertyDocument = {
    /** 
     * Describes the type of file according to the following enumeration
     * required 	true
     * type 	integer 
     */
    TypeId: DIC_DocumentType,
    /** 
     * Free text. Provide a description for the document.
     * required 	false 
     */
    Description?: string,
    /**
     * It must contain the file name (i.e. 'picture.jpg')
     * required 	true
     * type 	string
     */
    Url: string,
    /**
     * If the document refers to a room, you can indicate it within this field, according to the following enumeration
     * required 	false
     * type 	integer
     */
    RoomTypeId?: DIC_RoomType,
    /**
     * Indicates the file type according to the following enum
     * required 	true
     * type 	integer
     */
    FileTypeId: DIC_FileType,
    /**
     * Indicates whether the document has to be shown in publications or communications
     * required 	true
     */
    Visible: boolean,
    /**
     * Use it to alter the order of appearence of the document in lists
     * required 	true
     */
    SortingId: number,
  };
  export declare type PropertyFeature = {
    FeatureId: number
    LanguageId: DIC_Language,
    DecimalValue?: number,
    BoolValue?: boolean,
    TextValue?: string
  };
  export declare type PropertyContactInfo = {
    TypeId: DIC_PropertyContactInfoType,
    Value: string,
    ValueTypeId: DIC_PropertyContactInfoValueType,
  };
  export declare type PropertyTransaction = {
    TransactionTypeId: DIC_TransactionType,
    CustomerPrice?: number,
    Price: number,
    PriceM2: number,
    CurrencyId: DIC_Currency,
    PaymentPeriodicityId?: DIC_PaymentPeriodicity,
    ShowPrice: boolean,
    Conditions?: string,
  };
  export declare type PropertyPublication = {
    PublicationId: DIC_Publication,
    PublicationTypeId: DIC_PublicationType,
  };
  export const enum DIC_BuildingType {
    Piso = 1,
    Casa = 2,
    Local = 3,
    Oficina = 4,
    Edificio = 5,
    Suelo = 6,
    Industrial = 7,
    Parking = 8,
    Hotel = 10,
    Trastero = 12,
  };
  export const enum DIC_Piso_Subtype {
    Semisotano = 1,
    Triplex = 2,
    Duplex = 3,
    Buhardilla = 4,
    Ático = 5,
    Estudio = 6,
    Loft = 7,
    Otro = 8,
    Piso = 9,
    Apartamento = 10,
    Planta_baja = 11,
  };
  export const enum DIC_Casa_Subtype {
    Casa = 13,
    Cortijo = 14,
    Adosada = 17,
    Caserio = 18,
    Pareada = 19,
    Chalet_Torre = 20,
    Masía = 21,
    Unifamiliar = 23,
    Casa_rustica = 24,
    Casa_de_pueblo = 25,
    Casa_rural = 26,
    Bungalow = 27,
    Casona = 28,
  }

  export const enum DIC_PropertyStatus {
    Disponible = 1,
    Reservado = 2,
    Captación = 3,
    No_disponible = 4,
    En_construccion = 5,
  }
  export const enum DIC_ExpirationCause {
    Vendido = 1,
    Anulación = 2,
    Alquilado = 3,
    Bloqueado_por_geografía = 4,
    Caducado = 5,
    Sin_registro_de_catastro = 6,
    Duplicado = 7,
    Bloqueado = 8,
    Venta_Compartida = 9,
  }
  export const enum DIC_PropertyContactType {
    Agencia = 1,
    Agente = 2,
    Especificado = 3,
  }
  export const enum DIC_PropertyContactInfoType {
    Email = 1,
    Telefono = 2,
    Movil = 3,
    Fax = 4,
    Web = 5,
    AIM_AOL = 6,
    Yahoo = 7,
    Messenger = 8,
    Windows_Live_Messenger = 9,
    ICQ = 10,
    Jabeer_Google_Talk = 11,
    Skype = 12,
    Facebook = 13,
    Twitter = 14,
    SlideShare = 15,
    Blog = 16,
    Otros = 17,
  }
  /** Indicates if the contact refers to the agency, the agent or other specified */
  export const enum DIC_PropertyContactInfoValueType {
    Datos_de_mi_agencia = 1,
    Agente_del_inmueble = 2,
    Otros_datos = 3,
  };

  /** Identifies the property's promotion type */
  export const enum DIC_PromotionType {
    Residencial = 1,
    Oficina_Local = 2,
    Industrial = 3
  };
  /**Logical status for a register. This is an internal status for logically manage the property's data in our database system. Only registers with StatusId = 2 (Active) are displayed in the application */
  export const enum DIC_Status {
    Pendiente = 1,
    Activo = 2,
    Caducado = 3,
    Eliminado = 4,
    Error = 5,
  }
  /** Identifies the bank entity for properties of the bank awarded type */
  export const enum DIC_BankAwarded {
    Bancaja_Bancajahabitat = 1,
    General_Electric = 2,
    Citibank = 3,
    UCI_Unión_de_Créditos_Inmobiliarios_Inmoos = 4,
    Otra = 5,
    Banco_Santander_Altamira_santander = 6,
    Credit_Agricole = 7,
    GMAC = 8,
    La_Caixa_Servihabitat = 9,
    BNP = 10,
    Ahorro_Corporación = 11,
    CRM_Caja_Rural_del_Mediterráneo = 12,
    Bankia_Aktua = 13,
    Cajamar_Cimenta2 = 14,
    Caja_Mediterráneo_CAM = 15,
    CatalunyaCaixa_Cxinmobiliaria = 16,
    Bankia_Sareb = 17,
    BBVA_Atrea = 18,
    Banco_Sabadell_Solvia = 19,
    Unicaja_Inmuebles = 20,
    Banco_Popular_Ges_Aliseda = 21,
    Banca_March_Bancamarch = 22,
    Banco_Caixa_Geral_Inmocaixageral = 23,
    Banco_de_Valencia = 24,
    Banco_Guipuzcoano_Inmobicoaria = 25,
    Banco_Herrero_Solvia = 26,
    Banco_MareNostrum_BMN = 27,
    Banco_Pastor_Inmoseleccion = 28,
    Banesto_Inmobihtaria = 29,
    Bankia_Haya_Real_State = 30,
    Bankinter = 31,
    Barclays_Aberis = 32,
    BBVA_Vivienda_Anida = 33,
    Caixa_Catalunya_Procam = 34,
    Caixa_Galicia_Cxginmobiliaria = 35,
    Caixa_Girona_CaixaGirona = 36,
    Caixa_Manresa_Procam = 37,
    Caixa_Nova_Proinova = 38,
    Caixa_Penedès_Revalua = 39,
    Caixa_Sabadell_Unnimcasa = 40,
    Caixa_Terrassa_Unnimcasa = 41,
    Caja_Canarias_Incavesa = 42,
    Caja_Cantabria_Cajacantabria = 43,
    Caja_Círculo_Cajacirculo = 44,
    Caja_de_Burgos_Cajadeburgos = 45,
    Caja_de_Jaén_Cajaen = 46,
    Caja_Duero_Giasainversiones = 47,
    Caja_España_Caja_España = 48,
    Caja_Granada_Caja_Inmobiliaria = 49,
    Caja_Guadalajara_Cajaguadalajara = 50,
    Caja_Inmaculada_Caiencasa = 51,
    Caja_Mediterráneo_Caja_Ascam = 52,
    Caja_Murcia_Caja_Tex = 53,
    Caja_Navarra_Caja_Navarra = 54,
    Caja_Segovia_Edictaservicios = 55,
    Caja_Vital_Kutxa_Cajavital = 56,
    Cajalón_Ruralvia = 57,
    Cajas_Rurales_Ruralvia = 58,
    Cajasol = 59,
    Cajastur_Enobranueva = 60,
    Cajasur_Inmobiliaira = 61,
    Deutsche_Bank_Deutsche_Bank = 62,
    Halifax_Idealista = 63,
    Ibercaja = 64,
    Kutxa_Aiksa = 65,
    La_Caja_de_Canarias = 66,
    NovaCaixaGalicia_Escogecasa = 67,
    Sa_Nostra_Netmobilia = 68,
    Unnim_Unnimcasa = 69,
    Anticipa = 70,
    Hipoges = 71,
    Finsolutia = 72,
    Acciona = 73,
    Aedas_Homes = 74,
    Inmocaixa = 75,
  }
  /** Identifies the countries */
  export const enum DIC_LocationCountry {
    Andorra = 20,
    Francia = 250,
    Portugal = 620,
    España = 724,
  }
  /** Identifies the type of street for the property */
  export const enum DIC_StreetType {
    Calle = 1,
    Paseo = 2,
    Avenida = 3,
    Ronda = 4,
    Travesía = 5,
    Carretera = 6,
    Rambla = 7,
    Plaza = 8,
    Pasaje = 9,
    Bajada = 10,
    Vía = 11,
    Urbanización = 13,
    Camino = 14,
    Sector = 15,
    Glorieta = 16,
    Alameda = 17,
    Barranco = 18,
    Calleja = 19,
    Cuesta = 20,
    Grupo = 21,
    Gran_via = 22,
    Jardines = 23,
    Muelle = 24,
    Poligono_industrial = 25,
    Parque = 26,
    Prolongación = 27,
    Riera = 28,
    Rua = 29,
    Subida = 30,
  }
  /** Identifies the floor type of the property */
  export const enum DIC_Floor {
    Sótano = 1,
    Subsótano = 2,
    Bajos = 3,
    Entresuelo = 4,
    Principal = 5,
    p1 = 6,
    p2 = 7,
    p3 = 8,
    p4 = 9,
    p5 = 10,
    p6 = 11,
    p7 = 12,
    p8 = 13,
    p9 = 14,
    p10 = 15,
    p11 = 16,
    p12 = 17,
    p13 = 18,
    p14 = 19,
    p15 = 20,
    p15_mas = 21,
    Ático = 22,
    Sobreático = 23,
    Planta_baja = 24,
    Medio = 28,
    Último = 29,
    Terraza = 30,
    Otro = 31,
  }
  /** Identifies the document type */
  export const enum DIC_DocumentType {
    Foto = 1,
    Memoria_de_calidades = 2,
    Escritura = 3,
    Licencia_obra = 4,
    Dossier = 5,
    Otros = 6,
    Tour_virtual = 7,
    Video = 8,
    MicroSite = 9,
    Otros_Link = 10,
    Vista_aérea = 11,
    Logo_Plantillas = 12,
    Firma = 13,
    DNI_CIF = 15,
    Contrato = 16,
    Documentación_General = 17,
    Plano = 23,
    Contrato_2 = 24,
    Informe = 25,
    Link_Vídeo_Externo = 31,
    Link_Tour_Virtual = 32,
    Logo = 43,
    Documento_del_catastro = 45,
    Certificado_energético = 47,
    Hoja_de_Visita_Personalizada = 48,
  };

  /** If the document refers to a room, the room type can be provided according to the following dictionary */
  export const enum DIC_RoomType {
    Terraza = 1,
    Garaje = 3,
    Buhardilla = 5,
    Trastero = 6,
    Cocina = 7,
    Dormitorio = 8,
    Baño = 9,
    Jardín = 10,
    Salón = 11,
    Plano = 12,
    Fachada = 13,
    Comedor = 14,
    Despacho = 16,
    Entrada = 17,
    Pasillo = 18,
    Piso_3d = 19,
    Plano_de_situación = 20,
    Sala = 21,
    Salón_comedor = 22,
    Vistas = 23,
  }
  /** Identifies the type of the file in the file system */
  export const enum DIC_FileType {
    Documento_GIF = 1,
    Documento_JPG = 2,
    Documento_Word = 3,
    Plantilla_Word = 4,
    Plantilla_Excel = 5,
    Plantilla_Html = 6,
    Documento_QT_Movie = 7,
    Documento_HTML = 8,
    Documento_RTF = 9,
    Documento_PDF = 10,
    Documento_Texto_Simple = 11,
    Documento_Excel = 12,
    Plantilla_email_html = 13,
    Documento_Power_Point = 14,
    Plantillas_Auto_Adjuntables_html = 15,
    Plantillas_Comunicado = 16,
    Link = 17,
    Documento_Word_2007 = 19,
    Documento_Excel_2007 = 20,
    Documento_Power_Point_2007 = 21,
    Documento_BMP = 22,
    Documento_TIF = 23,
    Documento_DWG = 24,
    Documento_MPEG = 25,
    Documento_AVI = 26,
    Documento_WMA = 27,
    Documento_FLV = 28,
    Documento_MPG = 29,
    Documento_DIVX = 31,
    Documento_HTML_2 = 32,
    Documento_WMV = 33,
    Documento_JPEG = 34,
    Documento_PNG = 35,
    Documento_MP4 = 36,
  }
  /** Defines the languages supported by the application */
  export const enum DIC_Language {
    Portugués = 1,
    Inglés = 2,
    Francés = 3,
    Español = 4,
    Italiano = 5,
    Alemán = 6,
    Griego = 7,
    Catalán = 8,
    Euskera = 9,
    Gallego = 13,
    Holandés = 14,
    Valenciano = 15,
    Búlgaro = 16,
    Noruego = 17,
  };
  /** Defines the type of the transaction */
  export const enum DIC_TransactionType {
    Venta = 1,
    Alquiler = 3,
    Traspaso = 4,
    A_compartir = 7,
    Alquiler_vacacional = 8,
    Alquiler_con_opcion_a_compra = 9,
  }
  /** Defines the currencies supported by the application */
  export const enum DIC_Currency {
    Euros = 1
  }
  /** Defines the periodicity that the transaction payment is made */
  export const enum DIC_PaymentPeriodicity {
    Diario = 1,
    Semana = 2,
    Mes = 3,
    Temporada = 4,
    Año = 5,
    Quincena = 6,
  }
  /** Defines the publications supported by our system */
  export const enum DIC_Publication {
    /** htt://www.fotocasa.es */
    Fotocasa_y_Milanuncios = 1,//
    /** ht://www.enalquiler.com */
    EnAlquiler_com = 17,//
    /** ht://www.casasconjardin.com */
    casasconjardin_com = 18,//
    /** ht://www.tevagustar.es */
    tevagustar_es = 20,//
    /** ht://www.trovit.es */
    Trovit = 21,//
    /** ht://www.miparcela.com */
    miparcela_com = 22,//
    /** ht://www.inzoco.com */
    inzoco_com = 24,//
    /** ht://www.inmostock.es */
    inmostock_es = 25,//
    /** ht://www.globaliza.com */
    globaliza_com = 26,//
    /** ht://www.multipiso.com */
    multipiso_com = 27,//
    /** ht://www.urbaniza.com */
    urbaniza_com = 28,//
    /** ht://www.capgros.com */
    capgros_com = 29,//
    /** ht://www.tucasa.com */
    tucasa_com = 30,//
    /** ht://www.pisos.com */
    pisos_com = 31,//
    /** ht://www.alquilacompra.es */
    alquilacompra_es = 32,//
    /** ht://www.buscocasa.com */
    buscocasa_com = 33,//
    /** ht://www.facilisimo.com */
    facilisimo_com = 34,//
    /** ht://www.ibuscando.com */
    ibuscando_com = 35,//
    /** ht://www.ganga.es */
    ganga_es = 36,//
    /** ht://www.tenertodo.com */
    tenertodo_com = 37,//
    /** ht://www.emaresme.com */
    eMaresme_com = 38,//
    /** ht://www.realtorworldguide.com */
    RealtorWorldGuide_com = 39,//
    /** ht://www.ivive.com */
    Ivive_com = 1084,//
    /** ht://www.spainhouses.net */
    spainhouses_net = 1983,//
    /** ht://www.yaencontre.com */
    yaencontre_com = 2047,//
    /** 	 */
    Web_Mobile_Fotocasa = 2157,
    /**  	http://www.hogaria.net */
    hogaria_net = 2346,
    /**  	http://www.anuncioneon.com */
    anuncioneon = 2725,
    /**  	http://www.buscafincas.com */
    Buscafincas = 3076,
    /**  	http://www.alquilerjoven.com */
    Alquilerjoven = 3078,
    /**  	http://www.instalate.com */
    Instalate = 3079,
    /**  	http://www.pisocasas.com */
    Pisocasas_com = 3387,
    /**   */
    Test_portal = 3439,
    /**  	http://www.centrocomercialinmobiliario.com */
    Centro_Comercial_Inmobiliario = 3489,
    /**  	http://www.green-acres.es/ */
    Green_Acres = 3504,
    /**  	http://www.hispacasas.com */
    HispaCasas_com = 3799,
    /**  	http://www.linkbyu.es */
    linkbyu_es = 4005,
    /**  	http://www.micasa.es */
    Micasa_es = 4006,
    /**  	http://www.divendo.es */
    Divendo_es = 4010,
    /**  	http://www.buscocasaenmurcia.com */
    Buscocasaenmurcia = 4112,
    /**  	http://www.elpisoideal.com */
    elpisoideal = 4114,
    /**  	http://www.alnido.es */
    alnido = 4115,
    /**  	http://www.inmoportalix.com */
    inmoportalix = 4116,
    /**  	http://www.plandeprotecciondealquiler.com */
    plandeprotecciondealquiler_com = 4508,
    /**  	http://www.casanuncio.com */
    casanuncio_com = 4533,
    /**  	http://www.pisocasa.es */
    pisocasa_es = 4534,
    /**  	http://www.granmanzana.es */
    granmanzana_es = 4535,
    /**  	http://www.compra-venta.org */
    compra_venta_org = 4536,
    /**  	http://www.vendoo.net */
    vendoo_net = 4537,
    /**  	http://www.mitula.com */
    mitula_com = 4538,
    /**  	http://www.kyero.com */
    kyero_com = 4539,
    /**  	http://www.heraldo.es */
    Heraldo_es = 4692,
    /**  	http://www.misviviendas.com */
    misviviendas = 5131,
    /**  	http://www.venderya.es */
    venderya = 5189,
    /**  	 */
    Portales_Internacionales = 6053,
    /**  	http://anuncit.com */
    Anuncit = 7598,
    /**  	http://trovimap.com */
    Trovimap = 9127,
    /**  	http://worldimmo.org */
    WorldImmo = 9282,
    /**  	https://www.thinkspain.com/ */
    Thinkspain = 10025,
    /**   Microsite Advance	 */
    Fotocasa = 19156,
    /**  	http://www.habitaclia.com */
    Habitaclia = 23648,
    /**   */
    Internacional_Premium_Network = 23958,
    /**   */
    Listglobally_Classic = 23959,
    /**   */
    Finn = 28638,
    /**   */
    Internacional_Basic_Network = 32421,
  }

  /** Defines the publications type supported by our system */
  export const enum DIC_PublicationType {
    Web_propia = 1,
    Oficina_Virtual = 2,
    Sitio_con_pasarela_de_pago = 3,
    Sitio_con_pasarela_gratuita = 4,
    Otros_sitio = 5,
    Sitio_público = 6,
    Web_propia_Nueva = 7,
  };

  /** Defines activity event's type */
  export const enum DIC_ActivityEventType {
    SMS_en_cambio_precio = 1,
    Prensa = 2,
    Tasación = 3,
    e_mail = 4,
    Reservar_Inmueble = 5,
    Oferta_económica = 6,
    Publicar_en_prensa = 7,
    Internet = 8,
    Llamada_Cliente = 9,
    Llamada_Propietario = 10,
    Contrato = 11,
    Email_en_cambio_de_precio = 12,
    Primera_visita = 13,
    Colgar_Cartel = 14,
    Seguimiento_de_captación = 15,
    Alerta_en_cambio_de_precio = 16,
    Enviar_SMS = 17,
    SMS_en_cruce = 18,
    Publicar_en_Díptico = 19,
    Telemarketing = 20,
    Enviar_e_mail = 21,
    Segunda_visita = 22,
    Visita = 23,
    Visita_captación = 24,
    Seguimiento = 25,
    Email_en_cruce = 26,
    Mailing = 27,
    Escaparate = 28,
    Alerta_en_cruce = 29,
    Reunión_Cliente = 31,
    Revista = 33,
    Solicitud_de_información = 34,
    Atención_telefónica = 35,
    Atención_oficina = 36,
    Acción_comercial_propia = 37,
  }
  /** Defines activity event's status */
  export const enum DIC_ActivityEventStatus {
    Pendiente = 1,
    Realizada = 2,
    Anulada = 3,
  }
  /** Id 	Name */
  export const enum DIC_VisibilityMode {
    Mostrar_Calle_y_Número = 1,
    Mostrar_Calle = 2,
    Mostrar_Sólo_zona = 3,
  }
  //#region Feature

  export declare type Features = {
    //1
    Superficie: number,
  }
  export declare type FeaturesFotocasa = {
    // 2 COMUNES
    DescripcionBreve?: string,
    // 3 COMUNES
    DescripcionExtendida?: string,
    // 4 COMUNES
    OcultarSuperficie: boolean,
    // 11 PISO, CASA
    NumHabitaciones?: number,
    // 12 PISO, CASA
    NumBaños?: number,
    // 13 PISO, CASA
    NumAseos?: number,
    // 22 PISO
    Ascensor?: boolean,
    // 23 PISO, CASA
    Parking?: boolean,
    // 24 PISO, CASA
    Trastero?: boolean,
    // 25 PISO, CASA
    Piscina?: boolean,
    // 27 PISO, CASA
    Terraza?: boolean
    // 28 PISO, CASA
    Orientacion?: DIC_Orientacion,
    // 30 PISO, CASA
    Amueblado?: boolean,
    // 69 CASA
    Superficie_solar?: number,
    // 142 PISO, CASA
    Domótica?: boolean,
    // 171 CASA
    Ascensor_CASA?: boolean,
    // 231 PISO, CASA
    AñoConstrucción?: number
    // 249 PISO, CASA
    Conservación?: DIC_Conservación,
    // 254 PISO, CASA
    AireAcondicionado?: boolean,
    // 257 PISO, CASA
    Lavadero?: boolean,
    // 258 PISO, CASA
    Armarios?: boolean,
    // 259 PISO, CASA
    Electrodomésticos?: boolean,
    // 260 PISO, CASA
    Suite_con_baño?: boolean,
    // 263 PISO, CASA
    Patio?: boolean,
    // 281 PISO, CASA
    Cuarto_de_lavado_y_plancha?: boolean,
    // 285 PISO, CASA
    Vistas_a_la_montaña?: boolean,
    // 289 PISO, CASA
    Cocina_office?: boolean,
    // 290 PISO, CASA
    Parquet?: boolean,
    // 294 PISO, CASA
    Puerta_blindada?: boolean,
    // 295 PISO, CASA
    Gres_Cerámica?: boolean,
    // 296 PISO, CASA
    Calefacción?: boolean,
    // 297 PISO, CASA
    Balcones?: boolean,
    // 298 PISO, CASA
    Jardín_privado?: boolean,
    // 300 PISO, CASA
    Piscina_comunitaria?: boolean,
    // 301 PISO, CASA
    Zona_comunitaria?: boolean,
    // 302 PISO, CASA
    Zona_deportiva?: boolean,
    // 303 PISO,CASA
    Zona_infantil?: boolean,
    // 304 PISO,CASA
    Energía_solar?: boolean,
    // 305 PISO, CASA
    Párking_comunitario?: boolean,
    // 306 PISO, CASA
    Conserje?: boolean,
    // 307 PISO, CASA
    Vídeo_portero?: boolean,
    // 308 PISO, CASA
    Ascensor_interior?: boolean,
    // 309 PISO, CASA
    Gimnasio?: boolean,
    // 310 PISO, CASA
    Pista_de_tenis?: boolean,
    // 313 PISO, CASA
    Se_aceptan_mascotas?: boolean,
    // 314 PISO, CASA
    Cocina_equipada?: boolean,
    // 315 PISO, CASA
    Con_vistas_al_mar?: boolean,
    // 316 PISO, CASA
    No_amueblado?: boolean,
    // 321 PISO, CASA
    Agua_caliente_sanitaria?: DIC_AguaCalienteSanitaria,
    // 323 PISO, CASA
    Escala_eficiencia_consumo?: DIC_EscalaEficienciaConsumo,
    // 324 PISO, CASA
    Escala_eficiencia_emisiones?: DIC_EscalaEficienciaEmisiones,
    // 325 PISO, CASA
    Valor_eficiencia_consumo?: number,
    // 326 PISO, CASA
    Valor_eviciencia_emisiones?: number,
    // 327 PISO, CASA
    Certificado_energético?: DIC_CertificadoEnergético,
  }

  export declare type FeaturesExtras = {
    // 57 COMUNES
    SuperficieUtil?: string,
    // 29 PISO, CASA
    Calefacción?: boolean,
    // 42 PISO, CASA
    Núm_habitaciones_dobles?: number,
    // 43 PISO, CASA
    Núm_habitaciones_individuales?: number,
    // 44 PISO, CASA
    Núm_suites?: number,
    // 45 CASA
    Núm_plantas?: number,
    // 66 PISO
    AlturaReal?: number,
    // 70 PISO, CASA
    Superficie_edificada?: number,
    // 98 PISO, CASA
    Carpintería_exterior?: DIC_CarpinteriaExterior,
    // 101 PISO, CASA
    Puerta_principal?: DIC_PuertaPrincipal,
    // 110 PISO, CASA
    Suelos?: DIC_Suelo,
    // 123 PISO, CASA
    Agua?: boolean,
    // 124 PISO, CASA
    Gas?: boolean,
    // 126 PISO, CASA
    Agua_caliente?: DIC_AguaCaliente,
    // 129 PISO, CASA
    Refrigeración?: DIC_Refrigeración,
    // 199 PISO, CASA
    Sol?: DIC_Sol,
    // 235 PISO, CASA
    Alarma?: boolean,
    // 320 PISO, CASA
    Calefacción_opt?: DIC_Calefacción,
  }

  export function buildFeatures(comunes: Features, fotocasa: FeaturesFotocasa, extras: FeaturesExtras, LanguageId: DIC_Language | undefined = DIC_Language.Español): PropertyFeature[] {
    let result: PropertyFeature[] = [];
    if (comunes.Superficie !== void 0)
      result.push({
        FeatureId: 1, // TODOS
        DecimalValue: comunes.Superficie,
        LanguageId
      });
    if (fotocasa.DescripcionBreve !== void 0)
      result.push({
        FeatureId: 2, // TODOS
        TextValue: fotocasa.DescripcionBreve,
        LanguageId,
      });
    if (fotocasa.DescripcionExtendida !== void 0)
      result.push({
        FeatureId: 3, // TODOS
        TextValue: fotocasa.DescripcionExtendida,
        LanguageId,
      });
    if (fotocasa.OcultarSuperficie !== void 0)
      result.push({
        FeatureId: 4,
        BoolValue: fotocasa.OcultarSuperficie,
        LanguageId
      });
    if (extras.SuperficieUtil !== void 0)
      result.push({
        FeatureId: 57, // TODOS
        TextValue: extras.SuperficieUtil,
        LanguageId
      });
    if (fotocasa.NumHabitaciones !== void 0)
      result.push({
        FeatureId: 11, // PISO, CASA
        DecimalValue: fotocasa.NumHabitaciones,
        LanguageId,
      });
    if (fotocasa.NumBaños !== void 0)
      result.push({
        FeatureId: 12, // PISO, CASA
        DecimalValue: fotocasa.NumBaños,
        LanguageId,
      });
    if (fotocasa.NumAseos !== void 0)
      result.push({
        FeatureId: 13, // PISO, CASA
        DecimalValue: fotocasa.NumAseos,
        LanguageId,
      });
    if (fotocasa.Ascensor !== void 0)
      result.push({
        FeatureId: 22, // PISO
        BoolValue: fotocasa.Ascensor,
        LanguageId,
      });
    if (fotocasa.Parking !== void 0)
      result.push({
        FeatureId: 23, // PISO, CASA
        BoolValue: fotocasa.Parking,
        LanguageId,
      });
    if (fotocasa.Trastero !== void 0)
      result.push({
        FeatureId: 24, // PISO, CASA
        BoolValue: fotocasa.Trastero,
        LanguageId,
      });
    if (fotocasa.Piscina !== void 0)
      result.push({
        FeatureId: 25, // PISO, CASA
        BoolValue: fotocasa.Piscina,
        LanguageId,
      });
    if (fotocasa.Terraza !== void 0)
      result.push({
        FeatureId: 27, // PISO, CASA
        BoolValue: fotocasa.Terraza,
        LanguageId,
      });
    if (fotocasa.Orientacion !== void 0)
      result.push({
        FeatureId: 28, // PISO, CASA
        DecimalValue: fotocasa.Orientacion,
        LanguageId,
      });
    if (fotocasa.Amueblado !== void 0)
      result.push({
        FeatureId: 30, // PISO, CASA
        BoolValue: fotocasa.Amueblado,
        LanguageId
      });
    if (fotocasa.Superficie_solar !== void 0)
      result.push({
        FeatureId: 69, // CASA
        DecimalValue: fotocasa.Superficie_solar,
        LanguageId,
      });
    if (fotocasa.Domótica !== void 0)
      result.push({
        FeatureId: 142, // PISO, CASA
        BoolValue: fotocasa.Domótica,
        LanguageId,
      });
    if (fotocasa.Ascensor_CASA !== void 0)
      result.push({
        FeatureId: 171, // CASA
        BoolValue: fotocasa.Ascensor_CASA,
        LanguageId,
      });
    if (fotocasa.AñoConstrucción !== void 0)
      result.push({
        FeatureId: 231, // PISO, CASA
        DecimalValue: fotocasa.AñoConstrucción,
        LanguageId,
      });
    if (fotocasa.Conservación !== void 0)
      result.push({
        FeatureId: 249, // PISO, CASA
        DecimalValue: fotocasa.Conservación,
        LanguageId,
      });
    if (fotocasa.AireAcondicionado !== void 0)
      result.push({
        FeatureId: 254, // PISO, CASA
        BoolValue: fotocasa.AireAcondicionado,
        LanguageId,
      });
    if (fotocasa.Lavadero !== void 0)
      result.push({
        FeatureId: 257, // PISO, CASA
        BoolValue: fotocasa.Lavadero,
        LanguageId,
      });
    if (fotocasa.Armarios !== void 0)
      result.push({
        FeatureId: 258, // PISO, CASA
        BoolValue: fotocasa.Armarios,
        LanguageId,
      });
    if (fotocasa.Electrodomésticos !== void 0)
      result.push({
        FeatureId: 259, // PISO, CASA
        BoolValue: fotocasa.Electrodomésticos,
        LanguageId,
      });
    if (fotocasa.Suite_con_baño !== void 0)
      result.push({
        FeatureId: 260, // PISO, CASA
        BoolValue: fotocasa.Suite_con_baño,
        LanguageId,
      });
    if (fotocasa.Patio !== void 0)
      result.push({
        FeatureId: 263, // PISO, CASA
        BoolValue: fotocasa.Patio,
        LanguageId,
      });
    if (fotocasa.Cuarto_de_lavado_y_plancha !== void 0)
      result.push({
        FeatureId: 281, // PISO, CASA
        BoolValue: fotocasa.Cuarto_de_lavado_y_plancha,
        LanguageId,
      });
    if (fotocasa.Vistas_a_la_montaña !== void 0)
      result.push({
        FeatureId: 285, // PISO, CASA
        BoolValue: fotocasa.Vistas_a_la_montaña,
        LanguageId,
      });
    if (fotocasa.Cocina_office !== void 0)
      result.push({
        FeatureId: 289, // PISO, CASA
        BoolValue: fotocasa.Cocina_office,
        LanguageId,
      });
    if (fotocasa.Parquet !== void 0)
      result.push({
        FeatureId: 290, // PISO, CASA
        BoolValue: fotocasa.Parquet,
        LanguageId,
      });
    if (fotocasa.Puerta_blindada !== void 0)
      result.push({
        FeatureId: 294, // PISO, CASA
        BoolValue: fotocasa.Puerta_blindada,
        LanguageId,
      });
    if (fotocasa.Gres_Cerámica !== void 0)
      result.push({
        FeatureId: 295, // PISO, CASA
        BoolValue: fotocasa.Gres_Cerámica,
        LanguageId,
      });
    if (fotocasa.Calefacción !== void 0)
      result.push({
        FeatureId: 296, // PISO, CASA
        BoolValue: fotocasa.Calefacción,
        LanguageId,
      });
    if (fotocasa.Balcones !== void 0)
      result.push({
        FeatureId: 297, // PISO, CASA
        BoolValue: fotocasa.Balcones,
        LanguageId,
      });
    if (fotocasa.Jardín_privado !== void 0)
      result.push({
        FeatureId: 298, // PISO, CASA
        BoolValue: fotocasa.Jardín_privado,
        LanguageId,
      });
    if (fotocasa.Piscina_comunitaria !== void 0)
      result.push({
        FeatureId: 300, // PISO, CASA
        BoolValue: fotocasa.Piscina_comunitaria,
        LanguageId,
      });
    if (fotocasa.Zona_comunitaria !== void 0)
      result.push({
        FeatureId: 301, // PISO, CASA
        BoolValue: fotocasa.Zona_comunitaria,
        LanguageId,
      });
    if (fotocasa.Zona_deportiva !== void 0)
      result.push({
        FeatureId: 302, // PISO, CASA
        BoolValue: fotocasa.Zona_deportiva,
        LanguageId,
      });
    if (fotocasa.Zona_infantil !== void 0)
      result.push({
        FeatureId: 303, // PISO,CASA
        BoolValue: fotocasa.Zona_infantil,
        LanguageId,
      });
    if (fotocasa.Energía_solar !== void 0)
      result.push({
        FeatureId: 304, // PISO,CASA
        BoolValue: fotocasa.Energía_solar,
        LanguageId,
      });
    if (fotocasa.Párking_comunitario !== void 0)
      result.push({
        FeatureId: 305, // PISO, CASA
        BoolValue: fotocasa.Párking_comunitario,
        LanguageId,
      });
    if (fotocasa.Conserje !== void 0)
      result.push({
        FeatureId: 306, // PISO, CASA
        BoolValue: fotocasa.Conserje,
        LanguageId,
      });
    if (fotocasa.Vídeo_portero !== void 0)
      result.push({
        FeatureId: 307, // PISO, CASA
        BoolValue: fotocasa.Vídeo_portero,
        LanguageId,
      });
    if (fotocasa.Ascensor_interior !== void 0)
      result.push({
        FeatureId: 308, // PISO, CASA
        BoolValue: fotocasa.Ascensor_interior,
        LanguageId,
      });
    if (fotocasa.Gimnasio !== void 0)
      result.push({
        FeatureId: 309, // PISO, CASA
        BoolValue: fotocasa.Gimnasio,
        LanguageId,
      });
    if (fotocasa.Pista_de_tenis !== void 0)
      result.push({
        FeatureId: 310, // PISO, CASA
        BoolValue: fotocasa.Pista_de_tenis,
        LanguageId,
      });
    if (fotocasa.Se_aceptan_mascotas !== void 0)
      result.push({
        FeatureId: 313, // PISO, CASA
        BoolValue: fotocasa.Se_aceptan_mascotas,
        LanguageId,
      });
    if (fotocasa.Cocina_equipada !== void 0)
      result.push({
        FeatureId: 314, // PISO, CASA
        BoolValue: fotocasa.Cocina_equipada,
        LanguageId,
      });
    if (fotocasa.Con_vistas_al_mar !== void 0)
      result.push({
        FeatureId: 315, // PISO, CASA
        BoolValue: fotocasa.Con_vistas_al_mar,
        LanguageId,
      });
    if (fotocasa.No_amueblado !== void 0)
      result.push({
        FeatureId: 316, // PISO, CASA
        BoolValue: fotocasa.No_amueblado,
        LanguageId,
      });
    if (fotocasa.Agua_caliente_sanitaria !== void 0)
      result.push({
        FeatureId: 321, // PISO, CASA
        DecimalValue: fotocasa.Agua_caliente_sanitaria,
        LanguageId,
      });
    if (fotocasa.Escala_eficiencia_consumo !== void 0)
      result.push({
        FeatureId: 323, // PISO, CASA
        DecimalValue: fotocasa.Escala_eficiencia_consumo,
        LanguageId,
      });
    if (fotocasa.Escala_eficiencia_emisiones !== void 0)
      result.push({
        FeatureId: 324, // PISO, CASA
        DecimalValue: fotocasa.Escala_eficiencia_emisiones,
        LanguageId,
      });
    if (fotocasa.Valor_eficiencia_consumo !== void 0)
      result.push({
        FeatureId: 325, // PISO, CASA
        DecimalValue: fotocasa.Valor_eficiencia_consumo,
        LanguageId,
      });
    if (fotocasa.Valor_eviciencia_emisiones !== void 0)
      result.push({
        FeatureId: 326, // PISO, CASA
        DecimalValue: fotocasa.Valor_eviciencia_emisiones,
        LanguageId,
      });
    if (fotocasa.Certificado_energético !== void 0)
      result.push({
        FeatureId: 327, // PISO, CASA
        DecimalValue: fotocasa.Certificado_energético,
        LanguageId,
      });
    if (fotocasa.Calefacción !== void 0)
      result.push({
        FeatureId: 29, // PISO, CASA
        BoolValue: fotocasa.Calefacción,
        LanguageId,
      });
    if (extras.Núm_habitaciones_dobles !== void 0)
      result.push({
        FeatureId: 42, // PISO, CASA
        DecimalValue: extras.Núm_habitaciones_dobles,
        LanguageId,
      });
    if (extras.Núm_habitaciones_individuales !== void 0)
      result.push({
        FeatureId: 43, // PISO, CASA
        DecimalValue: extras.Núm_habitaciones_individuales,
        LanguageId,
      });
    if (extras.Núm_suites !== void 0)
      result.push({
        FeatureId: 44, // PISO, CASA
        DecimalValue: extras.Núm_suites,
        LanguageId,
      });
    if (extras.Núm_plantas !== void 0)
      result.push({
        FeatureId: 45, // CASA
        DecimalValue: extras.Núm_plantas,
        LanguageId,
      });
    if (extras.AlturaReal !== void 0)
      result.push({
        FeatureId: 66, // PISO
        DecimalValue: extras.AlturaReal,
        LanguageId,
      });
    if (extras.Superficie_edificada !== void 0)
      result.push({
        FeatureId: 70, // PISO, CASA
        DecimalValue: extras.Superficie_edificada,
        LanguageId,
      });
    if (extras.Carpintería_exterior !== void 0)
      result.push({
        FeatureId: 98, // PISO, CASA
        DecimalValue: extras.Carpintería_exterior,
        LanguageId,
      });
    if (extras.Puerta_principal !== void 0)
      result.push({
        FeatureId: 101, // PISO, CASA
        DecimalValue: extras.Puerta_principal,
        LanguageId,
      });
    if (extras.Suelos !== void 0)
      result.push({
        FeatureId: 110, // PISO, CASA
        DecimalValue: extras.Suelos,
        LanguageId,
      });
    if (extras.Agua !== void 0)
      result.push({
        FeatureId: 123, // PISO, CASA
        BoolValue: extras.Agua,
        LanguageId,
      });
    if (extras.Gas !== void 0)
      result.push({
        FeatureId: 124, // PISO, CASA
        BoolValue: extras.Gas,
        LanguageId,
      });
    if (extras.Agua_caliente !== void 0)
      result.push({
        FeatureId: 126, // PISO, CASA
        DecimalValue: extras.Agua_caliente,
        LanguageId,
      });
    if (extras.Refrigeración !== void 0)
      result.push({
        FeatureId: 129, // PISO, CASA
        DecimalValue: extras.Refrigeración,
        LanguageId,
      });
    if (extras.Sol !== void 0)
      result.push({
        FeatureId: 199, // PISO, CASA
        DecimalValue: extras.Sol,
        LanguageId,
      });
    if (extras.Alarma !== void 0)
      result.push({
        FeatureId: 235, // PISO, CASA
        BoolValue: extras.Alarma,
        LanguageId,
      });
    if (extras.Calefacción_opt !== void 0)
      result.push({
        FeatureId: 320, // PISO, CASA
        DecimalValue: extras.Calefacción_opt,
        LanguageId,
      });
    return result;
  }




  export const enum DIC_FeatureId {
    Superficie = 1,
    DescripciónBreve = 2,
    DescripcionExtendida = 3,

  }

  export const enum DIC_Orientacion {
    Noreste = 1,
    Oeste = 2,
    Norte = 3,
    Suroeste = 4,
    Este = 5,
    Sureste = 6,
    Noroeste = 7,
    Sur = 8,
  }

  export const enum DIC_Conservación {
    Buena = 1,
    Muy_buena = 2,
    Excelente = 3,
    Regular = 4,
    Necesita_reforma = 5,
  }
  export const enum DIC_AguaCalienteSanitaria {
    Gas_natural = 1,
    Electricidad = 2,
    Gasóleo = 3,
    Butano = 4,
    Propano = 5,
    Solar = 6,
  }
  export const enum DIC_EscalaEficienciaConsumo {
    A = 1,
    B = 2,
    C = 3,
    D = 4,
    E = 5,
    F = 6,
    G = 7,
  }
  export const enum DIC_EscalaEficienciaEmisiones {
    A = 1,
    B = 2,
    C = 3,
    D = 4,
    E = 5,
    F = 6,
    G = 7,
  }
  export const enum DIC_CertificadoEnergético {
    Si = 1,
    En_trámite = 2,
    Exento = 3,
  }
  export const enum DIC_CarpinteriaExterior {
    aluminio = 1,
    aluminio_lacado = 2,
    hierro = 3,
    madera = 4,
    madera_barnizada = 5,
    madera_noble = 6,
    madera_pintada = 7,
    madera_teka = 8,
    pvc = 9,
  }
  export const enum DIC_PuertaPrincipal {
    de_cuarterones = 2,
    de_hierro = 3,
    de_seguridad = 4,
    de_vidrio = 5,
    enrejada = 6,
    maciza = 7,
    madera = 8,
    mixta = 9,
    normal = 10,
    reforzada = 11,
  }
  export const enum DIC_Suelo {
    baldosa = 1,
    baldosa_rústica = 2,
    cerámico = 3,
    corcho = 4,
    de_gres = 5,
    de_madera = 6,
    de_mármol = 7,
    de_tarima = 9,
    de_terrazo = 10,
    de_gresite = 11,
    linóleo = 12,
    moqueta = 13,
    mosaico = 14,
    porcelanato = 15,
  }


  export const enum DIC_AguaCaliente {
    Gas_butano = 1,
    Gas_natural = 2,
    Gas_propano = 3,
    No_tiene_gas = 4,
    Termo_eléctrico = 5,
  }
  export const enum DIC_Refrigeración {
    Aacc_central = 1,
    Aacc_consolas = 2,
    Aacc_frío_calor = 3,
    Aacc_solo_frío = 4,
  }
  export const enum DIC_Sol {
    Muy_luminoso = 1,
    Sol_mañanas = 2,
    Sol_tardes = 3,
    Sol_todo_el_día = 4,
    Soleado = 5,
  }
  export const enum DIC_Calefacción {
    Gas_natural = 1,
    Electricidad = 2,
    Gasóleo = 3,
    Butano = 4,
    Propano = 5,
    Solar = 6,
  }

}