
export namespace pisos_com {

  /*
  // Tipo publicación en Pisos.com
  */
  export const enum TPublicationId {
    pisos = 1,  // por defecto debe ser siempre este
    microsite = 2,
    piso_compartido = 3
  }

  export const enum TSales {
    alquiler_vacacional = 1,
    alquiler_opcion_compra = 2,
    alquiler = 3,
    venta = 4,
  }
  export const enum TPeriodicity {
    dia = 1,
    semana = 2,
    mes = 3,
  }

  export const enum TFamiliaInmueble {
    pisos = 3,
    casas = 4,
    suelo = 5,
    comercial_industrial = 6,
    oficina = 7,
    parking = 8,
    habitacion = 11,
  }
  export const enum TCasasCategoria {
    torre = 2,
    casa = 8,
    chalet = 11,
    masia = 17,
    finca_rustica = 22,
    cortijo = 23,
    casa_de_campo = 26,
    casa_unifamiliar = 30,
    casa_adosada = 31,
    bungalow = 37,
    casa_cueva = 38,
    casa_pareada = 41,
    casa_rustica = 43,
    chalet_adosado = 44,
    chalet_pareado = 45,
    chalet_unifamiliar = 46,
    chalet_rustico = 47,
  }
  export const enum TPisosCategoria {
    piso = 1,
    duplex = 10,
    apartamento = 13,
    atico = 15,
    triplex = 25,
    loft = 32,
    estudio = 39,
  }
  export const enum TInmueblesCategoria {  // todos los de habitatsoft
    piso = 1,
    torre = 2,
    parking = 3,
    local_comercial = 4,
    nave_industrial = 5,
    almacen = 6,
    despacho = 7,
    casa = 8,
    solar = 9,
    duplex = 10,
    chalet = 11,
    oficina = 12,
    apartamento = 13,
    edificio = 14,
    atico = 15,
    masia = 17,
    finca_rustica = 22,
    cortijo = 23,
    garaje = 24,
    triplex = 25,
    casa_de_campo = 26,
    terreno = 28,
    parcela = 29,
    casa_unifamiliar = 30,
    casa_adosada = 31,
    loft = 32,
    restaurante = 33,
    trastero = 34,
    terreno_industrial = 35,
    parcela_industrial = 36,
    bungalow = 37,
    casa_cueva = 38,
    estudio = 39,
    habitacion = 40,
    casa_pareada = 41,
    nave_comercial = 42,
    casa_rustica = 43,
    chalet_adosado = 44,
    chalet_pareado = 45,
    chalet_unifamiliar = 46,
    chalet_rustico = 47,
  }

  /*
  // Tipos de Vía pisos.com
  */
  export const enum TStreetTypeId {
    avenida = 1,
    calle = 2,
    camino = 3,
    carretera = 5,
    grupo = 7,
    parque = 10,
    pasaje = 11,
    poligono = 13,
    plaza = 14,
    paseo = 16,
    subida = 17,
    rambla = 19,
    ronda = 20,
    urbanizacion = 22,
    callejon = 101,
    prolongacion = 102,
    rotonda = 104,
    travesia = 105,
    via = 106,
    alameda = 107,
    bajada = 108,
    cuesta = 109,
    glorieta = 110,
    riera = 111,
    sector = 112,
  }
  /*
  // Tipos numeración vía
  */
  export const enum TStreetNumberTypeId {
    kilometro = 1,
    numero = 2,
    parcela = 3,
    bloque = 13,
    parada = 14,
    torre = 15,
    nave = 16,
  }

  /*
  // Nivel detalle dirección
  */
  export const enum TShowAddressTypeId {
    ubicacion_aproximada = 1,
    ubicacion_nivel_calle = 2,
    ubicacion_nivel_calle_numero = 3,
  }

  /*
  // Códigos de país
  */
  export const enum TCountryId {
    españa = 1,
    francia = 2,
    andorra = 3,
  }

  export const enum TCondicion { // Estado
    a_estrenar = "1",
    en_buen_estado = "2",
    a_reformar = "3",
    reformado = "4",
  };

  export const enum TCalefaccion {
    central = "1",
    electrica = "2",
    gas_natural = "3",
    gasoil = "4",
    si = "5",
  }

  export const enum TAireAcondicionado {
    frio = "1",
    frio_y_calor = "2",
    calor = "3",
  }

  /*
  // Etiqueta exclusivo
  */
  export const enum TExclusiveLabelId {
    ultimas_viviendas = 1,
    oportunidad = 2,
    rebajado = 3,
    urge_vender = 4,
    urge_alquilar = 5,
    llave_en_mano = 6,
    vistas_al_mar = 7,
    preciosas_vistas = 8,
    alquiler_opcion_compra = 9,
    lujo = 10,
    centrico = 11,
    amueblado = 12,
    vivienda_VPO = 13,
    ultima_vivienda = 14,
    oferta = 15,
    calidades_superiores = 16,
    entrega_inmediata = 17,
    visite_piso_piloto = 18,
    viviendas_loft = 19,
    oferta_sima = 20,
    diseño_exclusivo = 22,
    exclusivo = 23,
    rehabibilitado = 24,
    oferta_SIE = 25,
    apto_RIC = 26,
    financiacion_100 = 27,
    financiacion_a_medida = 28,
    visitalo = 29,
    en_construccion = 30,
  }

  export declare type PropertyProduct = {
    prominentProduct?: number,    // 0-1, nullable
    exclusiveProduct?: number,    // 0-1, nullable
    exclusiveLabelId?: TExclusiveLabelId, // 1-30, nullable, example 2 (oportunidad)
  }

  /*
  // Tipos de media
  */
  export const enum TMediaTypeId {
    fotografia = 1,
    video_externo = 3,
    documento = 4,
    tour_virtual = 5,
    foto_dual = 6,
  }

  /*
  // Subtipos de media
  */
  export declare type MediaFormatId = {
    name: string,
    mediaTypeId: TMediaTypeId, // [1,3,6]
  }

  export const enum TMediaFormatId {
    fotografia = 0,
    planos = 2,
    memoria_calidades = 3,
    folleto = 4,
    mapa_situacion = 5,
    datos_financiacion = 6,
    certificacion_energetica = 8,
    visita_virtual = 9,
  }

  /*
  // Estados media
  */
  export const enum TMediaStatusId {
    inactivo = 0,
    activo = 1,
    moderado = 2,
  }

  /*
  // Etiquetas media
  */
  export const enum TLabelMediaId {
    aseo = 1,
    cocina = 2,
    salon = 3,
    hall = 4,
    terraza = 5,
    fachada = 6,
    plano = 7,
    banyo = 8,
    dormitorio = 9,
    pasillo = 10,
    patio = 11,
    comedor = 12,
    buhardilla = 13,
    despacho = 14,
    sala = 15,
    recepcion = 16,
    archivo = 17,
    entrada_salida = 18,
    piscina = 19,
    zonas_comunes = 20,
    garaje_parking = 21,
    trastero = 22,
    escalera = 23,
    jardin = 24,
    vistas = 25,
    entorno = 26,
    porteria = 27,
    detalles = 28,
    certificado_energetico = 29,
  }

  export const enum TCarpinteria {
    madera = "1",
    aluminio = "2",
    PVC = "3",
  }

  export const enum TJardin {
    propio = "1",
    comunitario = "2",
    si = "3",
  }

  export const enum TPiscina {
    propia = "1",
    comunitaria = "2",
    si = "3",
  }

  export const enum TSuministroElectrico {
    union_fenosa = "1",
    iberdrola = "2",
    endesa = "3",
    otra_companyia = "4",
    sin_luz = "5",
  }
  export const enum TSuministroGas {
    gas_natural = "1",
    iberdrola = "2",
    endesa = "3",
    otra_companyia = "4",
    sin_gas = "5",
  }
  export const enum TTipoHabitacion {
    individual = "1",
    doble = "2",
  }
  export const enum TTamanyoHabitacion {
    pequenya = "1", // "Pequeña (4-8 m²)"
    mediana = "2", // "Mediana (8-12 m²)"
    grande = "3", // "Grande (>12 m²)"
  }
  export const enum TNumeroInquilinos {
    inquilinos1 = "1",
    inquilinos2 = "2",
    inquilinos3 = "3",
    inquilinos4 = "4",
  }

  export const enum TGeneroInquilinos {
    femenino = "1",
    masculino = "2",
    mixto = "3",
  }

  export const enum TCaracteristicas {
    descripcion = 2,  // obsoleta.  se informa en PropertyDescription
    habitaciones_totales = 3,  // int
    habitaciones_dobles = 4,  // int
    habitaciones_simples = 5,  // int
    banyos_totales = 6,  // int
    banyos_completos = 7,  // int
    banyos_auxiliares = 8,  // int
    superficie_construida = 9,  // float
    superficie_util = 10, // float
    superficie_solar = 11, // float
    numero_vecinos = 12, // int
    certificado_num_registro = 13, // string
    energia_consumo_categoria = 14, // TEnergia, int
    energia_consumo_valor = 15, // float
    energia_emision_categoria = 16, // TEnergia, int
    energia_emision_valor = 17, // float 
    codigo_vivienda_turistica = 18, // string
    estado_conservacion = 19, // TCondicion, int
    anyo_construccion = 20, // int
    comedor = 21, // bool/text
    cocina = 22, // bool/text
    lavadero = 23, // bool/text
    trastero = 24, // bool/text
    ascensor = 25, // bool/text
    balcon = 26, // bool/text
    terraza = 27, // bool/text
    suelo = 28, // integer/text
    armarios_empotrados = 29, // integer/text
    amueblado = 30, // bool/text
    calefaccion = 31, // TCalefaccion, integer/text
    aire_acondicionado = 32, // TAireAcondicionado, integer/text
    vidrios_dobles = 33, // bool/text
    carpinteria_interior = 34, // bool/text
    carpinteria_exterior = 35, // TCarpinteria, integer/text, 1-3
    chimenea = 36, // bool/text
    altillo = 37, // bool/text
    puerta_blindada = 38, // bool/text
    portero_automatico = 39, // bool/text
    sistema_seguridad = 40, // bool/text
    garaje = 41, // integer/text
    jardin = 42, // TJardin, integer/text, 1-3
    piscina = 43, // TPiscina, integer/text, 1-3
    accesibilidad = 44, // bool/text
    exterior = 45, // bool/text
    interior = 46, // bool/text
    orientacion = 47, // TOrientacion, integer/text, 1-10
    soleado = 48, // bool/text
    gastos_comunidad = 49, // integer
    suministro_agua = 50, // bool/text
    suministro_electrico = 51, // TSuministroElectrico, integer/text
    suministro_gas = 52, // TSuministroGas, integer/text
    suministro_telefono = 53, // bool/text
    subterraneo = 54, // bool/text
    cerrado = 55, // bool/text
    divisiones = 56, // bool/text
    columnas = 57, // bool/text
    vallado = 58, // bool/text
    cultivable = 59, // bool/text
    arboles = 60, // bool/text
    edificaciones = 61, // bool/text
    edificable = 62, // bool/text
    alcantarillado = 63, // bool/text
    urbanizado = 64, // bool/text
    asfaltado = 65, // bool/text
    alumbrado = 66, // bool/text
    muelle_de_carga = 67, // bool/text
    grua = 68, // bool/text
    aislantes = 69, // bool/text
    oficina = 70, // bool/text
    salida_humos = 71, // bool/text
    cubierta = 72, // bool/text
    salida_antiincendios = 73, // bool/text
    instalacion_red = 74, // bool/text
    puertas_acceso = 75, // bool/text
    escaparate = 76, // bool/text
    planta_baja = 77, // bool/text
    caja_fuerte = 78, // bool/text
    vestuarios = 79, // bool/text
    habitaciones_suites_dobles = 80, // integer
    ancho = 81, // integer
    alto = 82, // integer
    fondo = 83, // integer
    fondo_izquierdo = 84, // integer
    fondo_derecho = 85, // integer
    plazas = 86, // integer
    altura_piso = 87, // integer
    superficie_edificada = 88, // float
    se_aceptan_mascotas = 89, // bool/text
    adaptado_discapacitados = 90, // bool/text
    indice_referencia_precios = 91, // float
    tipo_habitación = 92, // TTipoHabitacion, integer/text, 1-2
    tamanyo_habitacion = 93, // TTamanyoHabitacion, integer/text, 1-3
    numero_inquilinos = 94, // TNumeroInquilinos, iteger/text, 1-4
    genero_inquilinos = 95, // TGeneroInquilinos, integer/text, 1-3
    se_permite_fumar = 96, // bool/text
    permitido_parejas = 97, // bool/text
    banyo_propio = 98, // bool/text
    gastos_incluidos = 99, // bool/text
    acceso_internet = 100, // bool/text
  }

  export declare type PropertyMedia = {
    mediaTypeId: TMediaTypeId,     //* 1-6, example: 1 (fotografía) 
    mediaFormatId: TMediaFormatId, //* 0-9
    order: number,                 //*
    mediaStatusId: TMediaStatusId, //* 0-2
    descriptionMedia?: string,    // maxLength: 255, nullable
    url: string,                   //* length: 1-255. example: http://mediaURL.com
    urL2?: string,                   //* length: 1-255. example: http://mediaURL.com, nullable
    labelMediaId?: TLabelMediaId,   //* 1-29, nullable, example: 1 (aseo)
  }

  export declare type PropertyContact = {
    email?: string, // length 1-255. nullable. Email of the property
    phone?: string, // length 9-50. nullable. example: 912345678. Phone number of the property
    publicationId?: TPublicationId, // 1-3. nullable. example: 1. Publication id for this contact
  }

  export declare type PropertyTransaction = {
    transactionTypeId: TSales,      //*
    price: number,                  //*
    periodicityId?: TPeriodicity,   // nullable
    publicationId?: TPublicationId, // 1-3. nullable. example: 1. Publication id for this transaction
    hidePrice?: number,             // pattern: [0-1]  nullable
  }

  export declare type PropertyLocation = {
    addressZone?: string,
    townName?: string,
    localityAddress?: string,
    districtAddress?: string,
    neighborhoodAddress?: string,
    streetTypeId?: TStreetTypeId,              // 1-112. nullable. example: 2 (calle)
    streetNumberTypeId?: TStreetNumberTypeId, // 1-16. nullable. example: 2 (numero)
    streetName?: string,                       // maxlength: 50. nullable
    streetNumber?: string,                     // maxlength: 50. nullable
    zipCode?: string,                          // length: 5-8. nullable
    floor?: number,
    showAddressTypeId?: TShowAddressTypeId,     // 1-3. nullable. example: 1 (ubicacion aproximada)
    zone?: string,                             // length: 1-100. nullable
    latitudeCoordinate: number,                //* pattern: ^(\-?\d+(\.\d+)?),\s*(\-?\d+(\.\d+)?)$ . example: 1.111111
    longitudeCoordinate: number,              //* pattern: ^(\-?\d+(\.\d+)?),\s*(\-?\d+(\.\d+)?)$ . example: 1.111111
    countryId?: TCountryId,                    // 1-3. nullable.  example 1 (España)
    townIneId?: string,                        // nullable
  }

  export declare type PropertyFeature = {
    featureId: TCaracteristicas,  //*
    value: string,      //* minLength: 1
    textValue?: string,
  }

  export declare type PropertyDescription = {
    language: string,     // length: 1-2.  example: es
    description?: string  // nullable    "type": "integer",
  }

  /**
  * Estructura de Property de pisos.com
  */
  export declare type Property = {
    externalId: string, // * length: 1-50. Unique internal id of the property for the agency
    agencyId: string,   // * length 1-30. example: SA3. External Id of the Agency
    reference: string,  // * length 1-50. example: REF001. Unique reference of the property from the agency
    propertyTypeId: TInmueblesCategoria, // * 1-47. example: 1.	Id of the property type
    propertyContact?: PropertyContact[], // PropertyContact
    propertyTransactions: PropertyTransaction[], //*
    propertyLocation: PropertyLocation,  //*
    propertyProducts?: PropertyProduct,
    propertyFeatures?: PropertyFeature[],
    propertyMedias?: PropertyMedia[],
    propertyDescriptions?: PropertyDescription[],
  }


  const toFeature = (featureId: TCaracteristicas, value: string): PropertyFeature => ({ featureId, value });
  const toIntFeature = (featureId: TCaracteristicas, value: number) => toFeature(featureId, value.toFixed().toString());
  const toBoolFeature = (featureId: TCaracteristicas, value: boolean) => toFeature(featureId, value ? "true" : "false");
  const toFloatFeature = (featureId: TCaracteristicas, value: number) => toFeature(featureId, value.toString());
  export const featuresFactory = {


    //   "2": {
    //     "name": "Descripción",
    //     "groupFamily": "[3,4,5,6,7,8,11]",
    //     "buildingType": "[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,22,23,24,25,26,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47]",
    //     "type": "string"
    //   },
    //   "3": {
    //     "name": "Habitaciones Totales",
    //     "groupFamily": "[3,4,5,11]",
    //     "buildingType": "[1,2,8,9,10,11,13,15,17,22,23,25,26,28,29,30,31,32,35,36,37,38,39,40,41,43,44,45,46,47]",
    //     "type": "integer"
    //   },
    habitacionesTotales: (value: number) => toIntFeature(TCaracteristicas.habitaciones_totales, value),
    //   "4": {
    //     "name": "Habitaciones Dobles",
    //     "groupFamily": "[3,4,5,11]",
    //     "buildingType": "[1,2,8,9,10,11,13,15,17,22,23,25,26,28,29,30,31,32,35,36,37,38,39,40,41,43,44,45,46,47]",
    //     "type": "integer"
    //   },
    habitacionesDobles: (value: number) => toIntFeature(TCaracteristicas.habitaciones_dobles, value),
    //   "5": {
    //     "name": "Habitaciones Simples",
    //     "groupFamily": "[3,4,5,11]",
    //     "buildingType": "[1,2,8,9,10,11,13,15,17,22,23,25,26,28,29,30,31,32,35,36,37,38,39,40,41,43,44,45,46,47]",
    //     "type": "integer"
    //   },
    habitacionesSimples: (value: number) => toIntFeature(TCaracteristicas.habitaciones_simples, value),
    //   "6": {
    //     "name": "Baños Totales",
    //     "groupFamily": "[3,4,5,6,7,11]",
    //     "buildingType": "[1,2,4,5,6,7,8,9,10,11,12,13,14,15,17,22,23,25,26,28,29,30,31,32,33,35,36,37,38,39,40,41,42,43,44,45,46,47]",
    //     "type": "integer"
    //   },
    bañosTotales: (value: number) => toIntFeature(TCaracteristicas.banyos_totales, value),
    //   "7": {
    //     "name": "Baños Completos",
    //     "groupFamily": "[3,4,5,6,7,11]",
    //     "buildingType": "[1,2,4,5,6,7,8,9,10,11,12,13,14,15,17,22,23,25,26,28,29,30,31,32,33,35,36,37,38,39,40,41,42,43,44,45,46,47]",
    //     "type": "integer"
    //   },
    bañosCompletos: (value: number) => toIntFeature(TCaracteristicas.banyos_completos, value),
    //   "8": {
    //     "name": "Baños Auxiliares",
    //     "groupFamily": "[3,4,5,6,7,11]",
    //     "buildingType": "[1,2,4,5,6,7,8,9,10,11,12,13,14,15,17,22,23,25,26,28,29,30,31,32,33,35,36,37,38,39,40,41,42,43,44,45,46,47]",
    //     "type": "integer"
    //   },
    bañosAuxiliares: (value: number) => toIntFeature(TCaracteristicas.banyos_auxiliares, value),
    //   "9": {
    //     "name": "Superficie Construida",
    //     "groupFamily": "[3,4,5,6,7,8,11]",
    //     "buildingType": "[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,22,23,24,25,26,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47]",
    //     "type": "float"
    //   },
    superficieConstruida: (value: number) => toFloatFeature(TCaracteristicas.superficie_construida, value),
    //   "10": {
    //     "name": "Superficie Útil",
    //     "groupFamily": "[3,4,5,11]",
    //     "buildingType": "[1,2,8,9,10,11,13,15,17,22,23,25,26,28,29,30,31,32,35,36,37,38,39,40,41,43,44,45,46,47]",
    //     "type": "float"
    //   },
    superficieUtil: (value: number) => toFloatFeature(TCaracteristicas.superficie_util, value),
    //   "11": {
    //     "name": "Superficie Solar",
    //     "groupFamily": "[4,5,6,7]",
    //     "buildingType": "[2,4,5,6,7,8,9,11,12,14,17,22,23,26,28,29,30,31,33,35,36,37,38,41,42,43,44,45,46,47]",
    //     "type": "float"
    //   },
    superficieSolar: (value: number) => toFloatFeature(TCaracteristicas.superficie_solar, value),
    //   "12": {
    //     "name": "Número Vecinos",
    //     "groupFamily": "[2,5,6,7,8,11]",
    //     "buildingType": "[3,4,5,6,7,9,12,14,17,22,23,24,26,28,29,33,34,35,36,40,42]",
    //     "type": "integer"
    //   },
    numeroVecinos: (value: number) => toIntFeature(TCaracteristicas.numero_vecinos, value),
    //   "13": {
    //     "name": "Certificado Núm Registro",
    //     "groupFamily": "[3,4,5,6,7,8,11]",
    //     "buildingType": "[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,22,23,24,25,26,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47]",
    //     "type": "string"
    //   },
    certificadoNumRegistro: (value: string) => toFeature(TCaracteristicas.certificado_num_registro, value),
    //   "14": {
    //     "name": "Energía Consumo Categoría",
    //     "groupFamily": "[3,4,5,6,7,8,11]",
    //     "buildingType": "[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,22,23,24,25,26,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47]",
    //     "type": "integer",
    //     "valueList": {
    //       "1": "A",
    //       "2": "B",
    //       "3": "C",
    //       "4": "D",
    //       "5": "E",
    //       "6": "F",
    //       "7": "G",
    //       "8": "Exento",
    //       "9": "En trámite"
    //     }
    //   },
    energiaConsumo: (value: TEnergia) => toFeature(TCaracteristicas.energia_consumo_categoria, value),
    //   "15": {
    //     "name": "Energía Consumo Valor",
    //     "groupFamily": "[3,4,5,6,7,8,11]",
    //     "buildingType": "[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,22,23,24,25,26,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47]",
    //     "type": "float"
    //   },
    //   "16": {
    //     "name": "Energía Emisión Categoría",
    //     "groupFamily": "[3,4,5,6,7,8,11]",
    //     "buildingType": "[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,22,23,24,25,26,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47]",
    //     "type": "integer",
    //     "valueList": {
    //       "1": "A",
    //       "2": "B",
    //       "3": "C",
    //       "4": "D",
    //       "5": "E",
    //       "6": "F",
    //       "7": "G",
    //       "8": "Exento",
    //       "9": "En trámite"
    //     }
    //   },
    energiaEmisiones: (value: TEnergia) => toFeature(TCaracteristicas.energia_emision_categoria, value),
    //   "17": {
    //     "name": "Energía Emisión Valor",
    //     "groupFamily": "[3,4,5,6,7,8,11]",
    //     "buildingType": "[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,22,23,24,25,26,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47]",
    //     "type": "float"
    //   },
    //   "18": {
    //     "name": "Código Vivienda Turística",
    //     "groupFamily": "[3,4,5]",
    //     "buildingType": "[1,2,8,9,10,11,13,15,17,22,23,25,26,28,29,30,31,32,35,36,37,38,39,41,43,44,45,46,47]",
    //     "type": "string"
    //   },
    //   "19": {
    //     "name": "Estado Conservación",
    //     "groupFamily": "[3,4,5,6,7,8,11]",
    //     "buildingType": "[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,22,23,24,25,26,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47]",
    //     "type": "integer",
    //     "valueList": {
    //       "1": "A estrenar",
    //       "2": "En buen estado",
    //       "3": "A reformar",
    //       "4": "Reformado"
    //     }
    //   },
    estadoConservacion: (valor: TCondicion) => toFeature(TCaracteristicas.estado_conservacion, valor),
    //   "20": {
    //     "name": "Año Construcción",
    //     "groupFamily": "[3,4,7,11]",
    //     "buildingType": "[1,2,4,6,7,8,10,11,12,13,14,15,25,30,31,32,33,37,38,39,40,41,43,44,45,46,47]",
    //     "type": "integer"
    //   },
    añoConstruccion: (valor: number) => toIntFeature(TCaracteristicas.anyo_construccion, valor),
    //   "21": {
    //     "name": "Comedor",
    //     "groupFamily": "[3,4,5,11]",
    //     "buildingType": "[1,2,8,9,10,11,13,15,17,22,23,25,26,28,29,30,31,32,35,36,37,38,39,40,41,43,44,45,46,47]",
    //     "type": "bool/text"
    //   },
    comedor: (valor: boolean) => toBoolFeature(TCaracteristicas.comedor, valor),
    //   "22": {
    //     "name": "Cocina",
    //     "groupFamily": "[3,4,5,11]",
    //     "buildingType": "[1,2,8,9,10,11,13,15,17,22,23,25,26,28,29,30,31,32,35,36,37,38,39,40,41,43,44,45,46,47]",
    //     "type": "bool/text"
    //   },
    cocina: (valor: boolean) => toBoolFeature(TCaracteristicas.cocina, valor),
    //   "23": {
    //     "name": "Lavadero",
    //     "groupFamily": "[3,4,5,11]",
    //     "buildingType": "[1,2,8,9,10,11,13,15,17,22,23,25,26,28,29,30,31,32,35,36,37,38,39,40,41,43,44,45,46,47]",
    //     "type": "bool/text"
    //   },
    lavadero: (valor: boolean) => toBoolFeature(TCaracteristicas.lavadero, valor),
    //   "24": {
    //     "name": "Trastero",
    //     "groupFamily": "[3,4,6,8]",
    //     "buildingType": "[1,2,3,5,8,10,11,13,15,24,25,30,31,32,34,37,38,39,41,42,43,44,45,46,47]",
    //     "type": "bool/text"
    //   },
    trastero: (valor: boolean) => toBoolFeature(TCaracteristicas.trastero, valor),
    //   "25": {
    //     "name": "Ascensor",
    //     "groupFamily": "[3,7,8,11]",
    //     "buildingType": "[1,3,4,6,7,10,12,13,14,15,24,25,32,33,34,39,40]",
    //     "type": "bool/text"
    //   },
    ascensor: (valor: boolean) => toBoolFeature(TCaracteristicas.ascensor, valor),
    //   "26": {
    //     "name": "Balcón",
    //     "groupFamily": "[3,11]",
    //     "buildingType": "[1,10,13,15,25,32,39,40]",
    //     "type": "bool/text"
    //   },
    balcon: (valor: boolean) => toBoolFeature(TCaracteristicas.balcon, valor),
    //   "27": {
    //     "name": "Terraza",
    //     "groupFamily": "[3,4,5,11]",
    //     "buildingType": "[1,2,8,9,10,11,13,15,17,22,23,25,26,28,29,30,31,32,35,36,37,38,39,40,41,43,44,45,46,47]",
    //     "type": "bool/text"
    //   },
    terraza: (valor: boolean) => toBoolFeature(TCaracteristicas.terraza, valor),
    //   "28": {
    //     "name": "Suelo",
    //     "groupFamily": "[3,4,5]",
    //     "buildingType": "[1,2,8,9,10,11,13,15,17,22,23,25,26,28,29,30,31,32,35,36,37,38,39,41,43,44,45,46,47]",
    //     "type": "integer/text",
    //     "valueList": {
    //       "1": "Gres",
    //       "2": "Mármol",
    //       "3": "Moqueta",
    //       "4": "Parquet",
    //       "5": "Tarima flotante",
    //       "6": "Tarima maciza",
    //       "7": "Terrazo"
    //     }
    //   },
    suelo: (valor:pisos_com.TSuelo) => toFeature(TCaracteristicas.suelo, valor),
    //   "29": {
    //     "name": "Armarios Empotrados",
    //     "groupFamily": "[3,4,5,11]",
    //     "buildingType": "[1,2,8,9,10,11,13,15,17,22,23,25,26,28,29,30,31,32,35,36,37,38,39,40,41,43,44,45,46,47]",
    //     "type": "integer/text"
    //   },
    armarios_empotrados: (valor: number) => toIntFeature(TCaracteristicas.armarios_empotrados, valor),
    //   "30": {
    //     "name": "Amueblado",
    //     "groupFamily": "[3,4,5,11]",
    //     "buildingType": "[1,2,8,9,10,11,13,15,17,22,23,25,26,28,29,30,31,32,35,36,37,38,39,40,41,43,44,45,46,47]",
    //     "type": "bool/text"
    //   },
    amueblado: (valor: boolean) => toBoolFeature(TCaracteristicas.amueblado, valor),
    //   "31": {
    //     "name": "Calefacción",
    //     "groupFamily": "[3,4,5,6,7,11]",
    //     "buildingType": "[1,2,4,5,6,7,8,9,10,11,12,13,14,15,17,22,23,25,26,28,29,30,31,32,33,35,36,37,38,39,40,41,42,43,44,45,46,47]",
    //     "type": "integer/text",
    //     "valueList": {
    //       "1": "Central",
    //       "2": "Eléctrica",
    //       "3": "Gas natural",
    //       "4": "Gasoil",
    //       "5": "Si"
    //     }
    //   },
    calefaccion: (valor: TCalefaccion) => toFeature(TCaracteristicas.calefaccion, valor),
    //   "32": {
    //     "name": "Aire Acondicionado",
    //     "groupFamily": "[3,4,5,6,7,11]",
    //     "buildingType": "[1,2,4,5,6,7,8,9,10,11,12,13,14,15,17,22,23,25,26,28,29,30,31,32,33,35,36,37,38,39,40,41,42,43,44,45,46,47]",
    //     "type": "integer/text",
    //     "valueList": {
    //       "1": "Frío",
    //       "2": "Frío y calor",
    //       "3": "Sí"
    //     }
    //   },
    aire_acondicionado: (valor: TAireAcondicionado) => toFeature(TCaracteristicas.aire_acondicionado, valor),
    //   "33": {
    //     "name": "Vidrios Dobles",
    //     "groupFamily": "[3,4,5,7,11]",
    //     "buildingType": "[1,2,4,6,7,8,9,10,11,12,13,14,15,17,22,23,25,26,28,29,30,31,32,33,35,36,37,38,39,40,41,43,44,45,46,47]",
    //     "type": "bool/text"
    //   },
    vidrios_dobles: (valor: boolean) => toBoolFeature(TCaracteristicas.vidrios_dobles, valor),
    //   "34": {
    //     "name": "Carpintería Interior",
    //     "groupFamily": "[3,4,5,7]",
    //     "buildingType": "[1,2,4,6,7,8,9,10,11,12,13,14,15,17,22,23,25,26,28,29,30,31,32,33,35,36,37,38,39,41,43,44,45,46,47]",
    //     "type": "bool/text"
    //   },
    //   "35": {
    //     "name": "Carpintería Exterior",
    //     "groupFamily": "[3,4,5,7]",
    //     "buildingType": "[1,2,4,6,7,8,9,10,11,12,13,14,15,17,22,23,25,26,28,29,30,31,32,33,35,36,37,38,39,41,43,44,45,46,47]",
    //     "type": "integer/text",
    //     "valueList": {
    //       "1": "Madera",
    //       "2": "Aluminio",
    //       "3": "PVC"
    //     }
    //   },
    carpinteria_exterior: (valor: TCarpinteria) => toFeature(TCaracteristicas.carpinteria_exterior, valor),
    //   "36": {
    //     "name": "Chimenea",
    //     "groupFamily": "[3,4,5]",
    //     "buildingType": "[1,2,8,9,10,11,13,15,17,22,23,25,26,28,29,30,31,32,35,36,37,38,39,41,43,44,45,46,47]",
    //     "type": "bool/text"
    //   },
    chimenea: (valor: boolean) => toBoolFeature(TCaracteristicas.chimenea, valor),
    //   "37": {
    //     "name": "Altillo",
    //     "groupFamily": "[6,7]",
    //     "buildingType": "[4,5,6,7,12,14,33,42]",
    //     "type": "bool/text"
    //   },
    //   "38": {
    //     "name": "Puerta Blindada",
    //     "groupFamily": "[3,4,5,7,11]",
    //     "buildingType": "[1,2,4,6,7,8,9,10,11,12,13,14,15,17,22,23,25,26,28,29,30,31,32,33,35,36,37,38,39,40,41,43,44,45,46,47]",
    //     "type": "bool/text"
    //   },
    puerta_blindada: (valor: boolean) => toBoolFeature(TCaracteristicas.puerta_blindada, valor),
    //   "39": {
    //     "name": "Portero Autómatico",
    //     "groupFamily": "[3,7,11]",
    //     "buildingType": "[1,4,6,7,10,12,13,14,15,25,32,33,39,40]",
    //     "type": "bool/text"
    //   },
    portero_automatico: (valor: boolean) => toBoolFeature(TCaracteristicas.portero_automatico, valor),
    //   "40": {
    //     "name": "Sistema Seguridad",
    //     "groupFamily": "[3,4,5,6,7,8]",
    //     "buildingType": "[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,22,23,24,25,26,28,29,30,31,32,33,34,35,36,37,38,39,41,42,43,44,45,46,47]",
    //     "type": "bool/text"
    //   },
    sistema_seguridad: (valor: boolean) => toBoolFeature(TCaracteristicas.sistema_seguridad, valor),
    //   "41": {
    //     "name": "Garaje",
    //     "groupFamily": "[3,4,5,6,7,11]",
    //     "buildingType": "[1,2,4,5,6,7,8,9,10,11,12,13,14,15,17,22,23,25,26,28,29,30,31,32,33,35,36,37,38,39,40,41,42,43,44,45,46,47]",
    //     "type": "integer/text"
    //   },
    garaje: (valor: boolean) => toBoolFeature(TCaracteristicas.garaje, valor),
    //   "42": {
    //     "name": "Jardín",
    //     "groupFamily": "[3,4,11]",
    //     "buildingType": "[1,2,8,10,11,13,15,25,30,31,32,37,38,39,40,41,43,44,45,46,47]",
    //     "type": "integer/text",
    //     "valueList": {
    //       "1": "Propio",
    //       "2": "Comunitario",
    //       "3": "Sí"
    //     }
    //   },
    jardin: (valor: TJardin) => toFeature(TCaracteristicas.jardin, valor),
    //   "43": {
    //     "name": "Piscina",
    //     "groupFamily": "[3,4,5,11]",
    //     "buildingType": "[1,2,8,9,10,11,13,15,17,22,23,25,26,28,29,30,31,32,35,36,37,38,39,40,41,43,44,45,46,47]",
    //     "type": "integer/text",
    //     "valueList": {
    //       "1": "Propia",
    //       "2": "Comunitaria",
    //       "3": "Si"
    //     }
    //   },
    piscina: (valor: TPiscina) => toFeature(TCaracteristicas.piscina, valor),
    //   "44": {
    //     "name": "Accesibilidad",
    //     "groupFamily": "[5,6,7]",
    //     "buildingType": "[4,5,6,7,9,12,14,17,22,23,26,28,29,33,35,36,42]",
    //     "type": "bool/text"
    //   },
    accesibilidad: (valor: boolean) => toBoolFeature(TCaracteristicas.accesibilidad, valor),
    //   "45": {
    //     "name": "Exterior",
    //     "groupFamily": "[3,4,5,6,7]",
    //     "buildingType": "[1,2,4,5,6,7,8,9,10,11,12,13,14,15,17,22,23,25,26,28,29,30,31,32,33,35,36,37,38,39,41,42,43,44,45,46,47]",
    //     "type": "bool/text"
    //   },
    //   "46": {
    //     "name": "Interior",
    //     "groupFamily": "[3,6,7]",
    //     "buildingType": "[1,4,5,6,7,10,12,13,14,15,25,32,33,39,42]",
    //     "type": "bool/text"
    //   },
    //   "47": {
    //     "name": "Orientación",
    //     "groupFamily": "[3,4,5,7]",
    //     "buildingType": "[1,2,4,6,7,8,9,10,11,12,13,14,15,17,22,23,25,26,28,29,30,31,32,33,35,36,37,38,39,41,43,44,45,46,47]",
    //     "type": "integer/text",
    //     "valueList": {
    //       "1": "Norte",
    //       "2": "Noreste",
    //       "3": "Este",
    //       "4": "Sureste",
    //       "5": "Sur",
    //       "6": "Suroeste",
    //       "7": "Oeste",
    //       "8": "Noroeste",
    //       "9": "Al mar",
    //       "10": "A la montaña"
    //     }
    //   },
    orientacion: (valor:pisos_com.TOrientacion) => toFeature(TCaracteristicas.orientacion, valor),
    //   "48": {
    //     "name": "Soleado",
    //     "groupFamily": 3,
    //     "buildingType": "[1,10,13,15,25,32,39]",
    //     "type": "bool/text"
    //   },
    soleado: (valor: boolean) => toBoolFeature(TCaracteristicas.soleado, valor),
    //   "49": {
    //     "name": "Gastos Comunidad",
    //     "groupFamily": "[3,4,7]",
    //     "buildingType": "[1,2,4,6,7,8,10,11,12,13,14,15,25,30,31,32,33,37,38,39,41,43,44,45,46,47]",
    //     "type": "integer"
    //   },
    gastos_comunidad: (valor: number) => toIntFeature(TCaracteristicas.gastos_comunidad, valor),
    //   "50": {
    //     "name": "Suministro Agua",
    //     "groupFamily": "[3,4,5,6,7]",
    //     "buildingType": "[1,2,4,5,6,7,8,9,10,11,12,13,14,15,17,22,23,25,26,28,29,30,31,32,33,35,36,37,38,39,41,42,43,44,45,46,47]",
    //     "type": "bool/text"
    //   },
    suministro_agua: (valor: boolean) => toBoolFeature(TCaracteristicas.suministro_agua, valor),
    //   "51": {
    //     "name": "Suministro Eléctrico",
    //     "groupFamily": "[3,4,5,6,7]",
    //     "buildingType": "[1,2,4,5,6,7,8,9,10,11,12,13,14,15,17,22,23,25,26,28,29,30,31,32,33,35,36,37,38,39,41,42,43,44,45,46,47]",
    //     "type": "integer/text",
    //     "valueList": {
    //       "1": "Unión Fenosa",
    //       "2": "Iberdrola",
    //       "3": "Endesa",
    //       "4": "Otra compañía",
    //       "5": "Sin luz"
    //     }
    //   },
    suministro_electrico: (valor: pisos_com.TSuministroElectrico) => toFeature(TCaracteristicas.suministro_electrico, valor),
    //   "52": {
    //     "name": "Suministro Gas",
    //     "groupFamily": "[3,4,5,6,7,11]",
    //     "buildingType": "[1,2,4,5,6,7,8,9,10,11,12,13,14,15,17,22,23,25,26,28,29,30,31,32,33,35,36,37,38,39,40,41,42,43,44,45,46,47]",
    //     "type": "integer/text",
    //     "valueList": {
    //       "1": "Gas Natural",
    //       "2": "Iberdrola",
    //       "3": "Endesa",
    //       "4": "Otra compañía",
    //       "5": "Sin luz"
    //     }
    //   },
    suministro_gas: (valor: pisos_com.TSuministroGas) => toFeature(TCaracteristicas.suministro_gas, valor),
    //   "53": {
    //     "name": "Suministro Teléfono",
    //     "groupFamily": "[3,4,5,7,11]",
    //     "buildingType": "[1,2,4,6,7,8,9,10,11,12,13,14,15,17,22,23,25,26,28,29,30,31,32,33,35,36,37,38,39,40,41,43,44,45,46,47]",
    //     "type": "bool/text"
    //   },
    suministro_telefono: (valor: boolean) => toBoolFeature(TCaracteristicas.suministro_telefono, valor),
    //   "54": {
    //     "name": "Subterráneo",
    //     "groupFamily": 8,
    //     "buildingType": "[3,24,34]",
    //     "type": "bool/text"
    //   },
    //   "55": {
    //     "name": "Cerrado",
    //     "groupFamily": 8,
    //     "buildingType": "[3,24,34]",
    //     "type": "bool/text"
    //   },
    //   "56": {
    //     "name": "Divisiones",
    //     "groupFamily": 7,
    //     "buildingType": "[4,6,7,12,14,33]",
    //     "type": "bool/text"
    //   },
    //   "57": {
    //     "name": "Columnas",
    //     "groupFamily": 8,
    //     "buildingType": "[3,24,34]",
    //     "type": "bool/text"
    //   },
    //   "58": {
    //     "name": "Vallado",
    //     "groupFamily": 5,
    //     "buildingType": "[9,17,22,23,26,28,29,35,36]",
    //     "type": "bool/text"
    //   },
    //   "59": {
    //     "name": "Cultivable",
    //     "groupFamily": 5,
    //     "buildingType": "[9,17,22,23,26,28,29,35,36]",
    //     "type": "bool/text"
    //   },
    //   "60": {
    //     "name": "Árboles",
    //     "groupFamily": 5,
    //     "buildingType": "[9,17,22,23,26,28,29,35,36]",
    //     "type": "bool/text"
    //   },
    //   "61": {
    //     "name": "Edificaciones",
    //     "groupFamily": 5,
    //     "buildingType": "[9,17,22,23,26,28,29,35,36]",
    //     "type": "bool/text"
    //   },
    //   "62": {
    //     "name": "Edificable",
    //     "groupFamily": 5,
    //     "buildingType": "[9,17,22,23,26,28,29,35,36]",
    //     "type": "bool/text"
    //   },
    //   "63": {
    //     "name": "Alcantarillado",
    //     "groupFamily": 5,
    //     "buildingType": "[9,17,22,23,26,28,29,35,36]",
    //     "type": "bool/text"
    //   },
    //   "64": {
    //     "name": "Urbanizado",
    //     "groupFamily": "[4,5]",
    //     "buildingType": "[2,8,9,11,17,22,23,26,28,29,30,31,35,36,37,38,41,43,44,45,46,47]",
    //     "type": "bool/text"
    //   },
    //   "65": {
    //     "name": "Asfaltado",
    //     "groupFamily": "[4,5]",
    //     "buildingType": "[2,8,9,11,17,22,23,26,28,29,30,31,35,36,37,38,41,43,44,45,46,47]",
    //     "type": "bool/text"
    //   },
    //   "66": {
    //     "name": "Alumbrado",
    //     "groupFamily": "[4,5]",
    //     "buildingType": "[2,8,9,11,17,22,23,26,28,29,30,31,35,36,37,38,41,43,44,45,46,47]",
    //     "type": "bool/text"
    //   },
    //   "67": {
    //     "name": "Muelle de Carga",
    //     "groupFamily": 6,
    //     "buildingType": "[5,42]",
    //     "type": "bool/text"
    //   },
    //   "68": {
    //     "name": "Grúa",
    //     "groupFamily": 6,
    //     "buildingType": "[5,42]",
    //     "type": "bool/text"
    //   },
    //   "69": {
    //     "name": "Aislantes",
    //     "groupFamily": "[6,7]",
    //     "buildingType": "[4,5,6,7,12,14,33,42]",
    //     "type": "bool/text"
    //   },
    //   "70": {
    //     "name": "Oficina",
    //     "groupFamily": 6,
    //     "buildingType": "[5,42]",
    //     "type": "bool/text"
    //   },
    //   "71": {
    //     "name": "Salida Humos",
    //     "groupFamily": "[6,7]",
    //     "buildingType": "[4,5,6,7,12,14,33,42]",
    //     "type": "bool/text"
    //   },
    //   "72": {
    //     "name": "Cubierta",
    //     "groupFamily": 6,
    //     "buildingType": "[5,42]",
    //     "type": "bool/text"
    //   },
    //   "73": {
    //     "name": "Salida Antiincendios",
    //     "groupFamily": "[6,7]",
    //     "buildingType": "[4,5,6,7,12,14,33,42]",
    //     "type": "bool/text"
    //   },
    //   "74": {
    //     "name": "Instalación Red",
    //     "groupFamily": "[6,7,11]",
    //     "buildingType": "[4,5,6,7,12,14,33,40,42]",
    //     "type": "bool/text"
    //   },
    //   "75": {
    //     "name": "Puertas Acceso",
    //     "groupFamily": "[6,7]",
    //     "buildingType": "[4,5,6,7,12,14,33,42]",
    //     "type": "bool/text"
    //   },
    //   "76": {
    //     "name": "Escaparate",
    //     "groupFamily": "[6,7]",
    //     "buildingType": "[4,5,6,7,12,14,33,42]",
    //     "type": "bool/text"
    //   },
    //   "77": {
    //     "name": "Planta Baja",
    //     "groupFamily": 7,
    //     "buildingType": "[4,6,7,12,14,33]",
    //     "type": "bool/text"
    //   },
    //   "78": {
    //     "name": "Caja Fuerte",
    //     "groupFamily": "[6,7]",
    //     "buildingType": "[4,5,6,7,12,14,33,42]",
    //     "type": "bool/text"
    //   },
    //   "79": {
    //     "name": "Vestuarios",
    //     "groupFamily": 6,
    //     "buildingType": "[5,42]",
    //     "type": "bool/text"
    //   },
    //   "80": {
    //     "name": "Habitaciones Suite Dobles",
    //     "groupFamily": "[3,4,5,11]",
    //     "buildingType": "[1,2,8,9,10,11,13,15,17,22,23,25,26,28,29,30,31,32,35,36,37,38,39,40,41,43,44,45,46,47]",
    //     "type": "integer"
    //   },
    //   "81": {
    //     "name": "Ancho",
    //     "groupFamily": "[5,6,7]",
    //     "buildingType": "[4,5,6,7,9,12,14,17,22,23,26,28,29,33,35,36,42]",
    //     "type": "integer"
    //   },
    //   "82": {
    //     "name": "Alto",
    //     "groupFamily": "[5,6,7]",
    //     "buildingType": "[4,5,6,7,9,12,14,17,22,23,26,28,29,33,35,36,42]",
    //     "type": "integer"
    //   },
    //   "83": {
    //     "name": "Fondo",
    //     "groupFamily": "[6,7]",
    //     "buildingType": "[4,5,6,7,12,14,33,42]",
    //     "type": "integer"
    //   },
    //   "84": {
    //     "name": "Fondo Izquierdo",
    //     "groupFamily": 5,
    //     "buildingType": "[9,17,22,23,26,28,29,35,36]",
    //     "type": "integer"
    //   },
    //   "85": {
    //     "name": "Fondo Derecho",
    //     "groupFamily": 5,
    //     "buildingType": "[9,17,22,23,26,28,29,35,36]",
    //     "type": "integer"
    //   },
    //   "86": {
    //     "name": "Plazas",
    //     "groupFamily": 8,
    //     "buildingType": "[3,24,34]",
    //     "type": "integer"
    //   },
    //   "87": {
    //     "name": "Altura Piso",
    //     "groupFamily": "[6,7]",
    //     "buildingType": "[4,5,6,7,12,14,33,42]",
    //     "type": "integer"
    //   },
    //   "88": {
    //     "name": "Superficie edificada",
    //     "groupFamily": "[5,6,7,8]",
    //     "buildingType": "[3,4,5,6,7,9,12,14,17,22,23,24,26,28,29,33,34,35,36,42]",
    //     "type": "float"
    //   },
    //   "89": {
    //     "name": "Se aceptan mascotas",
    //     "groupFamily": "[3,4,5,6,7,11]",
    //     "buildingType": "[1,4,5,6,8,10,11,12,13,14,15,22,32,39,40,42]",
    //     "type": "bool/text"
    //   },
    //   "90": {
    //     "name": "Adaptado a personas con discapacidad",
    //     "groupFamily": "[3,4,5,6,7,11]",
    //     "buildingType": "[1,4,5,6,8,10,11,12,13,14,15,22,32,39,40,42]",
    //     "type": "bool/text"
    //   },
    //   "91": {
    //     "name": "Indice referencia precios",
    //     "groupFamily": "[3,4]",
    //     "buildingType": "[1,2,8,10,11,13,15,17,18,23,25,26,30,31,32,37,38,39,41,43,44,45,46,47]",
    //     "type": "float"
    //   },
    //   "92": {
    //     "name": "Tipo habitación",
    //     "groupFamily": "[11]",
    //     "buildingType": "[40]",
    //     "type": "integer/text",
    //     "valueList": {
    //       "1": "Individual",
    //       "2": "Doble"
    //     }
    //   },
    //   "93": {
    //     "name": "Tamaño habitación",
    //     "groupFamily": "[11]",
    //     "buildingType": "[40]",
    //     "type": "integer/text",
    //     "valueList": {
    //       "1": "Pequeña (4-8 m²)",
    //       "2": "Mediana (8-12 m²)",
    //       "3": "Grande (>12 m²)"
    //     }
    //   },
    //   "94": {
    //     "name": "Número inquilinos",
    //     "groupFamily": "[11]",
    //     "buildingType": "[40]",
    //     "type": "integer/text",
    //     "valueList": {
    //       "1": "1 inquilino",
    //       "2": "2 inquilinos",
    //       "3": "3 inquilinos",
    //       "4": "4 inquilinos"
    //     }
    //   },
    //   "95": {
    //     "name": "Género inquilinos",
    //     "groupFamily": "[11]",
    //     "buildingType": "[40]",
    //     "type": "integer/text",
    //     "valueList": {
    //       "1": "Femenino",
    //       "2": "Masculino",
    //       "3": "Mixto"
    //     }
    //   },
    //   "96": {
    //     "name": "Se permite fumar",
    //     "groupFamily": "[11]",
    //     "buildingType": "[40]",
    //     "type": "bool/text"
    //   },
    //   "97": {
    //     "name": "Permitido parejas",
    //     "groupFamily": "[11]",
    //     "buildingType": "[40]",
    //     "type": "bool/text"
    //   },
    //   "98": {
    //     "name": "Baño propio",
    //     "groupFamily": "[11]",
    //     "buildingType": "[40]",
    //     "type": "bool/text"
    //   },
    //   "99": {
    //     "name": "Gastos incluidos",
    //     "groupFamily": "[11]",
    //     "buildingType": "[40]",
    //     "type": "bool/text"
    //   },
    //   "100": {
    //     "name": "Acceso internet",
    //     "groupFamily": "[11]",
    //     "buildingType": "[40]",
    //     "type": "bool/text"
    //   }
    // }
  }


  export const enum TAltura { // milanuncios
    semisotano = "Semisótano",
    sotano = "Sótano",
    entresuelo = "Entresuelo",
    principal = "Principal",
    planta_baja = "Planta baja",
    p1 = "1",
    p2 = "2",
    p3 = "3",
    p4 = "4",
    p5 = "5",
    p6 = "6",
    p7 = "7",
    p8 = "8",
    p9 = "9",
    p10 = "10",
    p11 = "11",
    p12 = "12",
    encima_12 = "+12",
  };
  export const enum TOrientacion {
    Norte = "1",
    Sur = "5",
    Este = "3",
    Oeste = "5",
    Noreste = "2",
    Noroeste = "8",
    Sureste = "4",
    Suroeste = "6",
    Mar = "9",
    Montaña = "10",
  }
  export const enum TEnergia {
    A = "1",
    B = "2",
    C = "3",
    D = "4",
    E = "5",
    F = "6",
    G = "7",
    exento = "8",
    en_tramite = "9",
  }
  export const enum TSuelo {
    gres = "1",
    marmol = "2",
    moqueta = "3",
    parquet = "4",
    tarima_flotante = "5",
    tarima_maciza = "6",
    terrazo = "7",
  }

  /**
   * Estructura con la que Pisos.com devuelve el Jwt Token
   */
  export declare type JwtToken = {
    error: string,
    errors: any,
    isSuccess: boolean,
    data: string
  }

  /**
   * Estructura con la que pisos.com identifica un account
   */
  export declare type AccountRefStruct = {
    agencyId: string,
    key: string
  }

  /**
   * Estructura con la que pisos.com valida un token, mínima
   */
  export declare type ValidAccount = {
    status: number,
    text: string
  }

}