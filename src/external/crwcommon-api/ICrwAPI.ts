import { IMessageBroker } from "lib/MessagesBroker";
import { CrwStructs } from "./CrwAdStruct";
import { CrwZoneStruct } from "./CrwZoneStruct";


export declare type CrwAdRequestData = {
  request_key: string,
  body: {
    ad_key: string
  }
}
export declare type CrwAdResponseData = {
  request_key: string,
  status: number,
  body: {
    ad_key: string,
  }
}


export interface ICrwAPI {
  /**
   * Lee la estructura de zonas completa.
   * Sólo lo usamos con crwidealistaAPI, en el resto de casos 
   */
  getZonesAsTree(): Promise<CrwZoneStruct[]>;
  listAdsVersionsPage(lastRead?: CrwStructs.AdVersion, size?: number): Promise<CrwStructs.AdVersion[]>;
  listAdsVersions(ids: string[]): Promise<CrwStructs.AdVersion[]>;
  listAds(ids: string[]): Promise<CrwStructs.Ad[]>;
  sendAdRefreshRequest(data: CrwAdRequestData): Promise<void>;
  subscribeAdRefreshResponse(messagesbroker: IMessageBroker, callback: (response: CrwAdResponseData) => Promise<void>): Promise<void>;
}
