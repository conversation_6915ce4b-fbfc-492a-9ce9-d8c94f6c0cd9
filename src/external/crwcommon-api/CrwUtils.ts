import { appContext } from "lib/AppContext";
import { CloudProvidersModel } from "model/cloudprovidersModel";
import { ICrwAPI } from "./ICrwAPI";

export namespace CrwUtils {
  /**
   * Obtiene la API para interactuar con el proveedor externo.
   * Futuras APIs deben añadirse manualmente aquí.
   * @param cloudProviderId Identificador del proveedor (entidad CloudProvider) que representa a un Crawler externo
   * @returns 
   */
  export function getProviderAPI(cloudProviderId: string): ICrwAPI {
    switch (cloudProviderId) {
      case CloudProvidersModel.providersIds.crwidealista:
        return appContext().crwidealista_api;
      case CloudProvidersModel.providersIds.crwhabitaclia:
        throw new Error("Not yet implemented!!!");
      case CloudProvidersModel.providersIds.crwhaya:
        return appContext().crwhaya_api;
      case CloudProvidersModel.providersIds.crwservihabitat:
        return appContext().crwservihabitat_api;
      case CloudProvidersModel.providersIds.crwsolvia:
        return appContext().crwsolvia_api;
      case CloudProvidersModel.providersIds.crwindividuals:
        return appContext().crwindividuals_api;
      case CloudProvidersModel.providersIds.crwunicaja:
        return appContext().crwunicaja_api;
      case CloudProvidersModel.providersIds.crwportalnow:
        return appContext().crwportalnow_api;
      case CloudProvidersModel.providersIds.crwaliseda:
        return appContext().crwaliseda_api;
        case CloudProvidersModel.providersIds.crwuci:
          return appContext().crwuci_api;
      default:
        throw new Error(`There is not an API for provider ${cloudProviderId}`);
    }

  }
}