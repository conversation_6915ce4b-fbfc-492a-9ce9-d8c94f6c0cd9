import { ErrUtils } from "agentor-lib"; const { doThrow } = ErrUtils;
import { jsonArrayMember, jsonMember, jsonObject, TypedJSON } from "typedjson";

export declare type integer = number;
export declare type uuid = string;


export namespace CrwStructs {
  /**
   * El valor a deserializar deben existir en el conjunto de valores aceptados
   * @param acceptedValues 
   * @returns 
   */
  function oneOfDeserializer<T>(acceptedValues: T[]) {
    const acceptedSet = new Set<T>(acceptedValues);
    return (json: any) =>
      json === null || json === void 0 ? json :
        acceptedSet.has(json) ? json :
          doThrow(new TypeError(`Value ${json} is not into acceptable ones`));
  }
  /**
   * Los valores a deserializar deben existir en el conjunto de valores aceptados
   * @param acceptedValues 
   * @returns 
   */
  function someOfDeserializer<T>(acceptedValues: T[]) {
    const acceptedSet = new Set<T>(acceptedValues);
    return (json: any) => {
      if (json === null || json === void 0)
        return json;
      else if (Array.isArray(json)) {
        const result = json.filter(value => acceptedSet.has(value));
        if (result.length !== json.length) {
          const diff = json.filter(x => !acceptedSet.has(x));
          throw new TypeError(`Values [${diff}] are not into the acceptable ones`)
        } else {
          return json;
        }
      }

    }
  }


  @jsonObject
  export class At {
    @jsonMember(Date, { isRequired: true })
    at!: Date
  }
  @jsonObject
  export class Modality {
    @jsonMember(Number, { isRequired: true })
    amount!: number
  }
  @jsonObject
  export class SourceContact {
    @jsonMember(String)
    phone: string | null = null
   
  }
  @jsonObject
  export class Source {
    @jsonMember(String, { isRequired: true })
    pageUrl!: string
    @jsonMember(String)
    reference: string | null = null
    @jsonMember(Boolean)
    anouncedByAnIndividual: boolean | null = null
    @jsonMember(CrwStructs.SourceContact, { isRequired: true })
    contact!: SourceContact
    @jsonMember(String)
    name: string | null = null
  }
  @jsonObject
  export class PropertyType {
    @jsonMember(String, { isRequired: true, deserializer: oneOfDeserializer(["flat", "house"]) })
    code!: string
  }
  @jsonObject
  export class Code {
    @jsonMember(String, { isRequired: true })
    code!: string
  }
  @jsonObject
  export class Id {
    @jsonMember(String, { isRequired: true })
    id!: string
  }
  @jsonObject
  export class PropertyAddress {
    @jsonMember(CrwStructs.Code, { isRequired: true })
    city!: Code
    /**
     * @deprecated En nuevos anuncios se usa property.zone
     */
    @jsonMember(Id)
    zone: Id | null = null
  }
  @jsonObject
  export class Media {
    @jsonMember(String, { isRequired: true })
    url!: string
    @jsonMember(String)
    title: string | null = null
  }
  @jsonObject
  export class PropertyAttributes {
    @jsonMember(Number, { isRequired: true })
    totalSurfaceM2!: number
    @jsonMember(Number)
    usefulSurfaceM2: number | null = null
    @jsonMember(Number)
    solarSurfaceM2: number | null = null
    @jsonMember(Number)
    totalBedroomsCount!: number // Integer
    @jsonMember(String, { deserializer: oneOfDeserializer(["new", "second_hand"]) })
    statusCode: string | null = null
    @jsonMember(Number) // Integer
    bathroomsCount: number | null = null
    @jsonMember(Boolean)
    elevatorHas: boolean | null = null
    @jsonMember(String, { deserializer: oneOfDeserializer(["none", "cold", "coldAndHeat"]) })
    airConditioningCode: string | null = null
    @jsonMember(String, { deserializer: oneOfDeserializer(["none", "central", "electric", "naturalGas", "gasoil"]) })
    heatingCode: string | null = null
    @jsonMember(String, { deserializer: oneOfDeserializer(["none", "own", "community"]) })
    gardenCode: string | null = null
    @jsonMember(String, { deserializer: oneOfDeserializer(["none", "own", "community"]) })
    swimmingPoolCode: string | null = null
    @jsonMember(Boolean)
    terraceHas: boolean | null = null
    @jsonMember(Number) // Integer
    buildInCabinetsCount: number | null = null
    @jsonArrayMember(String, { deserializer: someOfDeserializer(["interior", "exterior"]) })
    facadeCodes: string[] | null = null
    @jsonArrayMember(String, { deserializer: someOfDeserializer(["north", "northeast", "east", "southeast", "south", "southwest", "west", "northwest"]) })
    orientationCodes: string[] | null = null
    @jsonMember(String, { deserializer: oneOfDeserializer(["basement", "semibasement", "ground", "mezzanine", "main", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "penthouse"]) })
    floorCode: string | null = null
    @jsonMember(Number) // Integer
    constructionYear: number | null = null
    @jsonMember(Number) // Integer
    parkingPlacesCount: number | null = null
  }
  @jsonObject
  export class Property {
    @jsonMember(CrwStructs.PropertyType, { isRequired: true })
    type!: PropertyType
    @jsonMember(CrwStructs.Code)
    subtype: Code | null = null
    @jsonMember(CrwStructs.PropertyAddress, { isRequired: true })
    address!: PropertyAddress
    @jsonMember(CrwStructs.Id)
    zone: Id | null = null
    @jsonArrayMember(CrwStructs.Media, { isRequired: true })
    medias!: Media[]
    @jsonMember(Number)
    medias_count: number|null = null
    @jsonMember(CrwStructs.PropertyAttributes, { isRequired: true })
    attributes!: PropertyAttributes

  }
  @jsonObject
  export class RefinedcontentData {
    @jsonMember(String, { isRequired: true })
    title!: string
    @jsonMember(String, {
      // Algunas descripciones son string[].  
      //   Las convertimos a una string multilinea
      deserializer: (json) =>
        json === null ? json :
          Array.isArray(json) ? json.map(x => String(x)).join('\n') :
            String(json)
    })
    description: string | null = null
    @jsonMember(CrwStructs.Modality)
    sale: Modality | null = null
    @jsonMember(CrwStructs.Modality)
    rent: Modality | null = null
    @jsonMember(CrwStructs.Source)
    source: Source | null = null
    @jsonMember(CrwStructs.Property, { isRequired: true })
    property!: Property

  }
  @jsonObject
  export class RefinedContent {
    @jsonMember(CrwStructs.RefinedcontentData, { isRequired: true })
    data!: RefinedcontentData
    @jsonMember(Date)
    when!: Date
  }

  @jsonObject
  export class Ad {
    @jsonMember(String, { isRequired: true })
    id!: uuid;
    @jsonMember(CrwStructs.RefinedContent, { isRequired: true })
    refinedcontent!: RefinedContent
    @jsonMember(CrwStructs.At)
    deleted: At | null = null;
    @jsonMember(String, { isRequired: true })
    revision!: string
  }

  export namespace Ad {
    const serializer = new TypedJSON(CrwStructs.Ad, { errorHandler: e => { throw e } });
    export function deserialize(json: any): Ad {
      return serializer.parse(json) as Ad
    }
  }
  @jsonObject
  export class AdVersion {
    @jsonMember(String, { isRequired: true })
    id!: uuid
    @jsonMember(String, { isRequired: true })
    revision!: string
    @jsonMember(Boolean, { isRequired: true })
    isDeleted!: boolean
  }

}

