/**
 * Datos de una zona.
 * Es compatible 100% con el tipo ZoneWithChildrenDTO de agentor, pero he decidido separar ambas estructuras para abordar posibles diferencias futuras.
 * Debe tenerse en cuenta que agentor usa una copia "exacta" del esquema de zonas del crawler de idealista.
 */
export declare type CrwZoneStruct = {
  id: string,
  parent: { id: string },
  name: string,
  children: CrwZoneStruct[]
};