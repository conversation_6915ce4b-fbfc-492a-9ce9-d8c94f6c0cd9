import * as pg from 'pg';
import { EcomPurchasesModel } from 'model/ecompurchasesModel';
import { EcomAccountsModel } from 'model/ecomaccountsModel';
import { EcomTransactionModel } from 'model/ecomtransactionModel';
import { EcomPaymentsModel } from 'model/ecompaymentsModel';
import { EcomPaymentDTO } from 'model/common/dtos/EcomPaymentDTO';
import { BusinessError, throwUndefinedError } from './BusinessError';
import { EcomPurchaseDTO } from 'model/common/dtos/EcomPurchaseDTO';
import { AscOrDesc } from 'model/common/AscOrDesc';

export namespace ecomPaymentBusiness {

  export class NoFundsError extends BusinessError {
    constructor(msg?: string) { super(msg ?? "No hay suficientes fondos para realizar la operación"); Object.setPrototypeOf(this, new.target.prototype); }
    get code() {
      return "err_insufficientfunds";
    }
  }

  /**
   * Genera un pago si es necesario:
   * - Si la fecha valor del último pago asociado a la compra es anterior a 24 horas, se genera un pago con la fecha valor correspondiente.
   * - Si la cuenta no tiene suficiente fondos, la publicación es eliminada.
   * ¿Qué es "fecha valor"?:  Dada la fecha y hora de la compra, se considera que los pagos deben ejecutarse 
   *                          cada 24 horas "exactas".  Cuando se genera un pago, debe tener como fecha valor 
   *                          un múltiplo de 24 horas (+ fecha compra)
   * Se generará un error (NoFundsError) si no hay fondos suficientes paga generar el pago
   * @param dbTran 
   * @param purchaseId 
   * @returns el pago correspondiente (tanto si existe como si se ha generado) o null en caso de que la compra no requiera pago para el día actual.
   * @errors 
   *   Si no existe la compra, UnknownPurchaseError
   *   Si no hay fondos suficientes, NoFundsError
   */
  export async function generateTodayPaymentIfRequired(dbTran: pg.PoolClient, purchaseId: string, currentDate: Date = new Date()): Promise<EcomPaymentDTO | null> {

    /**
     * Calcula la "fecha valor" y la "cuantía" del hipotético pago que "cubre" la fecha en curso
     * @param purchase Compra a la que se refiere el pago
     * @param currentDate Instante que debe ser estar en el intervalo de fechas cubiertas por el pago
     * @returns {valueDate, valueEndDate, amount} "fecha valor", "fecha final valor" y "cuantía" del pago 
     */
    function calculatePaymentData(purchase: EcomPurchaseDTO, currentDate: Date) {

      const DAY_MS = 24 * 3600 * 1000;
      function daysAgo(nDays: number): Date {
        return new Date(currentDate.getTime() - DAY_MS * nDays);
      }
      function addDays(date: Date, days: number): Date {
        return new Date(date.getTime() + DAY_MS * days);
      }
      // Calcula la fecha de hoy en la que han transcurrido N días exactos desde la fecha de la venta
      function todayValueDate(purchaseDate: Date): Date {
        const diffDays = Math.floor((currentDate.getTime() - purchaseDate.getTime()) / DAY_MS);
        return new Date(purchaseDate.getTime() + diffDays * DAY_MS)
      }
      function addMs(date: Date, ms: number): Date {
        return new Date(date.getTime() + ms);
      }

      // Si estamos en el período de días cubierto por el primer pago, se trata del primer pago.
      const
        purchase_date = purchase.date ??
          throwUndefinedError("purchase date"),
        purchase_firstpayment_days = purchase.firstpayment?.days ??
          throwUndefinedError("purchase firstdate days"),
        isFirstPayment = purchase_date > daysAgo(purchase_firstpayment_days);

      if (isFirstPayment) {
        return {
          valueDate: purchase_date,
          nexPaymentValueDate: addDays(purchase_date, purchase_firstpayment_days),
          amount: purchase.firstpayment?.amount ?? 0
        };
      } else {
        let valueDate = todayValueDate(purchase_date);
        return {
          valueDate,
          // Es el valor de fecha "máximo" cubierto por un pago efectuado en valueDate
          nexPaymentValueDate: addDays(valueDate, 1),
          amount: purchase.dailyamount ?? 0
        };
      }

    }

    const [purchase] = await EcomPurchasesModel.list({ id: purchaseId, include_product: true })(dbTran);
    if (!purchase || !purchase.date)  // purchase.date lo evaluamos porque la definición del DTO admite "undefined"... aunque sabemos que si hay venta, la fecha existe
      throw new BusinessError(`Unknown purchase ${purchaseId}`);

    const { valueDate, nexPaymentValueDate, amount } = calculatePaymentData(purchase, currentDate);
    const [existingPayment] = await EcomPaymentsModel.list({ purchase_id: purchaseId, valuedate_min: valueDate, valuedate_lt: nexPaymentValueDate, sortBy: { valuedate: AscOrDesc.asc } })(dbTran);
    if (existingPayment)
      return existingPayment;
    else if (amount < 0)
      throw new BusinessError("Unexpected negative amount purchase");
    else if (amount === 0)
      // No es necesario ningún pago
      return null;
    else {
      const
        buyer_id = purchase.buyer?.id ??
          throwUndefinedError("purchase buyer"),
        seller_id = purchase.product?.account?.id ??
          throwUndefinedError("purchase product seller");

      // 1. Generamos la transacción 
      const transaction = await EcomTransactionModel.create({
        amount,
        sourceaccount: { id: buyer_id },
        destinationaccount: { id: seller_id },
      })(dbTran);
      // 2. Actualizamos balances

      // 2.1. Descontamos cuantía de la cuenta compradora
      const balance = await EcomAccountsModel.addToBalance(buyer_id, -amount)(dbTran);
      if (balance === null || Number(balance) < 0)
        throw new NoFundsError(`Not enought funds to cover ${amount} payment`);

      // 2.2. Añadimos cuantía a la cuenta vendedora
      await EcomAccountsModel.addToBalance(seller_id, amount)(dbTran);

      // 3. Registramos el pago asociado a la compra y asociado a la transacción
      const payment = await EcomPaymentsModel.create({
        purchase: { id: purchase.id },
        transaction: { id: transaction.id },
        valuedate: valueDate
      })(dbTran);

      return payment;
    }
  }
}