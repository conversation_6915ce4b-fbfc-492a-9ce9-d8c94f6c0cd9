import { ArrUtils, ErrUtils, <PERSON>eoloc } from "agentor-lib";
import assert from "assert";
import { appContext } from "lib/AppContext";
import { withDbTrx } from "lib/AppContextUtils";
import { AgentFavouriteoffersModel } from "model/agentFavouriteoffersModel";
import { CitiesModel } from "model/citiesModel";
import { AgentFavouriteofferDTO } from "model/common/dtos/AgentFavouriteofferDTO";
import { EcomPurchaseDTO, EcomPurchaseServiceDTO } from "model/common/dtos/EcomPurchaseDTO";
import { GeolocDTO } from "model/common/dtos/GeolocDTO";
import { OfferDTO } from "model/common/dtos/OfferDTO";
import { OfferstatusCode } from "model/common/dtos/OfferstatusDTO";
import { PropertyAddressDTO } from "model/common/dtos/PropertyAddressDTO";
import { WorkgroupOfferDTO } from "model/common/dtos/WrokgroupOfferDTO";
import { ReservedWorkgroupIds } from "model/consts";
import { ContactsModel } from "model/contactsModel";
import { EcomAccountsModel } from "model/ecomaccountsModel";
import { EcomProductsModel } from "model/ecomproductsModel";
import { EcomPurchasesModel } from "model/ecompurchasesModel";
import { MatchingsModel } from "model/matchingsModel";
import { OffersModel } from "model/offersModel";
import { PropertymediasModel } from "model/propertymediasModel";
import { StreettypesModel } from "model/streettypesModel";
import { workgroupsModel } from "model/workgroupsModel";
import { workgroupOffersModel } from "model/workgroupsOffersModel";
import { PoolClient } from "pg";
import { BusinessError, LogicEntityError } from "./BusinessError";
import { PropertymediasOps } from "./common/PropertymediasOps";
import { ecomPaymentBusiness } from "./ecomPaymentBusiness";
const { doThrow } = ErrUtils;


/** 
 * Unicamente las ofertas de estos agentes pueden ser borradas.
 * Esta limitación puede ser eliminada en cualquier momento, pero de momento solo los inmuebles importados de "homez" (agente cloud) pueden ser borrados!!!
 */
const C_REMOVEABLE_OFFERS_AGENT = [
  "1",
  // 0 = Agente cloud (homes)
  "0",
  // "Idealista Website Crawler"
  "9223372036854775807",
  // "Habitaclia Website Crawler"
  "9223372036854775806"
];

export namespace OfferBusiness {
  export const
    /**
     * Crear una nueva oferta gestionada por una gente.
     * @param dbTran 
     * @param agent_id 
     * @param dto 
     * @returns 
     */
    create = (agent_id: string, dto: OfferDTO) =>
      withDbTrx(dbTran =>
        create_imp(dbTran, agent_id, dto)
      ),
    update = (agent_id: string, id: string, dto: OfferDTO) =>
      withDbTrx(dbTran =>
        update_imp(dbTran, agent_id, id, dto)
      ),
    remove = (agent_id: string, id: string) =>
      withDbTrx(dbTran =>
        remove_imp(dbTran, agent_id, id)
      ),
    createPropertymedia = (myAgent_id: string, offer_id: string, media_key: string, markOfferAsUpdated: boolean = true) =>
      withDbTrx(dbTrans =>
        PropertymediasOps.createPropertymedia(dbTrans, myAgent_id, offer_id, media_key, markOfferAsUpdated)
      ),
    removePropertymedia = (myAgent_id: string, offer_id: string, media_key: string) =>
      withDbTrx(dbTrans =>
        removePropertymedia_imp(dbTrans, myAgent_id, offer_id, media_key)
      ),

    /**
     * Publicar una oferta en un grupo de trabajo.
     * Solo se permiten publicar ofertas en estado "comercialization" (o en cualquier otro estado en el grupo de trabajo "CLOUD")
     * TODO: En futuras versiones deberá configurarse qué estados se permiten publicar en un workgroup (Ej: en portales "comercialization", en cloud cualquiera) 
     * - Si se trata de portales, solo en comercialización
     * - Si se trata de otro tipo, cualquier estado
     * @param dbTran 
     * @param myAgent_id 
     * @param offer_id 
     * @param workgroupId 
     * @returns 
     */
    publishOnWorkgroup = (myAgent_id: string, offer_id: string, workgroupId: string, markOfferAsUpdated: boolean = true) =>
      withDbTrx(dbTran =>
        publishOnWorkgroup_imp(dbTran, myAgent_id, offer_id, workgroupId, markOfferAsUpdated)
      ),
    publishOnWorkgroups = (myAgent_id: string, offer_id: string, workgroupIds: string[]) =>
      withDbTrx(dbTran =>
        publishOnWorkgroups_imp(dbTran, myAgent_id, offer_id, workgroupIds)
      ),
    unpublishFromWorkgroups = (myAgent_id: string, offer_id: string, workgroupIds: string[]) =>
      withDbTrx(dbTran =>
        unpublishFromWorkgroups_imp(dbTran, myAgent_id, offer_id, workgroupIds, { changeOfferUpdatedAt: true })
      ),
    unpublishFromWorkgroup = (myAgent_id: string, offer_id: string, workgroupId: string) =>
      withDbTrx(dbTran =>
        unpublishFromWorkgroup_imp(dbTran, myAgent_id, offer_id, workgroupId, { changeOfferUpdatedAt: true })
      ),
    addOfferToFavourites = (myAgent_id: string, offer_id: string) =>
      withDbTrx(dbTran =>
        addOfferToFavourites_imp(dbTran, myAgent_id, offer_id)
      ),
    removeOfferFromFavourites = (myAgent_id: string, offer_id: string) =>
      withDbTrx(dbTran =>
        removeOfferFromFavourites_imp(dbTran, myAgent_id, offer_id)
      );



  async function create_imp(dbTran: PoolClient, agent_id: string, dto: OfferDTO): Promise<OfferDTO> {
    assert(dto.property?.type?.code ?? null !== null);
    // Si se ha asignado un customer, éste debe ser un contacto del agente.
    await checkOfferCustomer(dbTran, agent_id, dto);
    // Si vamos a ser la version de otra oferta, verificar que se cumplen las precondiciones necesarias.
    await checkOfferVersionOf(dbTran, agent_id, dto);
    // Si el estado no es histórico, anulamos todos los campos del estado "historic"
    // Esto es debido a que la entidad "historic" está incrustada como campos de la propia oferta (debería ser una entidad débil)
    if (dto.status?.code !== OfferstatusCode.historic) {
      dto.historic = null
    } else if (!dto.historic?.cause?.code) {
      // Si el estado es histórico, debe incluirse la causa de histórico
      throw new LogicEntityError("Historic cause must be specified");
    }
    // Revisamos la geolocalización 
    dto = await reviewPropertyLocation(dbTran, dto);

    // Creamos la oferta   
    const offerId = await OffersModel.create({ ...dto, agent: { id: agent_id } })(dbTran);

    await MatchingsModel.refreshOfferMatchings(offerId)(dbTran);

    const [offer] = await OffersModel.list({ id: offerId })(dbTran);
    return offer;
  }
  async function update_imp(dbTran: PoolClient, agent_id: string, id: string, dto: OfferDTO): Promise<OfferDTO | null> {
    assert(id !== void 0);
    // Debido a que el OffersModel.update en el modelo se realiza por ID, nos aseguramos de que el ID sea del agente.
    const existingCount = await OffersModel.count({ agent_id, id })(dbTran);
    // Si existe y es del agente
    if (existingCount === 1) {

      // Si se ha asignado un customer, éste debe ser un contacto del agente.
      let customerExists = dto.customer?.id ?
        1 === await ContactsModel.count({ id: dto.customer.id, agent_id })(dbTran) :
        true;
      if (!customerExists) {
        throw new LogicEntityError("Unknown customer");
      }
      // Si el estado no es histórico, anulamos todos los campos del estado "historic"
      // Esto es debido a que la entidad "historic" está incrustada como campos de la propia oferta (debería ser una entidad débil)
      if (dto.status?.code !== OfferstatusCode.historic) {
        dto.historic = null
      } else {
        if (dto.historic?.cause?.code === null) {
          // Si se intenta asignar explícitamente un null a la causa de histórico estando en estado histórico, se lanza un error
          throw new LogicEntityError("Historic cause is mandatory");
          //throw new RestError({ status: 422, message: "Historic cause is mandatory" });
        } else if (!await OffersModel.exists({ id, status_codes: [OfferstatusCode.historic] })(dbTran) && !dto.historic?.cause?.code) {
          // Estamos cambiando de otro estado a histórico, y no se ha indicado una causa
          throw new LogicEntityError("Historic cause is mandatory");
          //throw new RestError({ status: 422, message: "Historic cause is mandatory" });
        }
      }

      if (dto.sale === null || dto.sale?.allowed == false) {
        // Anulamos todas las propiedades asociadas a la venta
        dto.sale = { allowed: false, fee: null, amount: null, marketAmount: null };
      }
      if (dto.rent === null || dto.rent?.allowed === false) {
        // Anulamos todas las propiedades asociadas al alquiler
        dto.rent = { allowed: false, amount: null, marketAmount: null };
      }
      if (dto.mandate === null || dto.mandate?.type?.code === null) {
        // Nos aseguramos de eliminar cualquier propiedad asociada al mandato.
        dto.mandate = null;
      }

      // Permitimos indicar version:null, ya que las ofertas que no son versiones vienen con este dato informado así.
      // Debemos asegurarnos que la oferta no sea una versión antes de aceptar este cambio
      if (dto.version === null) {
        const isVersion = await OffersModel.count({ agent_id, id, isVersion: true })(dbTran);
        if (isVersion) {
          throw new LogicEntityError("A version can't be transformed to a non version offer");
        }
      }


      dto = await reviewPropertyLocation(dbTran, dto);
      // Lo modificamos              
      let updatedCount = await OffersModel.update({ ...dto, id })(dbTran);

      if (updatedCount !== 0) {
        // If not "comercialization" state, unpublish offer (from all workgroups)
        if (dto.status?.code !== OfferstatusCode.commercialization)
          //await workgroupOffersModel.removeByOfferId(id)(dbTran);
          await unpublishFromAllWorkgroups_imp(dbTran, id);
        // If main offer is in "historic", its versions must be changed to "historic"
        if (dto.status?.code === OfferstatusCode.historic) {
          // Pasar a histórico las versiones de la oferta que no lo estén (activas)
          //  La causa y fecha de histórico son las mismas que de la oferta versionada
          let activeVersions = await OffersModel.list({ version_of_id: id, status_codes: OfferstatusCode.activeCodes })(dbTran);
          for (const activeVersion of activeVersions) {
            await update_imp(dbTran, agent_id, activeVersion.id!, {
              ...activeVersion,
              status: {
                code: OfferstatusCode.historic
              },
              historic: {
                ...dto.historic
              },
              // Al updatear el property, no debe indicarse el identificador
              property: { ...activeVersion.property, id: void 0 },
            });
          }
        }
        /*TODO: 
        else {
          // Si no estamos en histórico pero somos versión, asegurar que la oferta de la que somos versión no esté en histórico
        }
        */

        await MatchingsModel.refreshOfferMatchings(id)(dbTran);
        // 
        const [offer] = await OffersModel.list({ id })(dbTran);
        return offer;
      }
    }
    return null;
  }
  async function remove_imp(dbTran: PoolClient, agent_id: string, id: string): Promise<number> {
    if (!C_REMOVEABLE_OFFERS_AGENT.includes(agent_id))
      throw new BusinessError("Only CLOUD imported offers can be removed");

    // Nos aseguramos de que el id es de una oferta del agente
    const existingCount = await OffersModel.count({ agent_id, id })(dbTran);
    // Si existe y es del agente
    if (existingCount === 1)
      // Esperamos que todo lo referente a la oferta se elimine a través de las claves foráneas.
      // Esta operación solo debería ser válidad para ofertas del CLOUD
      return OffersModel.remove(id)(dbTran);
    else
      return 0;
  }
  async function removePropertymedia_imp(dbTrans: PoolClient, myAgent_id: string, offer_id: string, media_key: string): Promise<number | null> {
    // Verify agent is the owner of the property
    let [property_id] = await OffersModel.listPropertyId({ id: offer_id, agent_id: myAgent_id })(dbTrans);
    if (!property_id) {
      return null;
    } else {
      const count = await PropertymediasModel.remove({ property_id, media_key })(dbTrans);
      if (count !== 0)
        // Forzamos la actualización de la oferta (para que conste a la hora de, por ejemplo, publicarla en portales)
        await OffersModel.update({ id: offer_id })(dbTrans);
      return count;
    }
  }

  async function publishOnWorkgroup_imp(dbTran: PoolClient, myAgent_id: string, offer_id: string, workgroupId: string, markOfferAsUpdated: boolean = true): Promise<WorkgroupOfferDTO> {
    const [wg] = await workgroupsModel.list({ id: workgroupId })(dbTran);
    if (!wg)
      //throw new RestError({ status: 422, message: `Unknown workgroup ${workgroupId}` });
      throw new LogicEntityError(`Unknown workgroup ${workgroupId}`);


    const [offer] = await OffersModel.list({ id: offer_id })(dbTran);
    if (!offer)
      throw new LogicEntityError(`Unknown offer ${offer_id}`);
    else if (offer.status?.code !== OfferstatusCode.commercialization && workgroupId !== ReservedWorkgroupIds.CLOUD && workgroupId !== ReservedWorkgroupIds.ACTIVOS_BANCARIOS && workgroupId !== ReservedWorkgroupIds.WEB_TOPBROKERS) // Hardcoded hasta que sea configurable
      throw new LogicEntityError(`Only offers in "commercialization" state can be published`);

    const ecomProduct_id = wg.publicationecomproduct?.id;
    const purchase = ecomProduct_id ? await (async function () {
      let [buyer] = await EcomAccountsModel.list({ agent_id: myAgent_id })(dbTran);
      if (buyer == void 0 || buyer.id === void 0)
        throw new BusinessError(`Agent ${myAgent_id} has no an associated account`);

      const purchasedService = {
        type: { code: "publication" },
        offer: { id: offer_id },
        workgroup: { id: workgroupId },
      };
      return performPurchaseAndPayment(dbTran, `Publicación de #${offer_id} en ${wg.name}`,
        buyer.id,
        ecomProduct_id,
        purchasedService
      );
    })() : null;


    const pkdto = await workgroupOffersModel.create({
      offer: { id: offer_id },
      member: {
        agent: { id: myAgent_id },
        workgroup: { id: workgroupId },
      },
      ecompurchase: purchase?.id ? { id: purchase.id } : null
    })(dbTran);
    if (markOfferAsUpdated)
      // Marcamos la oferta como actualizada (esto ayuda a "REPUBLICARLA" en los portales afectados)
      await OffersModel.update({ id: offer_id })(dbTran);

    const result = await workgroupOffersModel.read(pkdto)(dbTran);
    if (!result)
      throw new BusinessError("Unexpected error after creating the workgroupOffer.  created object doesn't exist!!!");

    return result;
  }
  async function publishOnWorkgroups_imp(dbTran: PoolClient, myAgent_id: string, offer_id: string, workgroupIds: string[]) {
    return ArrUtils.asyncMap(workgroupIds, workgroupId =>
      publishOnWorkgroup_imp(dbTran, myAgent_id, offer_id, workgroupId)
    );
  }

  async function unpublishFromWorkgroups_imp(dbTran: PoolClient, myAgent_id: string, offer_id: string, workgroupIds: string[], options: { changeOfferUpdatedAt: boolean }): Promise<number> {
    const counts = await ArrUtils.asyncMap(workgroupIds, id => unpublishFromWorkgroup_imp(dbTran, myAgent_id, offer_id, id, { changeOfferUpdatedAt: false }));
    const count = counts.reduce((sum: number, v) => sum + v, 0);
    if (options.changeOfferUpdatedAt && count !== 0)
      await OffersModel.update({ id: offer_id })(dbTran);
    return count;
  }
  async function unpublishFromWorkgroup_imp(dbTran: PoolClient, myAgent_id: string, offer_id: string, workgroupId: string, options: { changeOfferUpdatedAt: boolean }): Promise<0 | 1> {
    const wgoPk: workgroupOffersModel.PKDto = {
      member_agent_id: myAgent_id,
      member_workgroup_id: workgroupId,
      offer_id
    };

    const publication = await workgroupOffersModel.read(wgoPk)(dbTran);
    if (publication) {
      // Si tiene una compra asociada, marcamos la fecha de final de servicio
      if (publication.ecompurchase?.id) {  // No null, No undefined, No ""
        await EcomPurchasesModel.update(publication.ecompurchase?.id, { service: { enddate: new Date() } })(dbTran);
      }
      // Eliminamos la compartición
      await workgroupOffersModel.remove([{
        member_agent_id: myAgent_id,
        member_workgroup_id: workgroupId,
        offer_id
      }])(dbTran);
      // Actualizamos la oferta con la última fecha de actualización
      if (options.changeOfferUpdatedAt) {
        await OffersModel.update({ id: offer_id })(dbTran);
      }
      return 1;
    } else {
      return 0;
    }

  }

  async function unpublishFromAllWorkgroups_imp(dbTran: PoolClient, offer_id: string): Promise<number> {
    // Marcamos la fecha de final de servicio de las venta asociada a cada publicación
    for (const publication of await workgroupOffersModel.list({ offer_id })(dbTran)) {
      if (publication.ecompurchase?.id)
        await EcomPurchasesModel.update(publication.ecompurchase.id, { service: { enddate: new Date() } })(dbTran);
    }
    // Borramos todas las comparticiones    
    return await workgroupOffersModel.removeByOfferId(offer_id)(dbTran);
  }

  async function addOfferToFavourites_imp(dbTran: PoolClient, myAgent_id: string, offer_id: string): Promise<AgentFavouriteofferDTO> {
    const [favouriteAgentOffer] = await AgentFavouriteoffersModel.list({ offer_id, agent_id: myAgent_id, includeAgentDetails: false, includeOfferDetails: false })(dbTran);
    return favouriteAgentOffer ?? await AgentFavouriteoffersModel.create({ agent: { id: myAgent_id }, offer: { id: offer_id } })(dbTran);
  }
  async function removeOfferFromFavourites_imp(dbTran: PoolClient, myAgent_id: string, offer_id: string): Promise<1 | 0> {
    const count = await AgentFavouriteoffersModel.remove([{ agent_id: myAgent_id, offer_id }])(dbTran);
    return count === 0 ? 0 : 1;
  }
}

//#region private:

/**
   * Verifica que el cliente de la oferta realmente exista y que pertenezca al mismo agente que la oferta
   * @param dbCli 
   * @param agent_id 
   * @param offer 
   */
async function checkOfferCustomer(dbCli: PoolClient, agent_id: string, offer: OfferDTO) {
  const customerOk = offer.customer?.id
    ? 1 === await ContactsModel.count({ id: offer.customer.id, agent_id })(dbCli)
    : true;
  if (!customerOk)
    //throw new RestError({ status: 422, message: "Unknown customer" });
    throw new LogicEntityError("Unknown customer");
}
/**
   * Si la oferta es una versión, verifica que:
   *   La oferta versionada sea del mismo agente
   *   La oferta versionada no sea una versión
   *   La oferta versionada no esté en histórico
   * @param dbCli 
   * @param agent_id 
   * @param offer 
   */
async function checkOfferVersionOf(dbCli: PoolClient, agent_id: string, offer: OfferDTO) {
  const versionOfOk = offer.version?.of?.id ?
    1 === await OffersModel.count({
      id: offer.version.of.id,
      isVersion: false,
      agent_id,
      status_codes: OfferstatusCode.activeCodes
    })(dbCli) :
    true;


  if (!versionOfOk) 
    throw new LogicEntityError("Versioned offer doesn't exist or can't be versioned");
  
}
/**
 * Revisa (regenera) la localización (geolocalización) del inmueble si es necesario
 * @param ctx 
 * @param dbCli 
 * @param dto 
 * @returns
 */
async function reviewPropertyLocation(dbCli: PoolClient, dto: OfferDTO): Promise<OfferDTO> {
  if (!dto.property) {
    throw new LogicEntityError("Property must be specified");
  } else if (!dto.status?.code) {
    throw new LogicEntityError("Offer status must be specified")
  } else if (dto.status.code === OfferstatusCode.commercialization) {
    // En caso de que la oferta esté en comercialización... geolocalizar
    let address = dto.property.address;
    if (!address && dto.id) {
      // Si la oferta existe y no nos han indicado dirección, intentamos obtener la dirección actual
      let [existingOffer] = await OffersModel.list({ id: dto.id })(dbCli);
      address = existingOffer?.property?.address;
    }
    if (!address) {
      throw new LogicEntityError("Missing Address information: property can't be geolocated");
    } else {
      let location = await geolocateAddress(dbCli, address);
      return (location)
        ? { ...dto, property: { ...dto.property, location: location } }
        : { ...dto, property: { ...dto.property, location: null } };
    }
  } else {
    return dto;
  }

}

async function geolocateAddress(dbCli: PoolClient, address: PropertyAddressDTO): Promise<GeolocDTO | null> {
  // No se puede geocodificar sin nombre de calle
  if (!address.streetname) {
    return null;
  } else {
    let [city] = address.city?.code
      ? await CitiesModel.list({ code: address.city.code })(dbCli)
      : [];
    if (city === void 0) {
      return null;
    } else {
      let [streettype] = address.streettype?.id
        ? await StreettypesModel.list({ id: address.streettype.id })(dbCli)
        : [];
      let adaptedAddress: Geoloc.Address = {
        ...city.province?.country?.code ? { country_alpha2: city.province.country.code } : {}, // Nuestro código es el de 2 caracters... pero la geolocalización usa el código de 3 :-/
        ...address.postcode ? { postalCode: address.postcode } : {},
        cityName: city.label?.default ?? "",
        provinceName: city?.province?.label?.default ?? "",
        street: `${streettype?.label?.default ?? ""} ${address.streetname}`,
        addressNumber: address.number ?? "",
      };
      let geopositions = await appContext().geoloc.localizeAddress(adaptedAddress);
      return geopositions[0] || null;
    }
  }
}

async function performPurchaseAndPayment(dbTran: PoolClient, purchaseDetail: string, buyer_id: string, product_id: string, purchasedService: EcomPurchaseServiceDTO): Promise<EcomPurchaseDTO> {
  const [product] = await EcomProductsModel.list({ id: product_id })(dbTran);
  if (!product)
    throw new LogicEntityError(`Unknown Ecommerce Produc ${product_id}`);
  if (!product.account?.id)
    throw new LogicEntityError(`Unexpected Error: Unknown Ecommerce product ${product_id} has not account!!!`);  // Imposible (en teoría)

  // Generamos la compra
  const purchase = await EcomPurchasesModel.create({
    buyer: { id: buyer_id },
    firstpayment: {
      amount: product.firstpayment?.amount
        ?? doThrow(new LogicEntityError("Unexpected error:  product without firstpayment amount configured")),
      days: product.firstpayment?.days
        ?? doThrow(new LogicEntityError("Unexpected error:  product without firstpayment days configured")),
    },
    dailyamount: product.dailyamount,
    product: { id: product_id },
    details: purchaseDetail,
    service: purchasedService,
  })(dbTran);

  // Generamos el pago
  await ecomPaymentBusiness.generateTodayPaymentIfRequired(dbTran, purchase.id ?? doThrow(new BusinessError("Unexpected: purchase without id")));

  return purchase;
}
  //#endregion
