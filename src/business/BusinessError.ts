
const c_error_emailAlredyExists = "err_01";
const c_error_unknownValidationcode = "err_02";
const c_error_mobileAlredyExists = "err_03";
const c_error_emailDoesntExist = "err_04";

export class BusinessError extends Error {
  constructor(msg?: string) { super(msg); Object.setPrototypeOf(this, new.target.prototype); }

  /**
   * Codigo interno y único del error.
   * Es usado para alimentar las respuestas REST 
   */
  get code(): string | void {
    return void 0;
  }
}

export function throwBusinesError(msg?: string): never { throw new BusinessError(msg); }
export function throwUndefinedError(propertyName?: string): never { throw new BusinessError( propertyName ? `Missing "${propertyName}" property`: void 0); }
export function throwUnexpectedError(cause?: string): never { throw new BusinessError(cause); }
export function throwUnknownEntityError(msg?:string): never { throw new UnknownEntityError(msg);}
export class LogicEntityError extends BusinessError {
  constructor(msg?: string) { super(msg); Object.setPrototypeOf(this, new.target.prototype); }
}

export class UnknownEntityError extends BusinessError {
  constructor(msg?: string) { super(msg ?? "Unknown entity"); Object.setPrototypeOf(this, new.target.prototype); }
}
export class UnexpectedError extends BusinessError {
  constructor(cause?: string) { super("Unexpected:" + cause ?? "Unexpected situation"); Object.setPrototypeOf(this, new.target.prototype); }
}


export class EmailAlredyExistsError extends BusinessError {
  constructor(msg?: string) { super(msg); Object.setPrototypeOf(this, new.target.prototype); }
  get code() {
    return c_error_emailAlredyExists;
  }
}

export class MobileAlredyExistsError extends BusinessError {
  constructor(msg?: string) { super(msg); Object.setPrototypeOf(this, new.target.prototype); }
  get code() {
    return c_error_mobileAlredyExists;
  }
}

export class EmailDoesntExistError extends BusinessError {
  constructor(msg?: string) { super(msg); Object.setPrototypeOf(this, new.target.prototype); }
  get code() {
    return c_error_emailDoesntExist;
  }
}
export class UnknownValidationCodeError extends BusinessError {
  constructor(msg?: string) { super(msg ?? "validation code doesn't exist or has been expired"); Object.setPrototypeOf(this, new.target.prototype); }
  get code() {
    return c_error_unknownValidationcode;
  }
}