import { DateUtils } from "agentor-lib";
import { CrwStructs } from "external/crwcommon-api/CrwAdStruct";
import { CrwUtils } from "external/crwcommon-api/CrwUtils";
import { withDbTrx } from "lib/AppContextUtils";
import { generateKey } from "lib/KeygenUtils";
import { get, keys } from "lodash";
import { CloudExternaloffersModel } from "model/cloudexternaloffersModel";
import { CloudProvidersModel } from "model/cloudprovidersModel";
import { cloudRefreshcallsModel } from "model/cloudRefreshcallsModel";
import { AscOrDesc } from "model/common/AscOrDesc";
import { CloudExternalofferDTO, CloudExternalofferRefDTO } from "model/common/dtos/CloudExternalofferDTO";
import { CloudProviderDTO } from "model/common/dtos/CloudProviderDTO";
import { MultilingualStrDTO } from "model/common/dtos/MultilingualStrDTO";
import { OfferDTO, OfferSourceDTO } from "model/common/dtos/OfferDTO";
import { OffermandatetypeCode } from "model/common/dtos/OffermandatetypeDTO";
import { OfferstatusCode } from "model/common/dtos/OfferstatusDTO";
import { AirConditioningCode, EmissionLevelCode, FacadeCode, FloorCode, HeatingCode, NoneOwnCommunity, OrientationCode, PropertyStatusCode } from "model/common/dtos/PropertyAttributesDTO";
import { PropertyDTO } from "model/common/dtos/PropertyDTO";
import { PropertytypeCode } from "model/common/dtos/PropertytypeDTO";
import { OffersModel } from "model/offersModel";
import { workgroupMembersModel } from "model/workgroupsMembersModel";
import { throwUndefinedError, throwUnexpectedError } from "./BusinessError";
import { PropertymediasOps } from "./common/PropertymediasOps";
import { MediaBusiness } from "./mediaBusiness";
import { OfferBusiness } from "./offerBusiness";
import { appContext } from "lib/AppContext";
import { PoolClient } from "pg";

// Workgroup interno en el que hay que publicar las ofertas del cloud importadas
const C_CLOUD_WG_ID = "9b51df7e-492e-11eb-8f96-cf0cb77c50dd";
const C_ACTIVOS_BANCARIOS_WG_ID = "f2fb4715-970a-04ee-c1da-7a4b269da74b";
const C_WEB_TOPBROKERS_WG_ID = "68058b8b-ab82-496f-b142-817623ef2004"

const importConfigurations = {

}
export namespace cloudExternalofferBusiness {

  /** 

   * 
   * @returns String identificador de la oferta local que se ha vinculado o undefined si no se ha vinculado ninguna
   */

  /**
   * Si la oferta externa tiene una oferta equivalente local (para el agente asociado al proveedor externo) pero no está asociada a ningún proveedor, se asocia
   * - La forma de saber si son "equivalentes" es comparando la URL origen de la oferta local con la del anuncio.
   * Se excluyen las ofertas "veriones"
   * La asociación no realiza un Update de la oferta, únicamente crea el vínculo (si es posible)
   * 
   * @param providerId Proveedor del anuncio
   * @param externalofferId Identificador del anuncio en el proveedor
   * @param filter Filtro de ofertas locales entre las que buscaremos la oferta local.  Internamente se impone el agente, que no sea versión, que tenga la misma URL y que no tenga un proveedor externo
   * @returns String identificador de la oferta local que se ha vinculado o undefined si no se ha vinculado ninguna
   */
  export async function attachFromCrawlerIfExists(providerId: string, externalofferId: string, filter?: OffersModel.Filter): Promise<string | undefined> {
    const [provider] = await CloudProvidersModel.readN([providerId])();
    if (provider?.agent?.id === void 0)
      throwUnexpectedError(`Provider ${providerId} doesn't exist`);
    const [ad] = await CrwUtils.getProviderAPI(providerId).listAds([externalofferId]);
    if (ad !== void 0 && ad.refinedcontent.data.source?.pageUrl) {
      const agent_id = provider.agent.id;
      var pageUrl = ad.refinedcontent.data.source?.pageUrl;
      if (pageUrl) {
        const [localOffer] = await OffersModel.list({
          ...filter ?? {},
          agent_id: agent_id,
          isVersion: false,
          source_pageUrl: ad.refinedcontent.data.source.pageUrl,
          hasCloudprovider: false
        })()
        if (localOffer) {
          await CloudExternaloffersModel.createExternaloffer({
            provider: { id: providerId },
            revision: "",
            id: ad.id,
            localoffer: { id: localOffer.id }
          })();
          return localOffer.id;
        }

      }
    }
    return void 0;
  }
  export async function createFromCrawler(providerId: string, externalofferId: string) {
    const [provider] = await CloudProvidersModel.readN([providerId])();
    if (provider?.agent?.id === void 0)
      throwUnexpectedError(`Provider ${providerId} doesn't exist`);

    const [ad] = await CrwUtils.getProviderAPI(providerId).listAds([externalofferId]);
    if (ad !== void 0) {
      // Importamos los ficheros que aún no están en nuestro sistema (normalmente serán todos)
      // Siempre y cuando el anuncio esté activo (Rsulta ineficiente importar imágenes
      // de anuncios "históricos"... 
      const
        urls = ad.refinedcontent.data.property.medias.map(media => media.url),
        mediakeys = ad.deleted?.at ? [] : [...(await MediaBusiness.createFromUrlsIfNotExists(urls, true, true)).values()];
      // Creamos la oferta local


      let importAdConf = await CloudProvidersModel.getProviderImportAdConf(providerId)() ?? throwUnexpectedError(`Can't find ImportAd configuration for provider ${providerId}`)

      const
        importOperationConf = ad.deleted?.at ? importAdConf.deletedAdCr : importAdConf.activeAdCr,
        localofferData = CrwMapper.mapAdToLocaloffer(ad, importOperationConf),
        externalofferData = CrwMapper.mapToExternalofferRef(providerId, ad);

      const localofferWithMediasData = {
        ...localofferData,
        property: {
          ...localofferData.property,
          propertymedias: mediakeys.map((mediakey, idx) => ({
            isFavourite: idx === 0,
            media: {
              key: mediakey,
            }
          }))
        }
      };

      await withDbTrx(async dbTran => {
        const agent_id = provider!.agent!.id!;
        const localoffer = await OfferBusiness.create(agent_id, localofferWithMediasData)(dbTran);
        const localofferId = localoffer.id ?? throwUndefinedError("localoffer.id");
        await CloudExternaloffersModel.createExternaloffer({
          ...externalofferData,
          localoffer: { id: localofferId }
        })(dbTran);
        // Publicamos la oferta en workgroup cloud (si el agente tiene permiso para hacerlo)
        if ((await workgroupMembersModel.list({ agent_id, can_publish: true, workgroup_id: C_CLOUD_WG_ID })(dbTran)).length !== 0) {
          await OfferBusiness.publishOnWorkgroup(agent_id, localofferId, C_CLOUD_WG_ID, false)(dbTran);
        }

        // Publicamos la oferta en workgroup web topbrokers (si el agente tiene permiso para hacerlo)
        if ((await workgroupMembersModel.list({ agent_id, can_publish: true, workgroup_id: C_WEB_TOPBROKERS_WG_ID })(dbTran)).length !== 0) {
          await OfferBusiness.publishOnWorkgroup(agent_id, localofferId, C_WEB_TOPBROKERS_WG_ID, false)(dbTran);
        }

        // Publicamos la oferta en workgroup "Activos Bancarios" si se trata de un activo bancario        
        if ((providerId = CloudProvidersModel.providersIds.crwhaya) || (providerId = CloudProvidersModel.providersIds.crwservihabitat) || (providerId = CloudProvidersModel.providersIds.crwsolvia) || (providerId = CloudProvidersModel.providersIds.crwunicaja) || (providerId = CloudProvidersModel.providersIds.crwportalnow) || (providerId = CloudProvidersModel.providersIds.crwaliseda)) {
          if ((await workgroupMembersModel.list({ agent_id, can_publish: true, workgroup_id: C_ACTIVOS_BANCARIOS_WG_ID })(dbTran)).length !== 0) {
            await OfferBusiness.publishOnWorkgroup(agent_id, localofferId, C_ACTIVOS_BANCARIOS_WG_ID, false)(dbTran);
          }
        }
        if (
          importOperationConf.allowAdRefresh &&
          // Si el número de medias del anuncio es menor que el teórico solicitamos un refresco de los datos al crawler.
          // No todos los anuncios tienen registrado un medias_count:  sin ese dato no ordenamos refresco
          (ad.refinedcontent.data.property.medias.length ?? 0) < (ad.refinedcontent.data.property.medias_count ?? 0) &&
          // Como protección, por si el portal está dando mal la información de imágenes teóricas o si hay algún error al decodificarlas, 
          // solo refrescamos si  tenemos 1 o ninguna imagen (si tenemos más, asumimos que ya se solicitó un refresco real del anuncio en el pasado)
          (ad.refinedcontent.data.property.medias.length ?? 0) < 2
        )
          await _callRefreshIfRequired(dbTran, localofferId);
      })();

    }


  }

  export async function updateFromCrawler(providerId: string, externalofferId: string) {
    const { logger } = appContext();
    const [ad] = await CrwUtils.getProviderAPI(providerId).listAds([externalofferId]);
    if (ad !== void 0) {
      logger.debug(`> Updating from crawler: provider ${providerId}, id=${ad.id}, revision=${ad.revision} `);
      try {
        // Importamos los ficheros que aún no están en nuestro sistema
        const
          urls = ad.refinedcontent.data.property.medias.map(media => media.url);
        const
          mediakeys = [...(await MediaBusiness.createFromUrlsIfNotExists(urls, true, true)).values()];


        await withDbTrx(async dbTran => {
          let [provider] = await CloudProvidersModel.readN([providerId])(dbTran);

          let agent_id = provider?.agent?.id ?? throwUnexpectedError(`Provider ${providerId} doesn't exist`);

          const externalofferRef = CrwMapper.mapToExternalofferRef(providerId, ad);
          let localId = await CloudExternaloffersModel.idToLocalofferId(providerId, externalofferId)(dbTran);
          if (!localId) return;


          // Dependiendo del estado del anuncio externo y de la oferta local, debemos usar una configuración u otra para "mapear" los datos
          const offerImportConfiguration = await (async () => {
            let localOfferstatusCode = (await OffersModel.readStatusCode(agent_id, localId)(dbTran)) ?? throwUnexpectedError(`Can't read existing offer ${localId}`);
            const importAdConf = (await CloudProvidersModel.getProviderImportAdConf(providerId)(dbTran)) ?? throwUnexpectedError(`Can't find ImportAd configuration for provider ${providerId}`);
            if (ad.deleted)
              switch (localOfferstatusCode) {
                case OfferstatusCode.news: return importAdConf.deletedAdUp_news;
                case OfferstatusCode.draft: return importAdConf.deletedAdUp_draft;
                case OfferstatusCode.commercialization: return importAdConf.deletedAdUp_commercialization;
                default: return importAdConf.deletedAdUp_historic;
              }
            else
              switch (localOfferstatusCode) {
                case OfferstatusCode.news: return importAdConf.activeAdUp_news;
                case OfferstatusCode.draft: return importAdConf.activeAdUp_draft;
                case OfferstatusCode.commercialization: return importAdConf.activeAdUp_commercialization;
                default: return importAdConf.activeAdUp_historic;
              }
          })();
          // Generamos los datos con los que modificar la oferta local a partir de los datos del anuncio.
          const localOfferData = CrwMapper.mapAdToLocaloffer(ad, offerImportConfiguration);

          await OffersModel.update({ ...localOfferData, id: localId })(dbTran);
          //await OfferBusiness.update(agent_id, localId, localOfferData)(dbTran);
          await PropertymediasOps.regeneratePropertymedias(dbTran, agent_id, localId, mediakeys, false)
          await CloudExternaloffersModel.updateRevision(externalofferRef)(dbTran);
          if (
            offerImportConfiguration.allowAdRefresh &&
            // Si el número de medias del anuncio es menor que el teórico solicitamos un refresco de los datos al crawler.
            // No todos los anuncios tienen registrado un medias_count:  sin ese dato no ordenamos refresco
            (ad.refinedcontent.data.property.medias.length ?? 0) < (ad.refinedcontent.data.property.medias_count ?? 0) &&
            // Como protección, por si el portal está dando mal la información de imágenes teóricas o si hay algún error al decodificarlas, 
            // solo refrescamos si  tenemos 1 o ninguna imagen (si tenemos más, asumimos que ya se solicitó un refresco real del anuncio en el pasado)
            (ad.refinedcontent.data.property.medias.length ?? 0) < 2
          )
            await _callRefreshIfRequired(dbTran, localId);
        })();

        logger.debug(`> Updated from crawler: provider ${providerId}, id=${ad.id}, revision=${ad.revision} `);
      }
      catch(e:any){
        logger.error(`> Error Updating from: provider ${providerId}, id=${ad.id}, revision=${ad.revision}  (${e.message})`);
        throw e;
      }
    } 
  }
  export async function removeFromCrawler(cloudproviderId: string, externalofferId: string) {
    const localofferId = await CloudExternaloffersModel.idToLocalofferId(cloudproviderId, externalofferId)();
    if (localofferId)
      await OfferBusiness.remove(cloudproviderId, localofferId)();
  }
  /**
   * Si la oferta local indicada proviene de un proveedor externo (es una cloudExternaloffer):
   *  - Si es necesario, se inicia el proceso de refresco del sistema externo (el crawler) para reimportarla posteriormente.
   *  - Si hay un proceso activo actualmente, no se inicia uno nuevo.
   * @param localofferId Identificador de la oferta local que se desea refrescar
   * @returns {Promise<bool>} true si al finalizar este método, hay un proceso de refresco activo de la oferta externa asociada a la oferta local.
   */
  export function callRefreshIfRequired(localofferId: string): Promise<boolean> {
    return withDbTrx(trx => _callRefreshIfRequired(trx, localofferId))();
  }

  /**
   * Se re-importara la oferta del proveedor externo si se cumple:
   *   La oferta local indicada proviene de un proveedor externo (es una cloudExternaloffer)
   *   La versión ofrecidea por el proveedor externo es distinta a la que tenemos en local
   * Actualmente solo está implementado para crwIdealista 
   * @param localofferId 
   */
  export async function updateIfRequired(localofferId: string) {
    const externaloffer = await CloudExternaloffersModel.getByLocalofferId(localofferId)();
    if (externaloffer) {
      const [adVersion] = await CrwUtils.getProviderAPI(externaloffer.provider.id).listAdsVersions([externaloffer.id]);
      if (adVersion && adVersion.revision !== externaloffer.revision) {
        await updateFromCrawler(externaloffer.provider.id, externaloffer.id);
      }
    }

  }

  /**
   * Cuando recibimos un mensaje de que el anuncio remoto se ha refrescado, actuar en consecuencia en topbrokers
   * -> Importar el nuevo anuncio si es necesario.
   * -> Marcar que se ha obtenido la respuesta en cloudRefreshcalls.
   * @param key 
   * @param status 
   */
  export async function processCrawlerRefreshEnded(key: string, status: number) {

    const [lastRefreshCall] = await cloudRefreshcallsModel.list({ key })();
    if (lastRefreshCall) {
      if (status === 200 || status === 404 || status === 410)
        await updateFromCrawler(lastRefreshCall.externaloffer.provider.id, lastRefreshCall.externaloffer.id);

      await cloudRefreshcallsModel.update({
        ...lastRefreshCall,
        response: {
          status,
          when: new Date()
        }
      })();
    }

  }

}

async function _callRefreshIfRequired(dbTran: PoolClient, localofferId: string) {
  // Asegurarnos de que la oferta, realmente, es susceptible de ser refrescada
  const externaloffer = await CloudExternaloffersModel.getByLocalofferId(localofferId)(dbTran);
  if (externaloffer) {
    // Revisar si la oferta necesita o no ser refrescada
    const [lastRefreshCall] = await cloudRefreshcallsModel.list({
      externaloffer_id: externaloffer.id,
      externaloffer_provider_id: externaloffer.provider.id,
      orderBy: { request_when: AscOrDesc.desc },
      pagination: { limit: 1 }
    })(dbTran);
    //const [lastRefreshCall] = await db.withClient(async cli =>              MqrpcCallsModel.list(cli, { offer_id, orderBy: { request_when: AscOrDesc.desc }, pagination: { limit: 1 } }));
    if (!lastRefreshCall) {
      await _performRefreshCall(dbTran, externaloffer);
      return true;
    } else if (lastRefreshCall.response === null) {
      if (lastRefreshCall.request!.when! > DateUtils.substract(new Date(), { minutes: 20 })) {
        // Tenemos una llamada pendiente (sin respuesta) reciente:  que siga esperando
        return true;
      } else {
        // Tenemos una llamada pendiente (sin respuesta) pero esperando desde hace demasiado tiempo: generamos una nueva y que siga esperando
        await _performRefreshCall(dbTran, externaloffer);
        return true;
      }
    } else if (lastRefreshCall.response!.when! < DateUtils.substract(new Date(), { hours: 24 })) {
      // Tenemos una llamada finalizada pero no es reciente (es de hace más de 1 día): Generamos una nueva y que espere.
      await _performRefreshCall(dbTran, externaloffer);
      return true;
    } else {
      // Tenemos una llamada finalizada reciéntemente: Seguimos con el funcionamiento normal de lectura
    }
  }
  // No se ha dado ninguna condición por la que se considere que hay un refresco activo... 
  return false;



  async function _performRefreshCall(dbTran: PoolClient, externaloffer: CloudExternalofferDTO) {
    const { logger } = appContext();
    const crwApi = CrwUtils.getProviderAPI(externaloffer.provider.id);

    const key = generateKey();
    logger.info(`Performing async refreshCall to external provider for ad: ${externaloffer.id}. Call key is ${key}`);
    // Hacer aquí la llamada
    await crwApi.sendAdRefreshRequest({ request_key: key, body: { ad_key: externaloffer.id } });
    // Registrar en la BBDD
    await cloudRefreshcallsModel.create({
      key,
      externaloffer,
      request: {
        when: new Date(),
      },
      response: null
    })(dbTran);

  }
}


class CrwMapper {

  /**
   * Mapea una anuncio crawleado a un DTO de oferta 
   * @param ad Anuncio original (crawleado)
   * @param options Opciones del mapeo
   * @returns 
   */
  static mapAdToLocaloffer(ad: CrwStructs.Ad, options: CloudProviderDTO.ImportAdOperation): OfferDTO {
    const adContent = ad.refinedcontent.data;

    const offer: OfferDTO = {
      customer: options.customer_id === null ? null : options.customer_id === void 0 ? void 0 : { id: options.customer_id },
      source: extractSource(ad),
      // TODO: ¿Otras monedas?
      ...options.importModality ? {
        currency: { code: "EUR" },
        sale: adContent.sale ? {
          allowed: true,
          ...adContent.sale?.amount ? { amount: adContent.sale.amount } : {}
        } : void 0,
        rent: adContent.rent ? {
          allowed: true,
          ...adContent.rent?.amount ? { amount: adContent.rent.amount } : {}
        } : void 0,
      } : {},
      ...options.status_code === OfferstatusCode.historic ? {
        status: { code: OfferstatusCode.historic, },
        historic: { cause: { code: "sold_by_competitor" } },
      } : options.status_code ? {
        status: { code: options.status_code },
        historic: null,
      } : {},
      ...options.mandate_type_code ? {
        type: { code: OffermandatetypeCode.other },
        // Principio dle mandato: ahora
        start: new Date(),
        // Fin de mandato: 90 días despues
        end: new Date(new Date().getTime() + 90 * 24 * 3600 * 1000),
      } : {},
      ...options.importProperty ? {
        property: mapProperty(adContent.property),
      } : {
        property: {}
      },
      ...options.importDescrition ? {
        description: adContent.description ?? "",
      } : {},
      ...options.importInternals ? {
        notes: [adContent.title ?? "", adContent.source?.pageUrl ?? ""].filter(s => s.trim().length !== 0).join("\n")
      } : {}

    };

    return offer;



    function extractSource(ad: CrwStructs.Ad): OfferSourceDTO | null {
      const source = ad.refinedcontent.data.source;
      if (!source)
        return null;
      else
        return {
          pageUrl: source.pageUrl,
          reference: source.reference,
          announcedByAnIndividual: source.anouncedByAnIndividual ?? false,
          ... (source.contact?.phone || source.name) ? {
            contact: {
              phone: source.contact?.phone,
              name: source.name,
            }
          } : {},
          updated: { at: ad.refinedcontent.when }
        };
    }

    function mapProperty(property: CrwStructs.Property): PropertyDTO {
      const { logger } = appContext();
      const type_code = PropertytypeCode.parse(property.type.code);
      const subtype_code = CrwMapper.mapPropertySubtypeCode(property.subtype?.code, type_code);
      if (subtype_code === null) logger.info(`  no mapping found for property type=${property.type?.code} subtype=${property.subtype?.code} `);
      return {
        type: { code: type_code },
        subtype: subtype_code === null ? null : subtype_code === void 0 ? {} : { code: subtype_code },
        address: {
          city: { code: property.address.city.code },
        },
        zone: { id: property.zone?.id ?? property.address.zone?.id ?? throwUndefinedError("zone") },
        attributes: (attributes => ({
          totalSurfaceM2: attributes.totalSurfaceM2,
          usefulSurfaceM2: attributes.usefulSurfaceM2,
          solarSurfaceM2: attributes.solarSurfaceM2,
          totalBedroomsCount: attributes.totalBedroomsCount,
          statusCode: CrwMapper.mapPropertyStatusCode(attributes.statusCode),
          bathroomsCount: attributes.bathroomsCount,
          elevatorHas: attributes.elevatorHas,
          airConditioningCode: CrwMapper.mapAirConditioningCode(attributes.airConditioningCode),
          heatingCode: CrwMapper.mapHeatingCode(attributes.heatingCode),
          gardenCode: CrwMapper.mapNoneOwnCommunityCode(attributes.gardenCode),
          swimmingPoolCode: CrwMapper.mapNoneOwnCommunityCode(attributes.swimmingPoolCode),
          terraceHas: attributes.terraceHas,
          buildInCabinetsCount: attributes.buildInCabinetsCount,
          facadeCodes: CrwMapper.mapFacadeCodes(attributes.facadeCodes),
          orientationCodes: CrwMapper.mapOrientationCodes(attributes.orientationCodes),
          floorCode: CrwMapper.mapFloorCode(attributes.floorCode),
          constructionYear: attributes.constructionYear,
          parkingPlacesCount: attributes.parkingPlacesCount
        }))(property.attributes)
      };
    }
  }
  static mapToExternalofferRef(providerId: string, ad: CrwStructs.Ad): CloudExternalofferRefDTO {
    const revision = ad.deleted?.at && ad.deleted.at > ad.refinedcontent.when ?
      ad.deleted.at.toISOString() :
      ad.refinedcontent.when.toISOString();
    return {
      provider: { id: providerId },
      id: ad.id,
      revision
    };
  }

  private static mapPropertySubtypeCode(subtype_code: string | null | undefined, type_code: PropertytypeCode): string | null | undefined {
    switch (subtype_code) {
      case void 0: return void 0;
      case "1": return (type_code === "flat") ? subtype_code : null; // "{""default"":""Semisótano""}"
      case "10": return (type_code === "flat") ? subtype_code : null; // "{""default"":""Apartamento""}"
      case "11": return (type_code === "flat") ? subtype_code : null; // "{""default"":""Planta baja""}"
      case "13": return (type_code === "house") ? subtype_code : null; // "{""default"":""Casa""}"
      case "14": return (type_code === "house") ? subtype_code : null; // "{""default"":""Cortijo""}"
      case "17": return (type_code === "house") ? subtype_code : null; // "{""default"":""Adosada""}"
      case "18": return (type_code === "house") ? subtype_code : null; // "{""default"":""Caserio""}"
      case "19": return (type_code === "house") ? subtype_code : null; // "{""default"":""Pareada""}"
      case "2": return (type_code === "flat") ? subtype_code : null; // "{""default"":""Triplex""}"
      case "20": return (type_code === "house") ? subtype_code : null; // "{""default"":""Chalet/Torre""}"
      case "21": return (type_code === "house") ? subtype_code : null; // "{""default"":""Masía""}"
      case "23": return (type_code === "house") ? subtype_code : null; // "{""default"":""Unifamiliar""}"
      case "24": return (type_code === "house") ? subtype_code : null; // "{""default"":""Casa rústica""}"
      case "25": return (type_code === "house") ? subtype_code : null; // "{""default"":""Casa de pueblo""}"
      case "26": return (type_code === "house") ? subtype_code : null; // "{""default"":""Casa rural""}"
      case "27": return (type_code === "house") ? subtype_code : null; // "{""default"":""Bungalow""}"
      case "28": return (type_code === "house") ? subtype_code : null; // "{""default"":""Casona""}"
      case "3": return (type_code === "flat") ? subtype_code : null; // "{""default"":""Dúplex""}"
      case "4": return (type_code === "flat") ? subtype_code : null; // "{""default"":""Buhardilla""}"
      case "5": return (type_code === "flat") ? subtype_code : null; // "{""default"":""Ático""}"
      case "6": return (type_code === "flat") ? subtype_code : null; // "{""default"":""Estudio""}"
      case "7": return (type_code === "flat") ? subtype_code : null; // "{""default"":""Loft""}"
      case "8": return (type_code === "flat") ? subtype_code : null; // "{""default"":""Otro""}"
      case "9": return (type_code === "flat") ? subtype_code : null; // "{""default"":""Piso""}"
      default:
        return null;
    }
  }

  private static mapPropertyStatusCode(status_code: string | null): PropertyStatusCode | null {
    switch (status_code) {
      case "new": return PropertyStatusCode.new;
      case "second_hand": return PropertyStatusCode.second_hand;
      default: return null
    }
  }
  private static mapAirConditioningCode(code: string | null): AirConditioningCode | null {
    return code === null ? null : AirConditioningCode.tryParse(code) ?? null;
  }
  private static mapHeatingCode(code: string | null): HeatingCode | null {
    return code === null ? null : HeatingCode.tryParse(code) ?? null;
  }
  private static mapNoneOwnCommunityCode(code: string | null): NoneOwnCommunity | null {
    return code === null ? null : NoneOwnCommunity.tryParse(code) ?? null;
  }

  private static mapOrientationCodes(codes: string[] | null): OrientationCode[] | null {
    return codes === null ? null : codes.map(oc => OrientationCode.tryParse(oc)).filter(c => c != void 0) as OrientationCode[];
  }
  private static mapFacadeCodes(codes: string[] | null): FacadeCode[] | null {
    return codes === null ? null : codes.map(oc => FacadeCode.tryParse(oc)).filter(c => c != void 0) as FacadeCode[];
  }
  private static mapFloorCode(code: string | null): FloorCode | null {
    return code === null ? null : FloorCode.tryParse(code) ?? null;
  }


  private static mapCertificacionEnergetica(valor: string | null): EmissionLevelCode | null {
    switch (valor) {
      case "A": return EmissionLevelCode.A;
      case "B": return EmissionLevelCode.B;
      case "C": return EmissionLevelCode.C;
      case "D": return EmissionLevelCode.D;
      case "E": return EmissionLevelCode.E;
      case "F": return EmissionLevelCode.F;
      case "G": return EmissionLevelCode.G;
      default:
        return null;
    }
  }
  private isInLabel(label: MultilingualStrDTO, value: string): boolean {
    return keys(label).find(key => (get(label, key) as string | undefined)?.toLowerCase() === value.toLowerCase()) !== void 0;
  }
}
