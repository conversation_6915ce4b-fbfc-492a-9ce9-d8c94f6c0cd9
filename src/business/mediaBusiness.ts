import sharp from "sharp"
import { throwBusinesError, throwUnexpectedError, UnexpectedError } from "agentor-lib";
import { appContext } from "lib/AppContext";
import { downloadFile } from "lib/StreamUtils";
import { WatermarkUtils } from "lib/WatermarkUtils";
import mime from "mime";
import { MediasModel } from 'model/mediasModel';
import { nanoid } from "nanoid";
import { BusinessError } from './BusinessError';
import { MediaDTO } from "model/common/dtos/MediaDTO";
export namespace MediaBusiness {

  /**
   * Para todas las urls indicadas
   * -> Se obtiene el media cuyo origen es esa url (si existe) o se crea (si no existe)
   * El resultado es un map donde para cada url se indica el media_key asociado.
   * -> Si falla la descarga de alguna url (y se ha indicado avoidExceptions) dicha url no se incluye en el resultado
   * @param urls 
   * @param generatePublishingVersion 
   * @param avoidExceptions true -> Si una URL no puede descargarse, se continúa con la siguiete. false->Si una url no puede descargarse, se interrumpe el proceso.
   * @returns Map url=>media_key (tanto si el media se ha creado o ya existía).
   */
  export async function createFromUrlsIfNotExists(urls: string[], generatePublishingVersion: boolean = true, avoidExceptions: boolean = true): Promise<Map<string, string>> {
    let mediaskeys = new Map<string, string>();
    for (const url of urls) {
      try {
        const media_key = await createFromUrlIfNotEsists_imp(url, generatePublishingVersion);
        mediaskeys.set(url, media_key);
      } catch (e) {
        if (!avoidExceptions)
          throw e;
      }
    }
    return mediaskeys;
  }
  export async function createFromUrlIfNotExists(url: string, generatePublishingVersion: boolean = true): Promise<string> {
    return createFromUrlIfNotEsists_imp(url, generatePublishingVersion);
  }
  export async function createFromFile(file: { path: string /*, mimetype:string*/ }, generatePublishingVersion: boolean = true): Promise<string> {
    return createFromFile_imp(file, generatePublishingVersion);
  }

  async function createFromUrlIfNotEsists_imp(url: string, generatePublishingVersion: boolean = true): Promise<string> {
    const { tmpfiles,logger } = appContext();

    const [media] = await MediasModel.list({ source_url: url })();
    if (media)
      return regenerateIfRequired_imp(media, generatePublishingVersion);
    else {
      return tmpfiles.withTmpFile(".tmp", async tmpFile_path => {
        logger.debug(`Downloading from ${url}`);
        try {
          await downloadFile(url, tmpFile_path);
        } catch (e) {
          console.error(`Problems downloading from url ${url}`);
          throw e;
        }
        return await createFromFile_imp({ path: tmpFile_path }, generatePublishingVersion, url);
      });
    }
  }

  /**
   * Si alguno de las versiones del media (thumbnail o publishing o publishing_no_wm) debe regenerarse,
   * se regenera.
   * Es útil para medias "antiguos" en los que no existe la versión "publishing_no_wm", pero puede ser util
   * para otros casos en el futuro
   * @param media Media ya existente
   * @param generatePublishingVersion El media necesita version para publicación (con/sin watermark)
   * @returns 
   */
  async function regenerateIfRequired_imp(media: MediaDTO, generatePublishingVersion: boolean = true): Promise<string> {
    const { tmpfiles, storage } = appContext();

    const media_key = media.key ?? throwUnexpectedError("Unexpected: media without key");
    if (!media.thumbnail || generatePublishingVersion && (!media.publishing || !media.publishingNoWm)) {
      const
        original_url = media.original?.url ?? throwUnexpectedError("Unexpected: media without original url"),
        original_mediatype = media.original?.mediatype ?? throwUnexpectedError("Unexpected: media without original mediatype"),
        original_extension = mime.getExtension(original_mediatype);

      await tmpfiles.withTmpFile(`.${original_extension}`, async tmpFile_path => {
        try {
          await downloadFile(original_url, tmpFile_path);
        } catch (e) {
          console.error(`Problems downloading from url ${original_url}`);
          throw e;
        }

        const image = sharp(tmpFile_path);
        const { format, width, height } = await image.metadata();
        if (format === void 0 || width === void 0 || height === void 0)
          throw new UnexpectedError("Missing file format or dimensions");
        else if (!["jpeg", "png"].includes(format))
          throw new BusinessError(`Unsupported image file "${format}"`);
        else {
          const media_folder = media.folder ?? throwUnexpectedError("Unexpected: media without folder");
          const
            thumbnail = media.thumbnail ?? await tmpfiles.withTmpFile(".jpg", async (fileName) => {
              const thumbnailKey = s3MediaKey(media_folder, "thumbnail.jpg");
              const scale = 256 / Math.max(width, height, 1);
              await image.resize({ width: Math.max(1, Math.round(width * scale)), height: Math.max(1, Math.round(height * scale)), fit: "cover" }).jpeg().toFile(fileName);
              const uploaded = await storage.uploadFile(fileName, "image/jpeg", thumbnailKey);
              return {
                url: uploaded.url,
                mediatype: "image/jpg"
              }
            }),
            publishingNoWm = !generatePublishingVersion ? null :
              media.publishingNoWm ?? await tmpfiles.withTmpFile(`.jpg`, async (publishingNoWmPath) => {
                //console.debug(`Removing Idealista WM (medias/${media_folder}/publishing.jpg)`)
                await WatermarkUtils.removeIdealistaWM(tmpFile_path, publishingNoWmPath);
                const publishingKey = s3MediaKey(media_folder, "publishing_no_wm.jpg");
                const uploaded = await storage.uploadFile(publishingNoWmPath, "image/jpeg", publishingKey);
                return {
                  url: uploaded.url,
                  mediatype: "image/jpg"
                };
              }),
            publishing = !generatePublishingVersion ? null :
              media.publishing ?? await tmpfiles.withTmpFile(`.jpg`, (nowmPath) => tmpfiles.withTmpFile(`.jpg`, async (publishingPath) => {
                //console.debug(`Removing Idealista WM (medias/${media_folder}/publishing.jpg)`)
                await WatermarkUtils.removeIdealistaWM(tmpFile_path, nowmPath);
                await WatermarkUtils.addTopbrokersWM(nowmPath, publishingPath);
                const publishingKey = s3MediaKey(media_folder, "publishing.jpg");
                const uploaded = await storage.uploadFile(publishingPath, "image/jpeg", publishingKey);
                return {
                  url: uploaded.url,
                  mediatype: "image/jpg"
                };
              }));

          await MediasModel.update({
            ...media,
            thumbnail,
            publishing,
            publishingNoWm,
          })();
        }


      });

    }
    return media_key;
  }


  async function createFromFile_imp(file: { path: string /*, mimetype:string*/ }, generatePublishingVersion: boolean = true, sourceUrl?: string): Promise<string> {
    const { tmpfiles, storage } = appContext();

    const image = sharp(file.path);
    const { format, width, height } = await image.metadata();
    if (format === void 0 || width === void 0 || height === void 0)
      throw new UnexpectedError("Missing file format or dimensions");
    else if (!["jpeg", "png"].includes(format))
      throw new BusinessError(`Unsupported image file "${format}"`);
    else {

      const mimetype = `image/${format}`;
      const extension = mime.getExtension(mimetype) ?? throwBusinesError(`Can't process mimetype ${mimetype}`);
      const media_key = nanoid();
      // Los ficheros asociados a un *media* dejan de organizarse por agente en S3.  
      // Para mantener compatibilidad, se usan los 3 primeros caracteres de la clave como carpeta (lo cual facilita navegar por el arbol de ficheros en S3)
      const media_folder = `${media_key.substring(0, 3)}/${media_key}`;

      const
        storedOriginal = await (async () => {
          const originalKey = s3MediaKey(media_folder, `original.${extension}`);
          return storage.uploadFile(file.path, mimetype, originalKey);
        })(),
        // 
        storedThumbnail = await tmpfiles.withTmpFile(".jpg", async (fileName) => {
          const thumbnailKey = s3MediaKey(media_folder, "thumbnail.jpg");
          const scale = 256 / Math.max(width, height, 1);
          await image.resize({ width: Math.max(1, Math.round(width * scale)), height: Math.max(1, Math.round(height * scale)), fit: "cover" }).jpeg().toFile(fileName);
          return storage.uploadFile(fileName, "image/jpeg", thumbnailKey);
        }),
        storedPublishingNoWm = !generatePublishingVersion ? null :
          await tmpfiles.withTmpFile(`.jpg`, async (publishingNoWmPath) => {
            //console.debug(`Removing Idealista WM (medias/${media_folder}/publishing.jpg)`)
            await WatermarkUtils.removeIdealistaWM(file.path, publishingNoWmPath);
            const publishingKey = s3MediaKey(media_folder, "publishing_no_wm.jpg");
            return storage.uploadFile(publishingNoWmPath, "image/jpeg", publishingKey);
          }),
        storedPublishing = !generatePublishingVersion ? null :
          await tmpfiles.withTmpFile(`.jpg`, (nowmPath) => tmpfiles.withTmpFile(`.jpg`, async (publishingPath) => {
            //console.debug(`Removing Idealista WM (medias/${media_folder}/publishing.jpg)`)
            await WatermarkUtils.removeIdealistaWM(file.path, nowmPath);
            await WatermarkUtils.addTopbrokersWM(nowmPath, publishingPath);
            const publishingKey = s3MediaKey(media_folder, "publishing.jpg");
            return storage.uploadFile(publishingPath, "image/jpeg", publishingKey);
          }));

      return MediasModel.create({
        key: media_key,
        folder: media_folder,
        original: {
          url: storedOriginal.url,
          mediatype: mimetype
        },
        thumbnail: {
          url: storedThumbnail.url,
          mediatype: "image/jpg"
        },
        publishing: storedPublishing ? {
          url: storedPublishing.url,
          mediatype: "image/jpg"
        } : null,
        publishingNoWm: storedPublishingNoWm ? {
          url: storedPublishingNoWm.url,
          mediatype: "image/jpg"
        } : null,
        source: sourceUrl ? {
          url: sourceUrl
        } : void 0,
      })();
    }

  }


  //  /** Regenera las imágenes derivadas de la original */
  //  async function regenerateDerivatives(media_key: string) {
  //    const [processed] = (await MediasModel.list({ key: media_key })()).slice(0, 1).map(media => {
  //      const { original } = media;
  //
  //
  //
  //    });
  //
  //  }

  function s3MediaKey(mediaFolder: string, mediaName: string): string {
    return `medias/${mediaFolder}/${mediaName}`;
  }

}