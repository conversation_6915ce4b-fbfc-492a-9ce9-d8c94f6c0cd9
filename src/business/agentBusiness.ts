import { pick } from "lodash";
import { AgentsModel } from 'model/agentsModel';
import { AgentDTO } from 'model/common/dtos/AgentDTO';
import { AgenttypeCode } from "model/common/dtos/AgenttypeDTO";
import { ScrtyCredentialsDTO } from "model/common/dtos/ScrtyCredentialsDTO";
import { ValidationcodeItemType } from 'model/common/dtos/ValidationcodeDTO';
import { WorkgroupMemberDTO } from 'model/common/dtos/WrokgroupMemberDTO';
import { ReservedWorkgroupIds } from 'model/consts';
import { EcomAccountsModel } from 'model/ecomaccountsModel';
import { scrtyCredentialsModel } from "model/scrtyCredentialsModel";
import { scrtyUserModel } from "model/scrtyUserModel";
import { ValidationcodesModel } from 'model/validationcodesModel';
import { workgroupMembersModel } from 'model/workgroupsMembersModel';
import { customAlphabet } from "nanoid";
import { withAppContext } from '../lib/AppContext';
import { EmailAlredyExistsError, EmailDoesntExistError, throwUndefinedError, UnknownValidationCodeError } from './BusinessError';
import { ecomDepositBusiness } from "./ecomDepositBusiness";

const codeGenerator = customAlphabet("abcdefghijklmnopqrstuvwxyz012345678", 6);
const C_INITIAL_DEPOSIT_VALUE = 50;
/** Grupos de los que será miembro el nuevo usuario (y con qué permisos)*/
const c_default_memberships: WorkgroupMemberDTO[] = [

  // Cloud
  { can: { read: true, publish: false }, workgroup: { id: ReservedWorkgroupIds.CLOUD } },
  ...ReservedWorkgroupIds.activePortals.map(portalWkId => (
    { can: { read: false, publish: true }, workgroup: { id: portalWkId } })
  )

];


export namespace AgentBusiness {

  export async function createValidationcode(email: string) {
    return withAppContext(async ({ db, email: smtp, logger }) => {

      const validationcode = await db.withTransaction(async dbTran => {
        if (0 === await AgentsModel.count({ email })(dbTran))
          return ValidationcodesModel.create({
            code: codeGenerator(),
            item: {
              value: email,
              type: ValidationcodeItemType.email
            }
          })(dbTran);
        else
          throw new EmailAlredyExistsError();
      })();

      const messageId = await smtp.send({
        to: email,
        subject: "Tu código de validación",
        //        text: `Tu código de validación para registrarte en topbrokers.io es\n${validationcode.code}\nPor favor, introduce este vaor en el campo "Código de validación" del formulario de registro`,
        html: `
          <p>Tu código de validación para registrarte en topbrokers.io es</p>
          <p><big><pre>${validationcode.code}</pre></big></p>
          <p>Por favor, introduce este valor en el campo "Código de validación" del formulario de registro</p>
        `
      });
      logger.debug("Registratrion Validation Code Email Sent", messageId);
    });

  }

  export async function createPasswordValidationcode(email: string) {
    return withAppContext(async ({ email: smtp, db, logger }) => {

      const validationcode = await db.withTransaction(async dbTran => {
        if (1 === await AgentsModel.count({ email })(dbTran))
          return ValidationcodesModel.create({
            code: codeGenerator(),
            item: {
              value: email,
              type: ValidationcodeItemType.email
            }
          })(dbTran);
        else
          return null;
      })();
      if (validationcode !== null) {
        const html = `
        <p>Tu código de validación para cambiar de contraseña en topbrokers.io es</p>
        <p><big><pre>${validationcode.code}</pre></big></p>
        <p>Por favor, introduce este valor en el campo "Código de validación" del formulario de cambio de contraseña</p>
        `;
        const messageId = await smtp.send({
          to: email,
          subject: "Tu código de validación",
          html
        });
        logger.debug("Password recovery email sent", messageId);
      }
    });
  }

  /**
   * Crea un nuevo agente, una cuenta de eCommerce asociada y un usuario asociado con las credenciales indicadas.
   * - La cuenta creada tendrá un balance inicial de 50€ (se genera un demósito "manual" de 50€)
   * - Las credenciales generadas usan el email del agente como username
   * La creación del nuevo agente es notificada al email "<EMAIL>".  
   * El envío del email se realiza en paralelo y puede fallar (sin afectar al resultado de la operación de negocio)
   * @param validationcode 
   * @param agentData 
   * @param credentials Credenciales (username y passsword) del nuevo usuario.  credentials.username no se usa (se usa el email del agente).
   * @returns 
   */
  export async function createAgent(validationcode: string, agentData: AgentDTO, credentials: ScrtyCredentialsDTO) {
    return withAppContext(({ db, email: smtp }) =>
      db.withTransaction(async dbTran => {
        if (0 !== await AgentsModel.count({ email: agentData.email ?? throwUndefinedError("Agent email") })(dbTran))
          throw new EmailAlredyExistsError("email is alredy registered");
        else if (0 === await ValidationcodesModel.count({ code: validationcode, item_value: agentData.email ?? throwUndefinedError(), item_type: ValidationcodeItemType.email, allow_expired: false })(dbTran))
          throw new UnknownValidationCodeError("validation code doesn't exist or has been expired");
        else {
          const newAgent = await AgentsModel.create({
            ...agentData,
            type: {
              code: agentData.email?.endsWith("@topbrokers.io") || agentData.email?.endsWith("@perpetva.com") ? AgenttypeCode.professional : AgenttypeCode.individual
            }
          })(dbTran);
          // Vinculamos al agente con todos los grupos de trabajo "por defecto"
          for (const membership of c_default_memberships) {
            await workgroupMembersModel.create({
              ...membership,
              agent: {
                id: newAgent.id ?? throwUndefinedError()
              },
            })(dbTran);
          }
          // Creamos una cuenta de eCommerce asociada al agente
          const account = await EcomAccountsModel.create({ agent: { id: newAgent.id ?? throwUndefinedError() }, balance: 0, })(dbTran);
          if (C_INITIAL_DEPOSIT_VALUE > 0)
            // Le "regalamos" 30€
            await ecomDepositBusiness.createManual(dbTran, account.id!, C_INITIAL_DEPOSIT_VALUE);

          // Creamos el usuario "por defecto" ...
          const userId = await scrtyUserModel.create({ agent: newAgent, name: `Agent ${newAgent.email}` })(dbTran);
          // ... y sus credenciales 
          await scrtyCredentialsModel.create({
            user: { id: userId },
            // Usamos el email del agente como username (No permitimos que sea diferente al crear agente)
            username: agentData.email,
            password: credentials.password
          })(dbTran);

          return newAgent;
        }
      })().then(agent => {
        smtp.sendWithNoWait({
          to: "<EMAIL>",
          subject: `Registrado nuevo agente ${agent.firstName} ${agent.lastName}`,
          html: `<p>Registrado nuevo agente</p><pre>${JSON.stringify(pick(agent, ["firstName", "lastName", "email", "mobile"]))}</pre>`
        });
        return agent;
      })
    );
  }

  /**
   * Se mantiene por compatibilidad con la aplicación agentorapp.
   *   - Anteriormente, la contraseña era un dato más del agente.
   *   - Actualmente un agente tiene varios usuarios con sus propias credenciales.   
   * Se lee como "modificar la contraseña del agente con email @email", pero en realidad hace: "modificar las credenciales del usuario asociado al agente cuyo username es el  email indicado"
   * 
   * Modifica las credenciales del usuario cuyo username coincide con el email del agente.
   * El usuario debe ser usuario del agente
   * @param validationcode 
   * @param email 
   * @param password 
   * @returns 
   */
  export async function changePassword(validationcode: string, email: string, password: string) {
    return withAppContext(({ db }) =>
      db.withTransaction(async dbTran => {
        if (0 === await ValidationcodesModel.count({ code: validationcode, item_value: email, item_type: ValidationcodeItemType.email, allow_expired: false })(dbTran))
          throw new UnknownValidationCodeError("validation code doesn't exist or has been expired");
        else {

          const [credentials] = await scrtyCredentialsModel.list({ username: email })(dbTran);
          if (!credentials)
            throw new EmailDoesntExistError("email doesn't exist");
          else if (!credentials.user?.agent)
            // Unexpected: usuario sni agente
            throw new EmailDoesntExistError("email doesn't exist");
          else
            await scrtyCredentialsModel.update({ username: email, password: password })(dbTran);
        }
      })()
    );
  }
  //#endregion
}