import { property } from "lodash";
import { PropertymediaDTO } from "model/common/dtos/PropertymediaDTO";
import { OffersModel } from "model/offersModel";
import { PropertymediasModel } from "model/propertymediasModel";
import * as pg from "pg";

export namespace PropertymediasOps {
  export async function createPropertymedia(dbTrans: pg.PoolClient, myAgent_id: string, offer_id: string, media_key: string, markOfferAsUpdated: boolean = true): Promise<PropertymediaDTO | null> {
    // Verify agent is the owner of the property
    const [property_id] = await OffersModel.listPropertyId({ id: offer_id, agent_id: myAgent_id })(dbTrans);
    if (!property_id) {
      return null;
    } else {
      const favouritesCount = await PropertymediasModel.count({ property_id, isFavourite: true })(dbTrans);
      // Si ninguna imagen es favorita, marcamos la actual como favorita
      const propertymedia = await PropertymediasModel.create({
        property: { id: property_id },
        media: { key: media_key },
        isFavourite: favouritesCount === 0
      })(dbTrans);
      // Forzamos la actualización de la oferta (para que conste a la hora de, por ejemplo, publicarla en portales)
      if (markOfferAsUpdated)
        await OffersModel.update({ id: offer_id })(dbTrans);
      return propertymedia;
    }
  }

  export async function regeneratePropertymediasByPropertyId(dbTrans: pg.PoolClient, property_id: string, media_keys: string[], favouriteMediaKey?: string): Promise<PropertymediaDTO[]> {

    const propertyMedias = await PropertymediasModel.list({ property_id })(dbTrans);

    const keysToDelete = propertyMedias.map(pm => pm.media?.key).filter(key => key && !media_keys.includes(key)) as string[];
    if (keysToDelete.length !== 0)
      await PropertymediasModel.remove({ property_id, media_keys: keysToDelete })(dbTrans);


    // Si se indica una clave de media favorita, aseguramos que sea una de las indicadas
    favouriteMediaKey = favouriteMediaKey && media_keys.find(mk => mk === favouriteMediaKey);
    // Si no se ha indicado un favorito y no hay un favorito actual, usaremos la primera key como favotita
    if (!favouriteMediaKey && !propertyMedias.find(pm => pm.isFavourite && !keysToDelete.includes(pm.media!.key!))) {
      favouriteMediaKey = media_keys[0];
    }

    let result: PropertymediaDTO[] = [];
    for (const media_key of media_keys) {
      const existing = propertyMedias.find(pm => pm.media?.key === media_key);
      if (existing && !existing.isFavourite && favouriteMediaKey === media_key ) {
        await PropertymediasModel.setFavourite(property_id, media_key)(dbTrans);
        result.push({ ...existing, isFavourite: true });
      } else if (existing && existing.isFavourite && favouriteMediaKey && favouriteMediaKey !== media_key ) {
        await PropertymediasModel.unsetFavourite(property_id, media_key)(dbTrans);
        result.push({ ...existing, isFavourite: false });
      } else if (existing) {
        result.push(existing);
      } else {
        result.push(
          await PropertymediasModel.create({
            property: { id: property_id },
            media: { key: media_key },
            isFavourite: favouriteMediaKey === media_key
          })(dbTrans)
        );

      }

    }

    return result;
  }
  export async function regeneratePropertymedias(dbTrans: pg.PoolClient, myAgent_id: string, offer_id: string, media_keys: string[], markOfferAsUpdated: boolean = true, favouriteMediaKey?: string): Promise<PropertymediaDTO[]> {
    // Verify agent is the owner of the property
    const [property_id] = await OffersModel.listPropertyId({ id: offer_id, agent_id: myAgent_id })(dbTrans);
    if (!property_id) {
      return [];
    } else {
      const result = await regeneratePropertymediasByPropertyId(dbTrans, property_id, media_keys, favouriteMediaKey);
      // Forzamos la actualización de la oferta (para que conste a la hora de, por ejemplo, publicarla en portales)
      if (markOfferAsUpdated)
        await OffersModel.update({ id: offer_id })(dbTrans);

      return result;
    }
  }
}