import { appContext } from 'lib/AppContext';
import { withDbTrx } from 'lib/AppContextUtils';
import { Tusolucionhipotecaria } from 'lib/Tusolucionhipotecaria';
import { ActionsModel } from 'model/actionsModel';
import { ActiontypesModel } from 'model/actiontypesModel';
import { AgentsModel } from 'model/agentsModel';
import { AscOrDesc } from 'model/common/AscOrDesc';
import { ActionDTO } from 'model/common/dtos/ActionDTO';
import { ActiontypeId } from 'model/common/dtos/ActiontypeDTO';
import { AgenttypeCode } from 'model/common/dtos/AgenttypeDTO';
import { MultilingualStrDTO } from 'model/common/dtos/MultilingualStrDTO';
import { OfferversiontypeCode } from 'model/common/dtos/offerversiontypesDTO';
import { ContactsModel } from 'model/contactsModel';
import { OffersModel } from 'model/offersModel';
import { PoolClient } from 'pg';
import { LogicEntityError, throwBusinesError, throwUndefinedError, UnknownEntityError } from './BusinessError';
import { csiteEnabledagentsModel } from 'model/csiteEnabledagentsModel';
import { csiteSitesModel } from 'model/csiteSiteModel';
import { csiteOpportunitiesModel } from 'model/csiteOpportunitiesModel';

export namespace ActionBusiness {
  /**
   * Creación de acciones en nombre de un operador.
   * Se permite crear acciones cuyo tipo no puede ser empleado directamente por el agente.
   * @param agent_id Identificador del agente al que pertenece la acción
   * @param data 
   * @returns 
   */

  export function createByOperator(agent_id: string, data: ActionDTO): (dbTran?: PoolClient) => Promise<ActionDTO> {
    return withDbTrx(dbTran => create_imp(dbTran, agent_id, data, false));
  }
  /**
   * Creación de 1 acción en nombre de un agente
   * El tipo de la acción debe permitir acceptAgentCreation
   * @param agent_id Identificador del agente al que pertenece la acción
   * @param data 
   * @returns 
   */
  export function createByAgent(agent_id: string, data: ActionDTO): (dbTran?: PoolClient) => Promise<ActionDTO> {
    return withDbTrx(dbTran => create_imp(dbTran, agent_id, data, true));
  }

  async function create_imp(dbTran: PoolClient, agent_id: string, data: ActionDTO, createdByAgent: boolean): Promise<ActionDTO> {
    const { tusolucionhipotecaria } = appContext();

    // 0.- Leemos la definición del tipo de acción
    const actiontypeId = data.type?.id ?? throwUndefinedError("action type");
    const [actiontype] = await ActiontypesModel.list(dbTran)({ id: actiontypeId });
    if (!actiontype)
      throw new UnknownEntityError(`Unknown action type "${actiontypeId}"`)
    else if (createdByAgent && !actiontype.agentCanCreate)
      throw new LogicEntityError(`Action type ${actiontypeId} is for internal only use`)
    else if (actiontype.contactIsRequired === true && !data.contact?.id)
      throw new LogicEntityError("Contact is required")
    else if (actiontype.offerIsRequired === true && !data.offer?.id)
      throw new LogicEntityError("Offer is required")
    else {
      const offer_id = data.offer?.id;
      const contact_id = data.contact?.id;

      // 1.- Debemos verificar que las entidades asociadas a la acción pertenecen al agente
      if (offer_id)
        // Puedo crear acciones asociadas a mis ofertas o a ofertas compartidas conmigo.
        // nota: Solo yo podré ver mis acciones
        if (0 === await OffersModel.count({ id: offer_id, accessorCondition: { id: agent_id, includeMine: true, includeNotMine: true } })(dbTran))
          throw new UnknownEntityError("Unknown offer")

      if (contact_id)
        if (0 === await ContactsModel.count({ id: contact_id, agent_id: agent_id })(dbTran))
          throw new UnknownEntityError("Unknown contact")

      var dataPatch = {};
      if (actiontype?.service != null) {
        // 2.- Si la acción implica la ejecución de un servicio, debe hacerse
        if (actiontype!.service!.code === "tu_solucion_hipotecaria.mortagerequest")
          await buildLead(dbTran, agent_id, contact_id, offer_id, data.description).then(lead => {
            tusolucionhipotecaria.sendLead(lead, true);
          })
        // Las acciones que ejecutan un servicio se dan por "hechas" de forma inmediata (y con fecha la actual)
        dataPatch = { when: new Date(), done: true }
      } else if (actiontypeId === ActiontypeId.moreInformationRequest) {
        dataPatch = await execMoreInfoRequest_tasks(dbTran, agent_id, data);
      } else if (actiontypeId === ActiontypeId.buyerSurvey) {
        dataPatch = await execBuyerScoreSurvery_tasks(dbTran, agent_id, data);
      }
      // 3.- Creamos la acción
      const { id } = await ActionsModel.create({ ...data, agent: { id: agent_id }, ...dataPatch })(dbTran);
      const [action] = await ActionsModel.list({ id: id! })(dbTran);
      return action;
    }




    /**
     * Las solicitudes de más información tienen tareas "asociadas" que deben ejecutarse antes de crear la acción.
     * En concreto, la notificación del lead hipotecario a TSH (cuando sea necesario)
     * NOTA:
     *  
     * Las solicitudes de + información generan un lead hipotecario hacia TSH (en determinadas cirscunstancias)
     *  -> Cuando la oferta es bancaria
     *  -> Cuando la oferta no es bancaria y es la primera realizada por el contacto
     * Se exceptúan si la oferta es una "versión de tipo Pago por cuotas", en cuyo caso la notificación será al recibir
     * la respuesta a la encuesta de scoring de comprador
     *
     * @param dbTran 
     * @param data 
     * @returns Un objeto indicando si la acción debe ser condiderada "done" y, en caso positivo, en qué fecha se debe dar por hecha
     */
    async function execMoreInfoRequest_tasks(dbTran: PoolClient, agent_id: string, data: ActionDTO): Promise<{ when: Date, done: true } | {}> {
      // La oferta de la acción es obligatoria
      const offer_id = data.offer?.id ?? throwBusinesError("Unexpected error ERR20220603T120201");
      // El contacto de la acción es obligatorio.
      const contact_id = data.contact?.id ?? throwBusinesError("Unexpected error ERR20220202T190430");
      // El agente tiene definido un "dominio" de gestión de oportunidades?  (solo puede tener uno)
      const agentMatcherDomain = await csiteEnabledagentsModel.list({ agent_id })(dbTran);
      if (agentMatcherDomain) {
        // Para que el contacto pueda acceder a sus oportunidades, necesitará un contactsite: se crea si no existe
        if (!await csiteSitesModel.exists({ contact_id })(dbTran))
          await csiteSitesModel.create({ contact: { id: contact_id } })(dbTran);
        // Creamos la oportunidad inicial si no existe
        await csiteOpportunitiesModel.createIfNotExists({
          contact: { id: contact_id },
          offer: { id: offer_id },
          reaction: { likedit: true, date: data.when ?? new Date() }
        })(dbTran);
        // El proceso de oportunidades se detectarán en batch (tarea de detección de oportunidades)
      }
      // ¿Es la oferta una versión de tipo cuotas?
      const isMontlypaymentVersion = 0 !== await OffersModel.count({ id: offer_id, version_type_codes: [OfferversiontypeCode.monthlypayment, OfferversiontypeCode.monthlypayment_with_reformproject] })(dbTran);
      // Las versiones de ofertas pensadas para cuotas no deben notificarse directamente a TSH
      // Para estos casos, debemos esperar a las acciones de tipo BuyerSurvey 
      if (!isMontlypaymentVersion) {
        // ¿Es la primera solicitud de información que nos envía el contacto?
        const isFirstTime: boolean = 0 === await ActionsModel.count({ agent_id, contact_id, type_id: ActiontypeId.moreInformationRequest, pagination: { limit: 1 } })(dbTran);
        // ¿Es el cliente ofertante un BankServicer ?
        const isBankServicer: boolean = 0 !== await OffersModel.count({ id: offer_id, customer_isBankServicer: true, accessorCondition: { id: agent_id, includeMine: true, includeNotMine: true } })(dbTran);

        if (isBankServicer || isFirstTime)
          await buildLead(dbTran, agent_id, contact_id, offer_id, data.description).then(lead =>
            isBankServicer ?
              tusolucionhipotecaria.sendBankservicerLead(lead) :
              tusolucionhipotecaria.sendLead(lead, false)
          );
        return { when: new Date(), done: true }
      } else {
        return { when: new Date(), done: true };
      }

    }

    /**
     * Las Notificación de una encuesta de calificación a un comprador  tienen tareas "asociadas" que deben ejecutarse antes de crear la acción.
     * En concreto, la notificación del lead hipotecario a TSH (cuando sea necesario)
     * NOTA:
     *  
     * En registro de una encuesta  generan un lead hipotecario hacia TSH (en determinadas cirscunstancias)
     *  -> Cuando la oferta es una "versión" de otra oferta y tipo de versión es "Pago por cuotas"
     *
     * @param dbTran 
     * @param data 
     * @returns Un objeto indicando si la acción debe ser condiderada "done" y, en caso positivo, en qué fecha se debe dar por hecha
     */
    async function execBuyerScoreSurvery_tasks(dbTran: PoolClient, agent_id: string, data: ActionDTO): Promise<{ when: Date, done: true } | {}> {
      // La oferta de la acción es obligatoria
      const offer_id = data.offer?.id ?? throwBusinesError("Unexpected error ERR20220603T120201");
      // El contacto de la acción es obligatorio.
      const contact_id = data.contact?.id ?? throwBusinesError("Unexpected error ERR20220202T190430");
      // ¿Es la oferta una versión de tipo cuotas?
      const isMontlypaymentVersion = 0 !== await OffersModel.count({
        id: offer_id, version_type_codes: [
          OfferversiontypeCode.monthlypayment,
          OfferversiontypeCode.monthlypayment_with_reformproject
        ]
      })(dbTran);
      if (isMontlypaymentVersion) {
        // Necesitamos recurperar la acción "original" (solicitud de + información) que es la que realmente manda.
        //  - Última acción de solicitud de + información 
        //  - En estado pendiente
        //  - De la oferta
        //  - Del contacto
        // Si hay más de una, recogemos la última.
        const [pendingInfoRequest] = await ActionsModel.list({ agent_id, offer_id, contact_id, type_id: ActiontypeId.moreInformationRequest, done: false, orderby: { when: AscOrDesc.desc } })(dbTran);
        if (!pendingInfoRequest)
          throwBusinesError("Unexpected error ERR20220822T145433\nScoringSurvey action without pending +InfoRequest action");

        await buildLead(dbTran, agent_id, contact_id, offer_id, pendingInfoRequest.description, data.extradata).then(lead =>
          tusolucionhipotecaria.sendLead(lead, false)
        );
        // La solicitud de información original la damos por "hecha"
        // Si recibimos otro tally, se producirá una excepción ERR20220822T145433
        await ActionsModel.update({ id: pendingInfoRequest.id, done: true })(dbTran);

        return { when: new Date(), done: true }
      } else {
        return {};
      }

    }

    async function buildLead(dbTran: PoolClient, agent_id: string, contact_id?: string, offer_id?: string, notes?: string | null, extradata?: object | null): Promise<Tusolucionhipotecaria.Lead> {
      const
        agent = await AgentsModel.read(agent_id)(dbTran) ?? throwUndefinedError("agent"),
        contact = contact_id ?
          await ContactsModel.read(agent_id, contact_id)(dbTran) ?? throwBusinesError(`Unknown contact ${data.contact?.id}`) :
          void 0,
        offer = offer_id ?
          await OffersModel.read(agent_id, offer_id)(dbTran) :
          void 0;

      const lead: Tusolucionhipotecaria.Lead = {
        agent: {
          id: agent.id ?? throwUndefinedError("agent id"),
          firstName: agent.firstName ?? throwUndefinedError("agent firstName"),
          lastName: agent.lastName ?? null,
          email: agent.email ?? throwUndefinedError("agent email"),
          mobile: agent.mobile ?? null
        },
        ...contact ? {
          applicant: {
            id: contact.id ?? throwUndefinedError("contact id"),
            firstName: contact.firstName ?? throwUndefinedError("contact first name"),
            lastName: contact.lastName ?? null,
            email: contact.email ?? null,
            mobile: contact.mobile ?? null
          }
        } : agent.type?.code == AgenttypeCode.individual ? {
          applicant: {
            firstName: agent.firstName ?? throwUndefinedError("contact first name"),
            lastName: agent.lastName ?? null,
            email: agent.email ?? null,
            mobile: agent.mobile ?? null
          }
        } : {},
        ...offer ? {
          offer: {
            id: offer.id ?? "",
            property_type_name: getEs(offer.property?.type?.label),
            property_address_city_name: getEs(offer.property?.address?.city?.label),
            property_address_city_province_name: getEs(offer.property?.address?.city?.province?.label),
            property_address_streettype_name: getEs(offer.property?.address?.streettype?.label),
            property_address_streetname: offer.property?.address?.streetname ?? null,
            property_address_number: offer.property?.address?.number ?? null,
            property_attributes_totalSurfaceM2: offer.property?.attributes?.totalSurfaceM2 ?? null,
            rent_amount: offer.rent?.amount ?? null,
            sale_amount: offer.sale?.amount ?? null,
            sale_monthlypayment: offer.sale?.monthlyPayment ?? null,
            customer: offer.customer ? {
              email: offer.customer.email ?? null,
              phone: offer.customer.mobile ?? null,
              name: [offer.customer.firstName, offer.customer.lastName].map(token => token?.trim()).filter(token => !!token).join(" "),
              isBankServicer: offer.customer.isBankServicer ?? false
            } : null,
            ...offer.version ? {
              version: {
                type_code: offer.version?.type?.code ?? "",
                type_label: getEs(offer.version?.type?.label),
                disclaimer: offer.version?.disclaimer ?? ""
              },
            } : {
              version: null
            }

          }
        } : {},
        ...notes ? { notes } : {},
        ...extradata ? { survey: Tusolucionhipotecaria.obj2TallyFormResponse(extradata) } : {}
      }

      return lead;
    }

    function getEs(multilang?: MultilingualStrDTO): string {
      return multilang?.es ?? multilang?.default ?? "";
    }
  }



}