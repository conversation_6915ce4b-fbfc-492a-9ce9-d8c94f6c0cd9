sour###
topbrokers.io: Eliminar marcas de agua de idealista y añadir marca tb

Basicamente se usa la libreria opencv y en concreto la funcion inpaint que permite entre otras cosas restaurar una region de la imagen seleccionada usando los pixeles adyacentes:

>cv2.inpaint(image, mask, radius, flags=paint_method)

Como todas las fotos de idealista llevan su logo en la misma posicion (centro/centro), usamos una mascara de seleccion de 2000x2000 pixeles con el logo en el centro que recortamos manteniendo el centro para adaptarla a la imagen de entrada (no todas tienen el mismo tamaño). "Reparamos" el hueco enmascarado con los pixeles adyacentes que es lo mismo que "borrar" la marca.

Por ultimo se añade la marca de agua de topbrokers.io con un tamaño un poco más grande en posición centrada.

COMO LEVANTAR EL ENTORNO PARA EJECUTAR EL SCRIPT

- Se requiere Python 3.9.x   (evitar 3.10 de momento 15/10/21)
  
- [Clonar el repositorio](https://gitlab.com/pep_arilla/tb_idealista_watermark.git)
- Crear un entorno (python -m venv .venv) y activarlo (source .venv/bin/activate)
- pip install -r requeriments.txt

usage: 
tb_util_removemask_batch.py -i INPUT -o OUTPUT -m MASK [-a {telea,ns}] [-r RADIUS] [-w WATERMARK]

Por ejemplo:

>python tb_util_removemask_batch.py -i entrada -o salida -m idealista_mask_2000x2000.png

- Procesa todos los ficheros del directorio "entrada" (No incluye subdirectorios)
- Aplica la mascara "idealista_mask_2000x2000.png" para eliminar marca de agua
- Guarda los ficheros procesados en el directorio de "salida"
- Por defecto aplica la marca de agua "tb_wm_500.png" (se puede poner otra con el parametro -w, para crear una marca de agua basta con una imagen con fondo negro y la marca en blanco)





