/**
 * Utilidades asociadas al contexto de la aplicación
 * Permiten el uso de funcionalidades sobre, por ejemplo, la BBDD usando el contexto activo
 */

import * as pg from "pg";
import { withAppContext } from "./AppContext";
import { Db } from "agentor-lib";

/**
 * Si no se indica, obtiene un PoolClient y lo pasa como parámetro a la función
 * Si se indica, lo pasa diréctamente como parámetro a la función
 * @example 
 * withDbCli()( cli=> leerClientes(cli) );
 * 
 * @param cli 
 * @returns 
 */
export const
  withDbCli =
    <T>(fClient: Db.FClient<T>) => (cli?: pg.PoolClient) => withAppContext(({ db }) =>
      db.withClient(fClient)(cli)
    ),
  withDbTrx =
    <T>(fClient: Db.FClient<T>) => (trx?: pg.PoolClient) => withAppContext(({ db }) =>
      db.withTransaction(fClient)(trx)
    )
  ;