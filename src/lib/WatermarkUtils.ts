import { SpawnUtils } from "./SpawnUtils";
import path from "path";

//const c_idealista_mask_path = "./assets/idealista_mask.png";
export namespace WatermarkUtils {

	export async function removeIdealistaWM(src: string, dst: string): Promise<void> {
		await SpawnUtils.runCommand("python3", ["tb_util_remove_idealistawm.py", "-i", src, "-o", dst], { cwd: path.join(__dirname, "../py_src") });
	}
  export async function addTopbrokersWM(src: string, dst: string): Promise<void> {
		await SpawnUtils.runCommand("python3", ["tb_util_add_tbwm.py", "-i", src, "-o", dst], { cwd: path.join(__dirname, "../py_src") });
	}

	//	async function adjustMask(mask: cv.Mat, rows: number, cols: number): Promise<cv.Mat> {
	//		if (rows <= mask.rows && cols <= mask.cols) {
	//			let [x, y] = [(mask.cols - cols) / 2, (mask.rows - rows) / 2];
	//			return mask.getRegion(new cv.Rect(x, y, cols, rows));
	//		} else {
	//			return await copyCentered(mask, new cv.Mat(rows, cols, mask.type, 0));
	//		}
	//	}
	//
	//	/** 
	//	 * Copia la matriz src en dst centrada.
	//	 * Matriz debe ser de 1 sola capa.
	//	 */
	//	async function copyCentered(src: cv.Mat, dst: cv.Mat) {
	//		console.assert(src.rows <= dst.rows && src.cols <= dst.cols);
	//
	//		const
	//			xmin = Math.max(0, Math.floor((dst.cols - src.cols) / 2)),
	//			ymin = Math.max(0, Math.floor((dst.rows - src.rows) / 2));
	//
	//		let
	//			y = ymin - 1,
	//			x = xmin,
	//			next_ix = 0;
	//		(await src.getDataAsync()).
	//			forEach((value, ix) => {
	//				if (ix !== next_ix) {
	//					x++;
	//				} else {
	//					// Primera vez entra por aquí
	//					x = xmin;
	//					y++;
	//					next_ix += src.cols;
	//				}
	//				dst.set(y, x, value);
	//			});
	//
	//		return dst;
	//	}
	//	/*
	//	(async () => {
	//		try {
	//			await processImage("./tmp/entrada/p-14-1000x668.jpg", "./tmp/salida/original.jpeg");
	//		} catch (e) {
	//			console.log(e);
	//		}
	//	})();
	//	*/
}