import { spawn } from "child_process";
export namespace SpawnUtils {
	export async function runCommand(cmd: string, params: string[], options?: { cwd: string }): Promise<void> {
		//console.log("runCommand", { cmd, params, options });
		return new Promise((resolve, reject) => {
			const cpstream = spawn(cmd, params, options);
			cpstream.stdout.pipe(process.stdout);
			cpstream.stderr.pipe(process.stderr);
			cpstream.on("close", (code) => {
				if (code === 0) {
					resolve();
				} else {
					console.debug(`command ended with code ${code}`);
					reject(`command ended with code ${code}`);
				}
			});
		});
	}
}