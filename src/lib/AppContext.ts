import { Db, Storage, Geoloc, SmtpClient, ErrUtils, TmpFiles } from "agentor-lib";
import { TypedJsonUtils } from "lib/TypedJsonUtils";
import Ajv from "ajv";
import { PisoscomOfferMapper } from "controllers/tasks/publications/pisoscom/pisoscomOfferMapper";
import { InmofactoryAPI } from "external/adevinta/InmofactoryAPI";
import { CrwIdealistaAPI } from "external/crwidealista-api/CrwIdealistaAPI";
import { PisoscomAPI } from "external/pisoscom/PisoscomAPI";
import { createLogger, format, Logger, transports } from "winston";
import { IMessageBroker, MessagesBroker } from "./MessagesBroker";
import { Stripew } from './Stripew';
import { Tusolucionhipotecaria } from './Tusolucionhipotecaria';
import { env } from "process";
import { CSiteConfDTO } from "model/common/dtos/CsiteConfDTO";
import { HooksSrv, IHooksSrv } from "lib/HooksSrv";
const { doThrowError } = ErrUtils;


var _appContext: AppContext | undefined = void 0;

export declare type FAppContext<T> = (ctx: AppContext) => Promise<T>;
export class AppContext {
  public readonly environment: "production" | "devel";
  public readonly db: Db;
  public readonly ajv: Ajv;
  public readonly logger: Logger;
  /**
   * Sistema de almacenamiento empleado por topbrokers para imágenes, ...   se basa en S3
   * Equivale a storages.get("default");
   */
  public readonly storage: Storage;
  /**
   * Conjunto de sistemas de almacenamiento empleados con diferentes fines por la aplicación.
   * Se cumple que this.storate === this.storages.get("default") 
   */
  public readonly storages: Map<string, Storage>;
  public readonly email: SmtpClient;
  public readonly geoloc: Geoloc.Geoloc;
  public readonly tmpfiles: TmpFiles;
  public readonly stripeWrapper: Stripew;
  public readonly tusolucionhipotecaria: Tusolucionhipotecaria.ITSHAPI;
  public readonly messagesbroker: IMessageBroker;
  public readonly hooksSrv: IHooksSrv;
  /**
   * API del Crawler idealista: acceso a los anuncios arañados de idealista y el esquema de zonas.
   */
  public readonly crwidealista_api: CrwIdealistaAPI

  /**
   * 
   */
  public readonly crwindividuals_api: CrwIdealistaAPI
  /**
   * API del Crawler de nauncios de Haya:  Acceso a los anuncios arañados de idealista referidos a Haya
   */
  public readonly crwhaya_api: CrwIdealistaAPI
  /**
  * API del Crawler de nauncios de Servihabitat:  Acceso a los anuncios arañados de idealista referidos a Servihabitat
  */
  public readonly crwservihabitat_api: CrwIdealistaAPI
  /**
  * API del Crawler de nauncios de Solvia:  Acceso a los anuncios arañados de idealista referidos a Solvia
  */
  public readonly crwsolvia_api: CrwIdealistaAPI
  /**
  * API del Crawler de nauncios de Unicaja:  Acceso a los anuncios arañados de idealista referidos a Unicaja
  */
  public readonly crwunicaja_api: CrwIdealistaAPI
  /**
  * API del Crawler de nauncios de Portalnow:  Acceso a los anuncios arañados de idealista referidos a Portalnow
  */
  public readonly crwportalnow_api: CrwIdealistaAPI
  /**
  * API del Crawler de nauncios de Aliseda:  Acceso a los anuncios arañados de idealista referidos a Aliseda
  */
  public readonly crwaliseda_api: CrwIdealistaAPI
  /**
    * API del Crawler de nauncios de Uci:  Acceso a los anuncios arañados importados del xml de UCI
    */
  public readonly crwuci_api: CrwIdealistaAPI
  /**
   * Configuración específica de csites
   */
  public readonly csite_conf: CSiteConfDTO



  constructor(config: {
    db: any,
    crwidealista_api: any,
    crwhaya_api: any,
    crwunicaja_api: any,
    crwindividuals_api: any,
    crwservihabitat_api: any,
    crwsolvia_api: any,
    crwportalnow_api: any,
    crwaliseda_api: any,
    crwuci_api: any,
    geoloc: any,
    storages: any[],
    email: any,
    stripe: any,
    tusolucionhipotecaria: any,
    messagesbroker: any,
    csite: any,
    logger: { level: undefined | 'info' | 'debug' },
  }) {

    this.environment = (process.env.NODE_ENV === "production") ? "production" : "devel";

    this.logger = createLogger({
      transports: [new transports.Console({ level: config.logger?.level ?? 'info' })],
      level: 'info',
      format: format.combine(
        format.timestamp(),
        format.printf(({ timestamp, level, message }) => `[${timestamp}] ${level}: ${message}`)
      )
    });
    this.ajv = new Ajv();
    this.db = new Db(TypedJsonUtils.parse(config.db, Db.Options));
    this.crwidealista_api = new CrwIdealistaAPI(TypedJsonUtils.parse(config.crwidealista_api, CrwIdealistaAPI.Options));//, config.crwidealista_api.mq?.url ?? throwUndefinedError("crwidealista_api.mq.url"));
    this.crwhaya_api = new CrwIdealistaAPI(TypedJsonUtils.parse(config.crwhaya_api, CrwIdealistaAPI.Options));
    this.crwindividuals_api = new CrwIdealistaAPI(TypedJsonUtils.parse({
      advertiser_id: "eff69e49-f218-40a1-bb86-5dc189ca4f5e",
      ...config.crwindividuals_api
    }, CrwIdealistaAPI.Options));
    this.crwservihabitat_api = new CrwIdealistaAPI(TypedJsonUtils.parse(config.crwservihabitat_api, CrwIdealistaAPI.Options));
    this.crwsolvia_api = new CrwIdealistaAPI(TypedJsonUtils.parse(config.crwsolvia_api, CrwIdealistaAPI.Options));
    this.crwunicaja_api = new CrwIdealistaAPI(TypedJsonUtils.parse(config.crwunicaja_api, CrwIdealistaAPI.Options));
    this.crwportalnow_api = new CrwIdealistaAPI(TypedJsonUtils.parse(config.crwportalnow_api, CrwIdealistaAPI.Options));
    this.crwaliseda_api = new CrwIdealistaAPI(TypedJsonUtils.parse(config.crwaliseda_api, CrwIdealistaAPI.Options));
    this.crwuci_api = new CrwIdealistaAPI(TypedJsonUtils.parse(config.crwuci_api, CrwIdealistaAPI.Options));
    
    const storages = new Map(config.storages.map(conf => [conf["name"] ?? "default", new Storage(TypedJsonUtils.parse(conf, Storage.Options))]));
    this.storages = storages;
    this.storage = storages.get("default") ?? doThrowError("Missing default storage configuration");
    this.tmpfiles = new TmpFiles();
    this.geoloc = new Geoloc.Geoloc(TypedJsonUtils.parse(config.geoloc, Geoloc.Options));
    this.email = new SmtpClient(TypedJsonUtils.parse(config.email, SmtpClient.Options));
    this.stripeWrapper = new Stripew(config.stripe);
    this.tusolucionhipotecaria = Tusolucionhipotecaria.TSHAPI(TypedJsonUtils.parse(config.tusolucionhipotecaria, Tusolucionhipotecaria.TSHAPIOptions));
    this.messagesbroker = MessagesBroker(config.messagesbroker);
    this.hooksSrv = HooksSrv(this.logger);
    this.csite_conf = TypedJsonUtils.parse(config.csite, CSiteConfDTO);

  }

  public async dispose(): Promise<void> {
    appContext().hooksSrv.dispose();
    await appContext().messagesbroker.close();
  }

}

export async function withAppContext<T>(f: FAppContext<T>) {
  return f(appContext());
}
export function appContext(): AppContext {
  if (_appContext === void 0)
    throw new Error("appContext must be stablished before calling appContext global method")
  return _appContext;
}
export function setAppContext(ctx: AppContext): AppContext {
  _appContext = ctx;
  return ctx;
}



