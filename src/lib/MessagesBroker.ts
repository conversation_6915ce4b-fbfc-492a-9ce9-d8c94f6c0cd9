//require('dotenv').config();
import { ErrUtils } from 'agentor-lib';
import * as amqp from 'amqplib';
import { ConsumeMessage } from 'amqplib';
import { bool } from 'aws-sdk/clients/signer';
import { once, pull } from 'lodash';
import nanoid from "nanoid";
const { doThrowError } = ErrUtils;

export type SubscriptionHandler = (msg: ConsumeMessage | null, ack: () => void) => Promise<void>;

declare type ConsumerInfo = {
  consumerTag: string,
  handlers: SubscriptionHandler[],
  active: bool,
}

export declare type IMessageBroker = {
  close: () => Promise<void>,
  publish: (exchange: string, routingKey: string, content: Buffer, options?: amqp.Options.Publish) => Promise<void>,
  subscribe: (queueName: string, handler: SubscriptionHandler) => Promise<() => void>,
  enableSubscriptions: () => Promise<void>
};

export function MessagesBroker(options: { url: string }): IMessageBroker {
  let url = options.url;
  let mq: IMessageBroker | undefined;

  return {
    publish, subscribe, enableSubscriptions, close
  };

  async function close() {
    if (mq) { await mq.close(); mq = void 0; }
  }
  async function publish(exchange: string, routingKey: string, content: Buffer, options?: amqp.Options.Publish) {
    if (!mq)
      mq = await MessagesBrokerImp(url);
    await mq!.publish(exchange, routingKey, content, options);
  }
  async function subscribe(queueName: string, handler: SubscriptionHandler): Promise<() => void> {
    if (!mq)
      mq = await MessagesBrokerImp(url);
    return mq!.subscribe(queueName, handler);
  }
  async function enableSubscriptions() {
    if (!mq)
      mq = await MessagesBrokerImp(url);
    mq!.enableSubscriptions();
  }

}



async function MessagesBrokerImp(url: string) {
  try {
    const connection = await amqp.connect(url);
    /**
     * Rabbit MQ communications channel
     */
    const channel = await connection.createChannel();

    // Add error handlers
    connection.on('error', (err) => {
      console.error('RabbitMQ connection error:', err);
    });

    connection.on('close', () => {
      console.log('RabbitMQ connection closed');
    });

    channel.on('error', (err) => {
      console.error('RabbitMQ channel error:', err);
    });
    /**
     * We will only process one message at the same time
     */
    channel.prefetch(4);

    /**
     * Registro de todos los consumidores asociados a las colas
     */
    var consumersInfo = new Map<string, ConsumerInfo>();

    async function close() {
      for (const consumerInfo of consumersInfo.values())
        try { await channel.cancel(consumerInfo.consumerTag); } catch (e) { console.error(`Problems canceling consumer ${consumerInfo.consumerTag}`, e); }
      try { await channel.close(); } catch (e) { console.error("Problems clossing amqp channel", e); }
      try { await connection.close(); } catch (e) { console.error("Problems clossing amqp connection", e); }
    }


    /**
     * Send message to an exchange
     * @param {Object} - object defining exchange and routingKey
     * @param {Object} msg Message as Buffer
     */
    async function publish(exchange: string, routingKey: string, content: Buffer, options?: amqp.Options.Publish) {
      await channel.assertExchange(exchange, "direct", { durable: true });
      channel.publish(exchange, routingKey, content, options)
    }

    /**
     * Registra un subscriptor a la cola de mensajes, no se conecta a la cola real hasta que no se evoque enableSubscriptions();
     * @param {Object} - object defining queue name and bindingKey
     * @param {Function} handler Handler that will be invoked with given message and acknowledge function (msg, ack)
     */
    async function preSubscribe(queueName: string, handler: SubscriptionHandler): Promise<() => void> {
      queueName = queueName.toLowerCase();
      if (!consumersInfo.has(queueName))
        consumersInfo.set(queueName, { consumerTag: nanoid.nanoid(), handlers: [], active: false });

      let consumerInfo = consumersInfo.get(queueName) ?? doThrowError(`Unexpected:  can't find ${queueName} consummers info`);
      // La cola está lista, ahora sí podemos tratar con la subscripción
      let existingHandler = consumerInfo.handlers.find(existing => existing === handler);
      if (!existingHandler)
        consumerInfo.handlers.push(handler);
      //
      return () => unsubscribe(queueName, handler);
    }

    /**
     * Inicia el consumo de colas que se hayan preparado con "preSubscribe"
     */
    async function enableSubscriptions() {

      for (const [queueName, consumerInfo] of consumersInfo.entries()) {
        if (!consumerInfo.active) {
          await channel.assertQueue(queueName, { durable: true });
          await channel.consume(queueName, messageHandler(queueName), { consumerTag: consumerInfo.consumerTag });
          consumerInfo.active = true;
        }
      }

    function messageHandler(queueName: string) {
      return async (msg: amqp.ConsumeMessage | null) => {
        if (msg !== null) {

          let acknowledged = false;
          const ack = once(() => { 
            channel.ack(msg); 
            acknowledged = true; 
          });

          // Pedimos a todos loshandlers registrados a la cola que procesen el mensaje.
          const handlers = consumersInfo.get(queueName)?.handlers ?? [];
          for (const handler of handlers)
            try { await handler(msg, ack); } catch (e) {
              console.log("Error processing message", e);
            }

          // Si nadie ha enviado un ack, rechazamos el mensaje (nack);
          if (!acknowledged)
            channel.nack(msg);

        }
      };
      }
    }

    /**
     * Elimina un handler consumidor de una cola.
     * Si la cola se queda sin handlers, se cancela el consumo de mensajes de dicha cola
     * @param queueName
     * @param handler
     */
    async function unsubscribe(queueName: string, handler: SubscriptionHandler) {
      let queueInfo = consumersInfo.get(queueName);
      if (queueInfo) {
        pull(queueInfo.handlers, handler);
        if (queueInfo.handlers.length === 0) {
          if (queueInfo.active)
            await channel.cancel(queueInfo.consumerTag);
          consumersInfo.delete(queueName);
        }
      }
    }

    return {
      close, publish, subscribe: preSubscribe, enableSubscriptions
    } as IMessageBroker;
  } catch (error: any) {
    console.error('Failed to connect to RabbitMQ:', error.message);
    throw new Error(`RabbitMQ connection failed: ${error.message}`);
  }
}