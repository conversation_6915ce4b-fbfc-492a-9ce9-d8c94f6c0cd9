import { Request } from 'express';
import Stripe from 'stripe';

declare type StripewOptions = {
  publishable_key: string,
  secret_key: string,
  webhook_secret: string,
  prices: {
    code: string,
    priceId: string,
  }[]

};

export class Stripew {
  private stripe: Stripe;
  private options: StripewOptions;
  constructor(options: StripewOptions) {
    this.stripe = new Stripe(
      options.secret_key,
      { apiVersion: "2020-08-27" }
    );
    this.options = options;
  }

  extractWebhookEvent(req: Request): Stripe.Event {
    const sig = req.headers['stripe-signature'] ?? "";
    return this.stripe.webhooks.constructEvent(req.body, sig, this.options.webhook_secret); //, sig, webhookSecret);
  }
  getPublishableKey() {
    return this.options.publishable_key;
  }
  /**
   * Crea una sesión de checkout para comprar el producto indicado
   * @param priceID Identificador del precio en stripe 
   * @param success_url 
   * @param cancel_url 
   * @returns 
   */
  async createCheckout(priceCode: string, success_url: string, cancel_url: string): Promise<Stripe.Checkout.Session> {
    const price = this.options.prices.find(p => p.code === priceCode);
    if (!price)
      throw new Error(`Unknown priceCode ${priceCode}`);
    else {
      const response = await this.stripe.checkout.sessions.create(
        {
          payment_method_types: ["card"],
          line_items: [
            {
              price: price.priceId,
              quantity: 1,
            }
          ],
          mode: "payment",
          success_url,
          cancel_url
        }
      );
      if (response.lastResponse.statusCode !== 200)
        throw new Error(`Unexpected Stripe response StatusCode ${response.lastResponse.statusCode} RequestId:${response.lastResponse.requestId}`);
      else
        return response as Stripe.Checkout.Session;
    }

  }
  async readPaymentIntent(id: string): Promise<Stripe.PaymentIntent> {
    const response = await this.stripe.paymentIntents.retrieve(id);
    if (response.lastResponse.statusCode !== 200) {
      throw new Error(`Unexpected Stripe response StatusCode ${response.lastResponse.statusCode} RequestId:${response.lastResponse.requestId}`);
    } else {
      return response as Stripe.PaymentIntent;
    }
  }
}