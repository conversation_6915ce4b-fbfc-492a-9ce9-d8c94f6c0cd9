{"name": "agentor", "version": "1.1.06", "description": "", "main": "index.ts", "scripts": {"csite_generateopportunities": "ts-node -r tsconfig-paths/register src/tasks.ts --csite_generateopportunities", "sync_crwidealista": "ts-node -r tsconfig-paths/register src/tasks.ts --sync_crwidealista", "sync_crwhaya": "ts-node -r tsconfig-paths/register src/tasks.ts --sync_crwhaya", "sync_crwservihabitat": "ts-node -r tsconfig-paths/register src/tasks.ts --sync_crwservihabitat", "sync_crwsolvia": "ts-node -r tsconfig-paths/register src/tasks.ts --sync_crwsolvia", "sync_crwunicaja": "ts-node -r tsconfig-paths/register src/tasks.ts --sync_crwunicaja", "sync_crwportalnow": "ts-node -r tsconfig-paths/register src/tasks.ts --sync_crwportalnow", "sync_crwaliseda": "ts-node -r tsconfig-paths/register src/tasks.ts --sync_crwaliseda", "sync_crwindividuals": "ts-node -r tsconfig-paths/register src/tasks.ts --sync_crwindividuals", "sync_crwuci": "ts-node -r tsconfig-paths/register src/tasks.ts --sync_crwuci", "tasks": "ts-node -r tsconfig-paths/register src/tasks.ts", "build": "npm run clean && tsc && cp basecontent/* build && npm run cp_json_to_build && npm run cp_pysrc_to_build &&  cp -rf public build/public && cp package.json build", "start": "ts-node -r tsconfig-paths/register src/index.ts", "clean": "rm -rf ./build", "cp_json_to_build": "rsync -a -m --include '*/' --include '*.json' --include '*.png' --exclude '*' ./src/controllers/api/schemas/ ./build/controllers/api/schemas/", "cp_pysrc_to_build": "rsync -a -m ./src/py_src/ ./build/py_src/", "tests": "ts-node tests/generate_all_tests_imports.ts &&  ts-node -r tsconfig-paths/register tests/tests_runner.ts"}, "author": "", "license": "ISC", "dependencies": {"agentor-lib": "file:../agentor-lib", "ajv": "^8.11.0", "ajv-formats": "^2.1.1", "amqplib": "^0.10.7", "aws-sdk": "^2.1046.0", "axios": "^1.5.1", "compression": "^1.7.4", "connect-multiparty": "^2.2.0", "console-stamp": "^3.0.0-rc4.3", "cookie-session": "^1.4.0", "cors": "^2.8.5", "express": "^4.17.1", "express-validator": "^6.14.0", "got": "^11.8.1", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.21", "mime": "^2.4.6", "minimist": "^1.2.6", "multer": "^1.4.5-lts.1", "nanoid": "^3.3.1", "nodemailer": "^6.4.18", "remove": "^0.1.5", "sharp": "^0.30.2", "stripe": "^8.139.0", "tsconfig-paths": "^3.12.0", "typedjson": "^1.8.0", "winston": "^3.6.0"}, "devDependencies": {"@types/amqplib": "^0.10.7", "@types/bluebird": "^3.5.36", "@types/compression": "^1.7.0", "@types/cors": "^2.8.7", "@types/express": "^4.17.7", "@types/jsonwebtoken": "^8.5.0", "@types/lodash": "^4.14.161", "@types/mime": "^2.0.3", "@types/multer": "^1.4.4", "@types/nanoid": "^2.1.0", "@types/node": "^14.6.2", "@types/nodemailer": "^6.4.0", "@types/pg": "^8.11.3", "@types/sharp": "^0.30.2", "ts-node": "^10.8.0", "typescript": "^4.9.5"}}