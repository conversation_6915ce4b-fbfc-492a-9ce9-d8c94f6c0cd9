* Log
-- nueva tabla/entidad para registrar sucesos en la BBDD (ej: paso a histórico, despublicar oferta, ...) y sus causas.
-- útil para mostrarlos al usuario (o hacer seguimiento interno).
* Eliminar compartición 
--  En la venta marcar:
--     <PERSON><PERSON> fin servidcio
//--     Causa fin servicio: "offer_unpublished"
* Paso a histórico de inmueble
--  Eliminar la compartición
--  En la venta marcar:
--     Fecha fin servicio
//--     Causa fin servicio:  "offer_historic"
* Renovación de publicaciones activas
--  Para toda workgroup_offer con un purchase asociado, buscar si tiene algún pago pendiente.
--  Para todo pago pendiente.
--    Crear una transacción + pago.  El pago debe tener <fecha de valor> el de la venta + N * 24 horas (siempre en hora GTM).
--  Si no hay fondos suficientes para el pago
--    Eliminar compartición.
--    <PERSON>ar el "fin de servicio" en la venta:
--      <PERSON>cha fin de servicio: ahora.
--      Causa fin servicio: "payment_insuficient_funds"
